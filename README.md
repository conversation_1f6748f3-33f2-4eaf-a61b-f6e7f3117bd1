# YouTube Analysis Demo

## 项目概述

这是一个基于Spring Boot的YouTube视频分析应用，提供视频字幕分析、翻译、语音合成等功能。

## 技术栈

- **后端**: Spring Boot 3.x, Spring Security, Spring Data JPA
- **数据库**: H2 Database (嵌入式)
- **前端**: 原生JavaScript, HTML5, CSS3
- **浏览器扩展**: Chrome Extension
- **AI服务**: 阿里云通义千问, Azure翻译服务

## 核心功能模块

### 1. 用户认证与授权
- Google OAuth2 登录
- 用户会话管理
- 权限控制

### 2. 视频字幕处理
- 字幕下载与解析
- 多语言字幕支持
- 字幕清理与格式化

### 3. AI 分析服务
- 文本分析与总结
- 多语言翻译（DashScope API + Azure备用）
- 批量字幕翻译（支持多线程并行处理）
- 语音合成(TTS)
- 视频转录（Groq API + Whisper模型）

### 4. 学习内容管理
- 收藏夹功能
- 学习进度跟踪
- 个人笔记

### 5. 数据分析
- 用户行为统计
- 学习效果分析
- 使用情况报表

## 日志管理配置

### 日志策略
- **保留时间**: 30天自动清理
- **文件大小**: 单文件最大10MB
- **轮转方式**: 按天轮转 + 大小限制
- **压缩存储**: 历史日志自动压缩(.gz)

### 日志分类
- `logs/app.log` - 应用主日志(INFO及以上)
- `logs/error.log` - 错误日志(ERROR级别)
- `logs/debug.log` - 调试日志(DEBUG级别，保留7天)

### 日志级别配置
- 应用包(`org.example.youtubeanalysisdemo`): DEBUG
- Spring框架: INFO
- Hibernate: WARN
- SQL查询: DEBUG(开发环境)

### 环境特定配置
- **开发环境**: 详细控制台输出 + 文件日志
- **生产环境**: 主要文件日志，减少控制台输出

## 项目结构

```
src/
├── main/
│   ├── java/org/example/youtubeanalysisdemo/
│   │   ├── config/          # 配置类
│   │   ├── controller/      # REST控制器
│   │   ├── service/         # 业务逻辑层
│   │   ├── repository/      # 数据访问层
│   │   ├── entity/          # 实体类
│   │   ├── dto/             # 数据传输对象
│   │   ├── model/           # 数据模型
│   │   ├── strategy/        # 策略模式实现
│   │   └── util/            # 工具类
│   └── resources/
│       ├── static/          # 静态资源
│       ├── application.properties
│       └── logback-spring.xml
├── chrome-extension-demo/   # Chrome扩展
└── logs/                    # 日志文件目录
```

## 快速开始

### 环境要求
- Java 17+
- Maven 3.6+
- Chrome浏览器(扩展功能)

### 运行步骤
1. 克隆项目
```bash
git clone <repository-url>
cd youtube-analysis-demo
```

2. 配置环境变量
```bash
# 复制并编辑配置文件
cp src/main/resources/application.properties.example src/main/resources/application.properties
```

3. 启动应用
```bash
mvn spring-boot:run
```

4. 访问应用
- 主页: http://localhost:8080
- H2控制台: http://localhost:8080/h2-console

### Chrome扩展安装
1. 打开Chrome扩展管理页面
2. 启用开发者模式
3. 加载`chrome-extension-demo`目录

## API文档

### 认证相关
- `GET /api/auth/user` - 获取当前用户信息
- `POST /api/auth/logout` - 用户登出

### 字幕处理
- `POST /api/subtitles/download` - 下载字幕
- `GET /api/subtitles/{videoId}` - 获取字幕内容
- `POST /api/subtitles/translate` - 翻译字幕（使用DashScope API）
- `POST /api/subtitles/translate-azure` - 翻译字幕（使用Azure API备用）

### 学习管理
- `GET /api/saved-content` - 获取收藏内容
- `POST /api/saved-content` - 添加收藏
- `DELETE /api/saved-content/{id}` - 删除收藏

## 配置说明

### 数据库配置
- 使用H2嵌入式数据库
- 数据文件位置: `./data/appdb`
- 开发环境可通过H2控制台访问

### 安全配置
- 集成Spring Security
- 支持Google OAuth2登录
- 会话超时: 7天

### 外部服务配置
- Mistral AI（视频摘要）
- 阿里云通义千问API（DashScope）
- Azure翻译服务（备用）
- Paddle支付集成

### 视频摘要功能优化
- **Mistral AI集成**: 使用Spring AI框架集成Mistral AI的mistral-small-latest模型
- **现代化架构**: 采用Spring AI的ChatModel接口，提供更好的抽象和扩展性
- **Premium会员权限**: 视频摘要功能仅限Premium会员使用
- **智能摘要**: 生成200-300字的简洁摘要，突出视频核心内容和观点
- **中文字幕优先**: 优先选择中文字幕文件(zh-Hans/zh-Hant)进行摘要生成
- **中文输出**: 强制使用中文输出摘要，确保用户体验一致性
- **错误处理**: 完善的异常处理机制，确保服务稳定性
- **性能优化**: 自动截断过长文本，避免超出API限制

### 字幕翻译优化功能
- **批量翻译**: 将字幕按批次处理，减少API调用次数
- **多线程并行**: 支持多个批次同时翻译，大幅提升翻译速度
- **智能分段**: 自动将长字幕文件分割为合适的批次大小
- **翻译质量**: 使用阿里云通义千问模型，翻译质量显著提升
- **格式保持**: 完整保持VTT字幕文件的时间戳和格式结构
- **错误处理**: 单个批次失败不影响整体翻译进度
- **API限流**: 内置请求间隔控制，避免触发API限流

### 视频转录功能优化
- **Groq API集成**: 使用Groq的Whisper模型进行高速转录
- **无需排队**: 移除了原有的排队机制，转录请求立即处理
- **多语言支持**: 支持日语、英语、中文、韩语等多种语言转录
- **VTT格式**: 自动生成标准VTT字幕文件，包含精确时间戳
- **错误处理**: 完善的错误处理和重试机制
- **资源管理**: 自动清理临时音频文件，避免磁盘空间浪费

## 字幕验证和重试机制

### 问题描述
在某些情况下，yt-dlp下载的字幕文件可能只包含时间戳而没有实际的字幕内容，导致前端显示"字幕已加载但内容为空"的问题。

### 解决方案
1. **增强字幕文件验证**：
   - 不仅检查VTT格式和时间戳
   - 验证字幕文件是否包含实际的文本内容
   - 至少需要5行有效字幕内容才认为文件有效

2. **自动重试机制**：
   - 当检测到字幕文件无效时，自动重试下载
   - 最多重试3次，每次间隔固定1秒（快速重试，节省用户时间）
   - 依次尝试用户上传字幕→自动生成字幕→自动翻译字幕

3. **前端错误处理**：
   - 提供详细的错误信息
   - 添加重试按钮，允许用户手动重试
   - 区分不同类型的加载失败情况

4. **无效文件清理**：
   - 自动清理只有时间戳没有内容的字幕文件
   - 在每次字幕获取前执行清理操作

### 技术实现
- `SubtitleFileService.hasActualSubtitleContent()`: 检查字幕实际内容
- `SubtitleDownloadService.downloadLanguageSubtitle()`: 重试下载机制
- `SubtitleCore.retrySubtitleLoad()`: 前端重试功能
- `SubtitleFileService.cleanupInvalidSubtitles()`: 清理无效文件

### 使用效果
- 大幅提高字幕下载成功率
- 减少空字幕文件的产生
- 提供更好的用户体验和错误提示

## 开发规范

### 代码结构
- 遵循Spring Boot最佳实践
- 使用分层架构(Controller-Service-Repository)
- 实现策略模式处理多语言

### 日志规范
- 使用SLF4J进行日志记录
- 错误日志包含完整堆栈信息
- 关键业务操作记录INFO级别日志

### 安全要求
- 所有API接口需要认证
- 输入数据验证
- XSS和CSRF防护

## 部署说明

### 生产环境配置
1. 设置环境变量`spring.profiles.active=prod`
2. 配置外部数据库连接
3. 设置SSL证书
4. 配置反向代理

### 监控与维护
- 定期检查日志文件
- 监控应用性能指标
- 备份数据库文件

## 贡献指南
1. Fork项目
2. 创建功能分支
3. 提交代码变更
4. 发起Pull Request

## 许可证
本项目采用MIT许可证。 