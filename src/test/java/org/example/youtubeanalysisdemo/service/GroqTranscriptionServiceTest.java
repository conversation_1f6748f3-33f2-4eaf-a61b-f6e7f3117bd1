package org.example.youtubeanalysisdemo.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Groq转录服务测试
 */
@SpringBootTest
@TestPropertySource(properties = {
    "groq.api.key=test-key",
    "subtitles.save.path=test-subtitles"
})
public class GroqTranscriptionServiceTest {

    private GroqTranscriptionService groqTranscriptionService;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        groqTranscriptionService = new GroqTranscriptionService();
        objectMapper = new ObjectMapper();
    }

    @Test
    void testFormatTime() throws Exception {
        // 使用反射测试私有方法formatTime
        java.lang.reflect.Method formatTimeMethod = GroqTranscriptionService.class
                .getDeclaredMethod("formatTime", double.class);
        formatTimeMethod.setAccessible(true);
        
        // 测试不同的时间格式
        String result1 = (String) formatTimeMethod.invoke(groqTranscriptionService, 0.0);
        assertEquals("00:00:00.000", result1);
        
        String result2 = (String) formatTimeMethod.invoke(groqTranscriptionService, 65.5);
        assertEquals("00:01:05.500", result2);
        
        String result3 = (String) formatTimeMethod.invoke(groqTranscriptionService, 3661.123);
        assertEquals("01:01:01.123", result3);
    }

    @Test
    void testMapLanguageCode() throws Exception {
        // 使用反射测试私有方法mapLanguageCode
        java.lang.reflect.Method mapLanguageCodeMethod = GroqTranscriptionService.class
                .getDeclaredMethod("mapLanguageCode", String.class);
        mapLanguageCodeMethod.setAccessible(true);
        
        // 测试语言代码映射
        assertEquals("ja", mapLanguageCodeMethod.invoke(groqTranscriptionService, "ja"));
        assertEquals("ja", mapLanguageCodeMethod.invoke(groqTranscriptionService, "japanese"));
        assertEquals("en", mapLanguageCodeMethod.invoke(groqTranscriptionService, "en"));
        assertEquals("en", mapLanguageCodeMethod.invoke(groqTranscriptionService, "english"));
        assertEquals("zh", mapLanguageCodeMethod.invoke(groqTranscriptionService, "zh"));
        assertEquals("zh", mapLanguageCodeMethod.invoke(groqTranscriptionService, "chinese"));
        assertEquals("en", mapLanguageCodeMethod.invoke(groqTranscriptionService, "unknown"));
    }

    @Test
    void testGroqResponseParsing() {
        // 测试Groq API响应解析
        String mockResponse = """
            {
                "text": "みなさんこんにちは ここは築地です",
                "segments": [
                    {
                        "id": 0,
                        "start": 0.0,
                        "end": 6.0,
                        "text": "みなさんこんにちは ここは築地です"
                    },
                    {
                        "id": 1,
                        "start": 6.0,
                        "end": 15.98,
                        "text": "今からこのあたりの築地城外市場というところに向かいます"
                    }
                ]
            }
            """;
        
        try {
            // 验证JSON格式正确性
            objectMapper.readTree(mockResponse);
            assertTrue(true, "Mock response is valid JSON");
        } catch (Exception e) {
            fail("Mock response should be valid JSON: " + e.getMessage());
        }
    }
} 