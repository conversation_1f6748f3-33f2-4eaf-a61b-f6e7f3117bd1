package org.example.youtubeanalysisdemo.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

@Entity
@Table(name = "subscriptions")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Subscription {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String userId;
    
    @Column(nullable = false)
    private String paddleSubscriptionId;
    
    @Column(nullable = false)
    private String status; // active(当前生效), pending(等待生效), expired(已过期)
    
    @Column(nullable = false)
    private String planId;
    
    @Column(nullable = false)
    private String planName;
    
    @Column(nullable = true)
    private String description;
    
    @Column(nullable = true)
    private LocalDateTime startDate; // 订阅开始日期
    
    @Column(nullable = true)
    private LocalDateTime nextBillDate; // 订阅结束日期（也是下一个计费周期开始时间）
    
    @Column(nullable = false)
    private LocalDateTime createdAt;
    
    @Column(nullable = false)
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
} 