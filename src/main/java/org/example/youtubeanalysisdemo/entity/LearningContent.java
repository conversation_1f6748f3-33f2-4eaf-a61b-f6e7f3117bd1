package org.example.youtubeanalysisdemo.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

/**
 * 学习内容实体类 - 存储单词和语法的内容
 * 优化数据库结构，避免内容冗余存储
 */
@Entity
@Table(name = "learning_contents", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"content_key"})
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LearningContent {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 内容唯一标识，由word和type组合生成
     * 用于快速查找是否已存在相同内容
     */
    @Column(name = "content_key", nullable = false, unique = true)
    private String contentKey;
    
    /**
     * 单词或语法的名称
     */
    @Column(name = "word", nullable = false)
    private String word;
    
    /**
     * 单词或语法的详细解析
     */
    @Column(name = "analysis", columnDefinition = "TEXT", nullable = false)
    private String analysis;
    
    /**
     * 内容类型：word(单词) 或 grammar(语法)
     */
    @Column(name = "type", nullable = false)
    private String type;
    
    /**
     * 语言类型：japanese(日语) 或 english(英语)
     */
    @Column(name = "lang", nullable = false, length = 20)
    private String lang = "japanese";
    
    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    /**
     * 创建时自动设置时间
     */
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        // 如果lang为空，默认设置为japanese以保持向后兼容性
        if (lang == null || lang.isEmpty()) {
            lang = "japanese";
        }
    }
    
    /**
     * 根据单词、类型和语言生成唯一标识
     * @param word 单词或语法
     * @param type 类型
     * @param lang 语言
     * @return 唯一标识
     */
    public static String generateContentKey(String word, String type, String lang) {
        return lang + ":" + type + ":" + word;
    }
    
    /**
     * 根据单词和类型生成唯一标识（向后兼容）
     * @param word 单词或语法
     * @param type 类型
     * @return 唯一标识
     */
    public static String generateContentKey(String word, String type) {
        return generateContentKey(word, type, "japanese");
    }
}
