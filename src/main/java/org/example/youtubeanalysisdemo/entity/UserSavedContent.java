package org.example.youtubeanalysisdemo.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

/**
 * 用户收藏关系实体类
 * 记录用户与学习内容之间的关系，包括学习状态
 */
@Entity
@Table(name = "user_saved_contents", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"user_id", "content_id"})
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserSavedContent {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private String userId;
    
    /**
     * 关联到LearningContent的ID
     */
    @Column(name = "content_id", nullable = false)
    private Long contentId;
    
    /**
     * 学习状态：0-未学会，1-已学会
     */
    @Column(name = "learned", nullable = false)
    private Integer learned = 0;
    
    /**
     * 收藏时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    /**
     * 创建时自动设置时间
     */
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}
