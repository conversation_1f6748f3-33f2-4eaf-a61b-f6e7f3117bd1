package org.example.youtubeanalysisdemo.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 弹幕评论实体类
 * 用于存储用户在观看视频时发送的弹幕数据
 */
@Entity
@Table(name = "danmaku_comments", indexes = {
    @Index(name = "idx_video_timestamp", columnList = "videoId, videoTimestamp"),
    @Index(name = "idx_create_time", columnList = "createTime")
})
public class DanmakuComment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * YouTube视频ID
     */
    @Column(nullable = false, length = 50)
    private String videoId;
    
    /**
     * 弹幕内容
     */
    @Column(nullable = false, length = 500)
    private String content;
    
    /**
     * 视频播放时间戳（秒）
     */
    @Column(nullable = false)
    private Double videoTimestamp;
    
    /**
     * 发送弹幕的用户ID
     */
    @Column(nullable = false)
    private Long userId;
    
    /**
     * 弹幕颜色（十六进制，如 #FFFFFF）
     */
    @Column(length = 7, columnDefinition = "VARCHAR(7) DEFAULT '#FFFFFF'")
    private String color = "#FFFFFF";
    
    /**
     * 弹幕类型：1-滚动弹幕，2-顶部弹幕，3-底部弹幕
     */
    @Column
    private Integer type = 1;
    
    /**
     * 弹幕创建时间
     */
    @Column(nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 弹幕状态：1-正常，0-已删除
     */
    @Column
    private Integer status = 1;
    
    // 构造函数
    public DanmakuComment() {}
    
    public DanmakuComment(String videoId, String content, Double videoTimestamp, Long userId) {
        this.videoId = videoId;
        this.content = content;
        this.videoTimestamp = videoTimestamp;
        this.userId = userId;
        this.createTime = LocalDateTime.now();
    }
    
    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getVideoId() {
        return videoId;
    }
    
    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public Double getVideoTimestamp() {
        return videoTimestamp;
    }
    
    public void setVideoTimestamp(Double videoTimestamp) {
        this.videoTimestamp = videoTimestamp;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
    
    public Integer getType() {
        return type;
    }
    
    public void setType(Integer type) {
        this.type = type;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    @PrePersist
    protected void onCreate() {
        if (createTime == null) {
            createTime = LocalDateTime.now();
        }
    }
    
    @Override
    public String toString() {
        return "DanmakuComment{" +
                "id=" + id +
                ", videoId='" + videoId + '\'' +
                ", content='" + content + '\'' +
                ", videoTimestamp=" + videoTimestamp +
                ", userId=" + userId +
                ", color='" + color + '\'' +
                ", type=" + type +
                ", createTime=" + createTime +
                ", status=" + status +
                '}';
    }
} 