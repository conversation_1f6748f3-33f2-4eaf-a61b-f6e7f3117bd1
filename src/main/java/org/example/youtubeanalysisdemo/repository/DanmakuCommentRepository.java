package org.example.youtubeanalysisdemo.repository;

import org.example.youtubeanalysisdemo.entity.DanmakuComment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 弹幕评论数据访问接口
 */
@Repository
public interface DanmakuCommentRepository extends JpaRepository<DanmakuComment, Long> {
    
    /**
     * 根据视频ID查询弹幕，按时间戳排序
     * @param videoId 视频ID
     * @param status 弹幕状态（1-正常，0-已删除）
     * @param pageable 分页参数
     * @return 弹幕列表
     */
    Page<DanmakuComment> findByVideoIdAndStatusOrderByVideoTimestampAsc(
            String videoId, Integer status, Pageable pageable);
    
    /**
     * 根据视频ID和时间范围查询弹幕
     * @param videoId 视频ID
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param status 弹幕状态
     * @return 弹幕列表
     */
    List<DanmakuComment> findByVideoIdAndVideoTimestampBetweenAndStatusOrderByVideoTimestampAsc(
            String videoId, Double startTime, Double endTime, Integer status);
    
    /**
     * 根据视频ID查询弹幕总数
     * @param videoId 视频ID
     * @param status 弹幕状态
     * @return 弹幕总数
     */
    long countByVideoIdAndStatus(String videoId, Integer status);
    
    /**
     * 根据用户ID查询用户发送的弹幕
     * @param userId 用户ID
     * @param status 弹幕状态
     * @param pageable 分页参数
     * @return 弹幕列表
     */
    Page<DanmakuComment> findByUserIdAndStatusOrderByCreateTimeDesc(
            Long userId, Integer status, Pageable pageable);
    
    /**
     * 根据视频ID和用户ID查询用户在该视频的弹幕
     * @param videoId 视频ID
     * @param userId 用户ID
     * @param status 弹幕状态
     * @return 弹幕列表
     */
    List<DanmakuComment> findByVideoIdAndUserIdAndStatusOrderByVideoTimestampAsc(
            String videoId, Long userId, Integer status);
    
    /**
     * 查询指定时间范围内的弹幕数量（用于频率限制）
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 弹幕数量
     */
    @Query("SELECT COUNT(d) FROM DanmakuComment d WHERE d.userId = :userId " +
           "AND d.createTime BETWEEN :startTime AND :endTime")
    long countByUserIdAndCreateTimeBetween(
            @Param("userId") Long userId, 
            @Param("startTime") LocalDateTime startTime, 
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据视频ID和时间戳查询附近的弹幕（用于碰撞检测）
     * @param videoId 视频ID
     * @param timestamp 时间戳
     * @param timeDelta 时间范围（秒）
     * @param status 弹幕状态
     * @return 弹幕列表
     */
    @Query("SELECT d FROM DanmakuComment d WHERE d.videoId = :videoId " +
           "AND d.videoTimestamp BETWEEN :startTime AND :endTime " +
           "AND d.status = :status ORDER BY d.videoTimestamp ASC")
    List<DanmakuComment> findNearbyDanmaku(
            @Param("videoId") String videoId,
            @Param("startTime") Double startTime,
            @Param("endTime") Double endTime,
            @Param("status") Integer status);
    
    /**
     * 删除过期的弹幕（清理任务使用）
     * @param expireTime 过期时间
     * @return 删除的记录数
     */
    @Query("UPDATE DanmakuComment d SET d.status = 0 WHERE d.createTime < :expireTime")
    int markExpiredDanmakuAsDeleted(@Param("expireTime") LocalDateTime expireTime);
} 