package org.example.youtubeanalysisdemo.repository;

import org.example.youtubeanalysisdemo.model.User;
import org.example.youtubeanalysisdemo.model.UserLoginLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserLoginLogRepository extends JpaRepository<UserLoginLog, Long> {
    
    Optional<UserLoginLog> findByUserAndLoginDate(User user, LocalDate loginDate);
    
    @Query("SELECT COUNT(DISTINCT l.loginDate) FROM UserLoginLog l WHERE l.user = :user AND l.loginDate BETWEEN :startDate AND :endDate")
    Integer countDistinctLoginDatesBetween(@Param("user") User user, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    List<UserLoginLog> findByUserAndLoginDateBetween(User user, LocalDate startDate, LocalDate endDate);
} 