package org.example.youtubeanalysisdemo.repository;

import org.example.youtubeanalysisdemo.model.PlayHistory;
import org.example.youtubeanalysisdemo.model.User;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface PlayHistoryRepository extends JpaRepository<PlayHistory, Long> {
    
    // 根据用户ID查询播放历史，按时间倒序排列
    List<PlayHistory> findByUserOrderByPlayedAtDesc(User user, Pageable pageable);
    
    // 检查某个视频是否已经存在于用户的播放历史中
    Optional<PlayHistory> findByUserAndVideoId(User user, String videoId);
    
    // 删除用户的某个播放历史
    void deleteByUserAndVideoId(User user, String videoId);
    
    // 根据日期范围查询用户的播放历史
    List<PlayHistory> findByUserAndPlayedAtBetween(User user, LocalDateTime start, LocalDateTime end);
} 