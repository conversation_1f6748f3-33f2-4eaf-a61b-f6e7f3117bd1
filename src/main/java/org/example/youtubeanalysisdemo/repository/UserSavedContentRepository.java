package org.example.youtubeanalysisdemo.repository;

import org.example.youtubeanalysisdemo.entity.UserSavedContent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户收藏关系仓库接口
 */
@Repository
public interface UserSavedContentRepository extends JpaRepository<UserSavedContent, Long> {
    
    /**
     * 查找用户的所有收藏内容，按收藏时间降序排序
     * @param userId 用户ID
     * @return 用户收藏关系列表
     */
    List<UserSavedContent> findByUserIdOrderByCreatedAtDesc(String userId);
    
    /**
     * 根据学习状态查找用户的收藏内容，按收藏时间降序排序
     * @param userId 用户ID
     * @param learned 学习状态
     * @return 用户收藏关系列表
     */
    List<UserSavedContent> findByUserIdAndLearnedOrderByCreatedAtDesc(String userId, Integer learned);
    
    /**
     * 删除用户特定的收藏内容
     * @param userId 用户ID
     * @param id 收藏关系ID
     */
    void deleteByUserIdAndId(String userId, Long id);
    
    /**
     * 查找用户是否已收藏特定内容
     * @param userId 用户ID
     * @param contentId 内容ID
     * @return 用户收藏关系
     */
    Optional<UserSavedContent> findByUserIdAndContentId(String userId, Long contentId);
    
    /**
     * 联合查询用户收藏内容的详细信息
     * @param userId 用户ID
     * @return 包含详细信息的DTO列表
     */
    @Query("SELECT new org.example.youtubeanalysisdemo.dto.SavedItemDTO(" +
           "usc.id, usc.userId, lc.word, lc.analysis, lc.type, usc.learned, usc.createdAt) " +
           "FROM UserSavedContent usc JOIN LearningContent lc ON usc.contentId = lc.id " +
           "WHERE usc.userId = :userId ORDER BY usc.createdAt DESC")
    List<Object> findUserSavedContentsWithDetails(@Param("userId") String userId);
    
    /**
     * 根据学习状态联合查询用户收藏内容的详细信息
     * @param userId 用户ID
     * @param learned 学习状态
     * @return 包含详细信息的DTO列表
     */
    @Query("SELECT new org.example.youtubeanalysisdemo.dto.SavedItemDTO(" +
           "usc.id, usc.userId, lc.word, lc.analysis, lc.type, usc.learned, usc.createdAt) " +
           "FROM UserSavedContent usc JOIN LearningContent lc ON usc.contentId = lc.id " +
           "WHERE usc.userId = :userId AND usc.learned = :learned ORDER BY usc.createdAt DESC")
    List<Object> findUserSavedContentsWithDetailsByLearned(@Param("userId") String userId, @Param("learned") Integer learned);
}
