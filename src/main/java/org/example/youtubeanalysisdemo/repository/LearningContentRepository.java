package org.example.youtubeanalysisdemo.repository;

import org.example.youtubeanalysisdemo.entity.LearningContent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 学习内容仓库接口
 */
@Repository
public interface LearningContentRepository extends JpaRepository<LearningContent, Long> {
    
    /**
     * 根据内容唯一标识查找内容
     * @param contentKey 内容唯一标识
     * @return 学习内容对象
     */
    Optional<LearningContent> findByContentKey(String contentKey);
    
    /**
     * 根据单词查找内容
     * @param word 单词或语法
     * @return 学习内容列表
     */
    List<LearningContent> findByWord(String word);
    
    /**
     * 根据类型查找内容
     * @param type 内容类型
     * @return 学习内容列表
     */
    List<LearningContent> findByType(String type);
}
