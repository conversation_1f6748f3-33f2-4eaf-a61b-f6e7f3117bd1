package org.example.youtubeanalysisdemo.repository;

import org.example.youtubeanalysisdemo.entity.Subscription;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface SubscriptionRepository extends JpaRepository<Subscription, Long> {
    
    List<Subscription> findByUserId(String userId);
    
    Optional<Subscription> findByPaddleSubscriptionId(String paddleSubscriptionId);
    
    List<Subscription> findByUserIdAndStatus(String userId, String status);
    
    // 查找用户所有活跃订阅(包括当前生效和等待生效的)
    List<Subscription> findByUserIdAndStatusInOrderByStartDateAsc(String userId, List<String> statuses);
    
    // 查找用户当前生效的订阅(应该只有一个)
    Optional<Subscription> findFirstByUserIdAndStatusOrderByStartDateAsc(String userId, String status);
    
    // 查找用户的等待生效订阅，按开始时间排序
    List<Subscription> findByUserIdAndStatusOrderByStartDateAsc(String userId, String status);
    
    // 查找需要激活的订阅（startDate <= now 且 status = pending）
    @Query("SELECT s FROM Subscription s WHERE s.status = 'pending' AND s.startDate <= :now")
    List<Subscription> findSubscriptionsToActivate(@Param("now") LocalDateTime now);
    
    // 查找需要过期的订阅（nextBillDate <= now 且 status = active）
    @Query("SELECT s FROM Subscription s WHERE s.status = 'active' AND s.nextBillDate <= :now")
    List<Subscription> findSubscriptionsToExpire(@Param("now") LocalDateTime now);

    // 查找用户所有活跃和待处理订阅中，结束时间最晚的一个
    Optional<Subscription> findFirstByUserIdAndStatusInOrderByNextBillDateDesc(String userId, List<String> statuses);
} 