package org.example.youtubeanalysisdemo.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Groq转录服务，使用Groq API进行音频转录
 */
@Service
public class GroqTranscriptionService {

    private static final Logger logger = LoggerFactory.getLogger(GroqTranscriptionService.class);
    
    @Value("${groq.api.key:}")
    private String groqApiKey;
    
    @Value("${subtitles.save.path}")
    private String subtitleSavePath;
    
    private static final String GROQ_API_URL = "https://api.groq.com/openai/v1/audio/transcriptions";
    private static final String MODEL_NAME = "whisper-large-v3-turbo";
    private static final MediaType MEDIA_TYPE_JSON = MediaType.get("application/json; charset=utf-8");
    private static final MediaType MEDIA_TYPE_AUDIO = MediaType.get("audio/mpeg");
    
    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .readTimeout(120, TimeUnit.SECONDS)
            .build();
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 使用Groq API转录音频文件
     * @param audioFilePath 音频文件路径
     * @param language 语言代码
     * @param subtitleFileId 字幕文件ID
     * @return VTT字幕文件路径
     * @throws Exception 如果转录过程中出错
     */
    public String transcribeAudio(String audioFilePath, String language, String subtitleFileId) throws Exception {
        logger.info("开始使用Groq API转录音频: {}, subtitleFileId: {}", audioFilePath, subtitleFileId);
        
        if (groqApiKey == null || groqApiKey.isEmpty()) {
            throw new Exception("Groq API Key 未配置");
        }
        
        // 确保字幕目录存在
        Path subtitlePath = Paths.get(subtitleSavePath);
        if (!Files.exists(subtitlePath)) {
            Files.createDirectories(subtitlePath);
        }
        
        // 读取音频文件
        File audioFile = new File(audioFilePath);
        if (!audioFile.exists() || audioFile.length() == 0) {
            throw new Exception("音频文件不存在或为空: " + audioFilePath);
        }
        
        // 构建请求
        RequestBody audioBody = RequestBody.create(audioFile, MEDIA_TYPE_AUDIO);
        String filename = audioFile.getName();
        
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", filename, audioBody)
                .addFormDataPart("model", MODEL_NAME)
                .addFormDataPart("response_format", "verbose_json")
                .addFormDataPart("language", mapLanguageCode(language))
                .build();
        
        Request request = new Request.Builder()
                .url(GROQ_API_URL)
                .post(requestBody)
                .addHeader("Authorization", "Bearer " + groqApiKey)
                .build();
        
        logger.info("发送Groq API请求...");
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                throw new Exception("Groq API请求失败: " + response.code() + " - " + errorBody);
            }
            
            String responseBody = response.body().string();
            logger.debug("Groq API响应: {}", responseBody);
            
            // 解析响应并生成VTT文件
            String vttFilePath = generateVttFromGroqResponse(responseBody, audioFilePath, subtitleFileId);
            
            logger.info("Groq音频转录完成: {}", vttFilePath);
            return vttFilePath;
        }
    }
    
    /**
     * 将语言代码映射为Groq API支持的格式
     */
    private String mapLanguageCode(String language) {
        switch (language.toLowerCase()) {
            case "ja":
            case "japanese":
                return "ja";
            case "en":
            case "english":
                return "en";
            case "zh":
            case "chinese":
                return "zh";
            case "ko":
            case "korean":
                return "ko";
            default:
                return "en"; // 默认英语
        }
    }
    
    /**
     * 从Groq API响应生成VTT文件
     */
    private String generateVttFromGroqResponse(String responseJson, String audioFilePath, String subtitleFileId) throws Exception {
        try {
            JsonNode rootNode = objectMapper.readTree(responseJson);
            JsonNode segmentsNode = rootNode.get("segments");
            
            if (segmentsNode == null || !segmentsNode.isArray()) {
                throw new Exception("Groq API响应格式错误：缺少segments数组");
            }
            
            // 生成VTT文件路径
            String randomId = UUID.randomUUID().toString().substring(0, 8);
            String baseName = Paths.get(audioFilePath).getFileName().toString().replace(".mp3", "");
            String vttFileName = String.format("%s_%s_%s.vtt", baseName, subtitleFileId, randomId);
            Path vttFilePath = Paths.get(subtitleSavePath).resolve(vttFileName);
            
            // 构建VTT内容
            StringBuilder vttContent = new StringBuilder();
            vttContent.append("WEBVTT\n\n");
            
            int sequenceNumber = 1;
            for (JsonNode segment : segmentsNode) {
                double start = segment.get("start").asDouble();
                double end = segment.get("end").asDouble();
                String text = segment.get("text").asText().trim();
                
                if (!text.isEmpty()) {
                    vttContent.append(sequenceNumber).append("\n");
                    vttContent.append(formatTime(start)).append(" --> ").append(formatTime(end)).append("\n");
                    vttContent.append(text).append("\n\n");
                    sequenceNumber++;
                }
            }
            
            // 写入VTT文件
            Files.writeString(vttFilePath, vttContent.toString());
            
            logger.info("成功生成VTT文件: {}, 共{}条字幕", vttFilePath, sequenceNumber - 1);
            return vttFilePath.toString();
            
        } catch (Exception e) {
            logger.error("解析Groq API响应失败", e);
            throw new Exception("解析Groq API响应失败: " + e.getMessage());
        }
    }
    
    /**
     * 将秒数转换为VTT时间格式 (HH:MM:SS.mmm)
     */
    private String formatTime(double seconds) {
        int hours = (int) (seconds / 3600);
        int minutes = (int) ((seconds % 3600) / 60);
        double remainingSeconds = seconds % 60;
        
        return String.format("%02d:%02d:%06.3f", hours, minutes, remainingSeconds);
    }
} 