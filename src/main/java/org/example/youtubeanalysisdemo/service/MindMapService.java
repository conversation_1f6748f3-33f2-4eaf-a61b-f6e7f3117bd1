package org.example.youtubeanalysisdemo.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import java.util.*;

/**
 * 思维导图服务
 * 使用Mistral AI生成思维导图数据
 */
@Service
public class MindMapService {

    private static final Logger logger = LoggerFactory.getLogger(MindMapService.class);

    private final ChatModel chatModel;
    private final ObjectMapper objectMapper;

    public MindMapService(ChatModel chatModel) {
        this.chatModel = chatModel;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 使用Mistral AI生成思维导图数据
     * @param videoTitle 视频标题
     * @param subtitleText 字幕文本
     * @return 思维导图数据
     */
    public Map<String, Object> generateMindMapFromSubtitles(String videoTitle, String subtitleText) {
        logger.info("使用Mistral AI生成思维导图: videoTitle={}", videoTitle);
        
        try {
            // 限制字幕文本长度，避免超出API限制
            String limitedText = subtitleText;
            if (subtitleText.length() > 8000) {
                limitedText = subtitleText.substring(0, 8000);
                logger.info("字幕文本过长，已截断至8000字符");
            }
            
            String promptText = String.format("""
                你是一位顶级的分析师和思维导图专家。你的任务是深入分析给定的YouTube视频字幕，并生成一个结构清晰、内容丰富、逻辑严谨的大纲式思维导图。

                核心要求：
                1.  **动态主题识别**：不要生成笼统的摘要！你需要像人类专家一样，智能地识别出视频内容的核心脉络和不同的信息板块。例如，视频可能包含“主持人介绍”、“历史背景”、“核心概念阐述”、“技术细节”、“文化词汇解析”、“示例分析”、“互动问答”、“总结”等部分。请根据实际内容，提炼出这些主题作为思维导图的主要分支。如果视频没有相关内容，则不要创造这些主题。
                2.  **内容为王**：每个节点的文本都必须是信息量饱满的完整描述，而不是简单的、孤立的关键词。确保用户仅通过阅读思维导图就能掌握视频的核心知识。
                3.  **动态层级**：不要拘泥于固定的2-3层结构！根据内容的逻辑层次和复杂度，自由地构建树状结构，可以有更深或更浅的层级。
                4.  **JSON输出**：必须只输出一个严格的、有效的JSON对象，不要包含任何额外的解释性文字或代码块标记。

                JSON结构规范：
                -   每个节点都是一个对象，必须包含以下字段：`id` (唯一字符串), `text` (节点描述文本), `children` (子节点数组)。
                -   为了前端渲染，请在每个节点中额外添加 `level` 字段，根节点为0，逐级递增。

                JSON格式示例（这只是一个结构示例，你要根据视频内容生成自己的主题和层级）：
                {
                  "id": "root",
                  "text": "视频的核心主题（由你总结）",
                  "level": 0,
                  "children": [
                    {
                      "id": "theme_1",
                      "text": "识别出的第一个主题（例如：历史与起源）",
                      "level": 1,
                      "children": [
                        {
                          "id": "subpoint_1.1",
                          "text": "关于该主题的第一个详细信息点。",
                          "level": 2,
                          "children": []
                        },
                        {
                          "id": "subpoint_1.2",
                          "text": "关于该主题的第二个详细信息点，可能还包含更深层次的解释。",
                          "level": 2,
                          "children": [
                            {
                                "id": "detail_1.2.1",
                                "text": "这是一个更深层次的细节。",
                                "level": 3,
                                "children": []
                            }
                          ]
                        }
                      ]
                    },
                    {
                      "id": "theme_2",
                      "text": "识别出的第二个主题（例如：关键术语解析）",
                      "level": 1,
                      "children": [
                        {
                          "id": "subpoint_2.1",
                          "text": "对第一个关键术语的解释。",
                          "level": 2,
                          "children": []
                        }
                      ]
                    }
                  ]
                }

                现在，请根据以下视频信息，开始你的专业分析和创作：

                视频标题: %s

                字幕内容:
                %s
                """, videoTitle, limitedText);

            String result = chatModel.call(promptText);
            
            // 调试日志：输出Mistral AI的原始响应
            logger.info("Mistral AI原始响应: {}", result);
            
            if (result == null || result.trim().isEmpty()) {
                logger.warn("Mistral AI返回空结果，使用默认思维导图");
                return createDefaultMindMap(videoTitle);
            }
            
            // 清理响应，提取JSON部分
            String jsonResult = extractJsonFromResponse(result);
            
            try {
                // 解析JSON响应
                Map<String, Object> mindMapData = objectMapper.readValue(jsonResult, new TypeReference<Map<String, Object>>() {});
                
                // 验证数据结构
                if (validateMindMapData(mindMapData)) {
                    logger.info("Mistral AI思维导图生成成功");
                    return mindMapData;
                } else {
                    logger.warn("思维导图数据格式不正确，使用默认思维导图");
                    return createDefaultMindMap(videoTitle);
                }
                
            } catch (Exception parseException) {
                logger.error("解析思维导图JSON失败: {}", parseException.getMessage());
                logger.debug("原始响应: {}", result);
                return createDefaultMindMap(videoTitle);
            }
            
        } catch (Exception e) {
            logger.error("调用Mistral AI生成思维导图失败", e);
            return createDefaultMindMap(videoTitle);
        }
    }
    
    /**
     * 从响应中提取JSON部分
     */
    private String extractJsonFromResponse(String response) {
        if (response == null) {
            return "{}";
        }
        
        // 查找第一个{和最后一个}之间的内容
        int startIndex = response.indexOf('{');
        int endIndex = response.lastIndexOf('}');
        
        if (startIndex != -1 && endIndex != -1 && startIndex < endIndex) {
            return response.substring(startIndex, endIndex + 1);
        }
        
        // 如果没有找到完整的JSON，返回空对象
        return "{}";
    }
    
    /**
     * 验证思维导图数据结构
     */
    private boolean validateMindMapData(Map<String, Object> data) {
        if (data == null) {
            return false;
        }

        // 检查根节点必要字段
        return data.containsKey("id") &&
               data.containsKey("text") &&
               data.containsKey("level") &&
               data.containsKey("children");
    }
    
    /**
     * 创建默认思维导图
     */
    private Map<String, Object> createDefaultMindMap(String videoTitle) {
        Map<String, Object> mindMapData = new HashMap<>();
        
        // 创建根节点
        Map<String, Object> root = new HashMap<>();
        root.put("id", "root");
        root.put("text", videoTitle);
        root.put("level", 0);

        // 创建默认分支
        List<Map<String, Object>> children = new ArrayList<>();
        
        // 分支1：视频主要内容介绍
        Map<String, Object> branch1 = new HashMap<>();
        branch1.put("id", "branch1");
        branch1.put("text", "视频主要内容介绍");
        branch1.put("level", 1);
        branch1.put("children", Arrays.asList(
            createLeafNode("leaf1", "视频讲述的核心主题和要点"),
            createLeafNode("leaf2", "涵盖的重要知识点和信息")
        ));
        children.add(branch1);
        
        // 分支2：详细内容展开
        Map<String, Object> branch2 = new HashMap<>();
        branch2.put("id", "branch2");
        branch2.put("text", "详细内容展开");
        branch2.put("level", 1);
        branch2.put("color", "#F5A623");
        branch2.put("children", Arrays.asList(
            createLeafNode("leaf3", "相关背景信息和历史脉络"),
            createLeafNode("leaf4", "具体案例和实际应用场景")
        ));
        children.add(branch2);
        
        // 分支3：总结与启发
        Map<String, Object> branch3 = new HashMap<>();
        branch3.put("id", "branch3");
        branch3.put("text", "总结与启发");
        branch3.put("level", 1);
        branch3.put("children", Arrays.asList(
            createLeafNode("leaf5", "视频传达的关键结论和要点"),
            createLeafNode("leaf6", "对观众的实用建议和启发")
        ));
        children.add(branch3);
        
        root.put("children", children);
        
        return root;
    }
    
    /**
     * 创建叶子节点
     */
    private Map<String, Object> createLeafNode(String id, String text) {
        Map<String, Object> leaf = new HashMap<>();
        leaf.put("id", id);
        leaf.put("text", text);
        leaf.put("level", 2);
        leaf.put("children", new ArrayList<>());
        return leaf;
    }
} 