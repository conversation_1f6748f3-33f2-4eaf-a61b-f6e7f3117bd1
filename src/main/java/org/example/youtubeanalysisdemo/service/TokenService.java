package org.example.youtubeanalysisdemo.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

@Service
public class TokenService {

    private static final Logger logger = LoggerFactory.getLogger(TokenService.class);
    private final RestTemplate restTemplate;

    public TokenService() {
        this.restTemplate = new RestTemplate();
    }

    /**
     * 验证Google访问令牌，并获取用户信息
     * 
     * @param accessToken Google访问令牌
     * @return 用户信息，如果令牌无效则返回null
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> validateGoogleToken(String accessToken) {
        try {
            logger.info("开始验证Google访问令牌");
            // 调用Google API验证令牌并获取用户信息
            String url = "https://www.googleapis.com/oauth2/v2/userinfo";
            
            // 创建包含授权头的请求
            org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
            headers.setBearerAuth(accessToken);
            org.springframework.http.HttpEntity<?> entity = new org.springframework.http.HttpEntity<>(headers);
            
            // 发送请求并获取响应
            logger.info("向Google API发送请求获取用户信息");
            org.springframework.http.ResponseEntity<Map> response = restTemplate.exchange(
                url, 
                org.springframework.http.HttpMethod.GET, 
                entity, 
                Map.class
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                logger.info("Google令牌验证成功，获取到用户信息: {}", response.getBody());
                return response.getBody();
            }
            
            logger.warn("Google令牌验证失败，响应状态码: {}", response.getStatusCode());
            return null;
        } catch (Exception e) {
            // 处理异常情况，如令牌无效或网络问题
            logger.error("验证Google令牌时发生异常", e);
            return null;
        }
    }
} 