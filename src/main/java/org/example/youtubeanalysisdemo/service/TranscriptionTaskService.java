package org.example.youtubeanalysisdemo.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;

import org.example.youtubeanalysisdemo.dto.TranscriptionTask;

/**
 * 转录任务管理服务
 * 负责转录任务的创建、管理和进度跟踪
 */
@Slf4j
@Service
public class TranscriptionTaskService {

    private final ConcurrentHashMap<String, TranscriptionTask> tasks = new ConcurrentHashMap<>();

    @Autowired
    private TranscriptionService transcriptionService;

    @Autowired
    private YouTubeUtilService youTubeUtilService;

    /**
     * 创建一个新的转录任务
     */
    public String createTranscriptionTask(String videoUrl, String language, String createdBy, String subtitleFileId) {
        String videoId = youTubeUtilService.validateAndExtractVideoId(videoUrl);
        if (videoId == null) {
            throw new IllegalArgumentException("无效的YouTube URL");
        }

        String taskId = UUID.randomUUID().toString();
        TranscriptionTask task = new TranscriptionTask(taskId, videoUrl, videoId, language, createdBy, this, subtitleFileId);
        tasks.put(taskId, task);

        log.info("创建了新的转录任务: taskId={}, videoId={}, language={}, subtitleFileId={}", taskId, videoId, language, subtitleFileId);
        return taskId;
    }

    /**
     * 获取转录任务进度
     * @param taskId 任务ID
     * @return 任务信息
     */
    public Map<String, Object> getTranscriptionProgress(String taskId) {
        if (taskId == null || taskId.isEmpty()) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        // 获取任务信息
        TranscriptionTask taskInfo = tasks.get(taskId);
        if (taskInfo == null) {
            throw new IllegalArgumentException("任务不存在或已完成");
        }
        
        // 复制任务信息，避免并发问题
        Map<String, Object> response = new HashMap<>();
        response.put("taskId", taskInfo.getTaskId());
        response.put("videoUrl", taskInfo.getVideoUrl());
        response.put("videoId", taskInfo.getVideoId());
        response.put("language", taskInfo.getLanguage());
        response.put("status", taskInfo.getStatus());
        response.put("currentStep", taskInfo.getCurrentStep());
        response.put("progress", taskInfo.getProgress());
        response.put("createdBy", taskInfo.getCreatedBy());
        response.put("subtitleFileId", taskInfo.getSubtitleFileId());
        response.put("error", taskInfo.getError());
        response.put("originalSubtitlePath", taskInfo.getOriginalSubtitlePath());
        response.put("translatedSubtitlePath", taskInfo.getTranslatedSubtitlePath());
        response.put("createdAt", taskInfo.getCreatedAt());
        response.put("completedAt", taskInfo.getCompletedAt());
        response.put("success", true);
        
        return response;
    }

    /**
     * 异步处理转录任务
     * @param taskId 任务ID
     */
    public void processTranscriptionTaskAsync(String taskId) {
        // 使用CompletableFuture来异步处理整个转录和翻译流程
        CompletableFuture.runAsync(() -> {
            try {
                processTranscriptionTask(taskId).join(); // 等待异步流程完成
            } catch (Exception e) {
                log.error("转录任务处理失败: " + taskId, e);
                TranscriptionTask taskInfo = tasks.get(taskId);
                if (taskInfo != null) {
                    taskInfo.setStatus("failed");
                    taskInfo.setError("翻译失败: " + e.getMessage());
                }
            }
        });
    }

    /**
     * 处理转录任务
     * @param taskId 任务ID
     * @return 一个CompletableFuture，在任务完成时会包含最终结果
     */
    private CompletableFuture<Void> processTranscriptionTask(String taskId) {
        TranscriptionTask taskInfo = tasks.get(taskId);
        if (taskInfo == null) {
            log.warn("任务不存在: {}", taskId);
            return CompletableFuture.failedFuture(new IllegalArgumentException("任务不存在"));
        }
        
        String videoUrl = taskInfo.getVideoUrl();
        String videoId = taskInfo.getVideoId();
        String language = taskInfo.getLanguage();
        
        // 更新任务状态为处理中
        updateTaskStatus(taskId, "processing", "transcribing", 20);
        
        // 1. 使用Groq API进行转录（无需排队）
        return transcriptionService.transcribeVideo(videoUrl, videoId, language, taskInfo.getSubtitleFileId())
            .thenCompose(originalVttPath -> {
                try {
                    // 转录成功，更新状态为翻译中
                    log.info("音频转录完成: {}", originalVttPath);
                    updateTaskStatus(taskId, "processing", "translating", 70);
                    
                    // 2. 翻译字幕为中文
                    String translatedVttPath = transcriptionService.translateSubtitles(originalVttPath, "zh-Hans");
                    log.info("字幕翻译完成: {}", translatedVttPath);
                    
                    // 获取文件名部分而不是完整路径
                    String originalFileName = new File(originalVttPath).getName();
                    String translatedFileName = new File(translatedVttPath).getName();
                    
                    // 构建正确的相对路径
                    String originalRelativePath = "/subtitles/" + originalFileName;
                    String translatedRelativePath = "/subtitles/" + translatedFileName;
                    
                    // 更新状态为完成
                    taskInfo.setStatus("completed");
                    taskInfo.setProgress(100);
                    taskInfo.setCurrentStep("completed");
                    taskInfo.setOriginalSubtitlePath(originalRelativePath);
                    taskInfo.setTranslatedSubtitlePath(translatedRelativePath);
                    taskInfo.setLanguage(language);
                    taskInfo.setCompletedAt(System.currentTimeMillis());
                    
                    log.info("转录任务完成: {}", taskId);
                    
                    // 清理原始VTT文件（可选，取决于是否需要保留）
                    // Files.deleteIfExists(Paths.get(originalVttPath));
                    
                    return CompletableFuture.<Void>completedFuture(null);
                    
                } catch (Exception e) {
                    log.error("转录任务的翻译步骤失败: {}", taskId, e);
                    updateTaskStatus(taskId, "failed", "translation_failed", 90);
                    taskInfo.setError("翻译失败: " + e.getMessage());
                    return CompletableFuture.<Void>failedFuture(e);
                }
            })
            .exceptionally(ex -> {
                log.error("转录任务失败: {}", taskId, ex);
                updateTaskStatus(taskId, "failed", "transcription_failed", 50);
                taskInfo.setError("转录失败: " + ex.getMessage());
                return null;
            });
    }

    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param status 状态
     * @param currentStep 当前步骤
     * @param progress 进度
     */
    public void updateTaskStatus(String taskId, String status, String currentStep, int progress) {
        TranscriptionTask taskInfo = tasks.get(taskId);
        if (taskInfo != null) {
            taskInfo.setStatus(status);
            taskInfo.setCurrentStep(currentStep);
            taskInfo.setProgress(progress);
            log.info("任务状态更新: taskId={}, status={}, step={}, progress={}%", taskId, status, currentStep, progress);
        } else {
            log.warn("尝试更新不存在的任务状态: {}", taskId);
        }
    }

    /**
     * 移除已完成的任务
     * @param taskId 任务ID
     */
    public void removeTask(String taskId) {
        tasks.remove(taskId);
        log.info("移除转录任务: {}", taskId);
    }

    /**
     * 获取所有任务数量
     * @return 任务数量
     */
    public int getTaskCount() {
        return tasks.size();
    }
} 