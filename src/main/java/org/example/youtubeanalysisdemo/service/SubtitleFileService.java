package org.example.youtubeanalysisdemo.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

/**
 * 字幕文件管理服务
 * 负责字幕文件的查找、验证和清理操作
 */
@Slf4j
@Service
public class SubtitleFileService {

    @Value("${subtitles.directory:src/main/resources/static/subtitles}")
    private String subtitlesDirectory;
    
    private static final String SUBTITLE_EXTENSION = ".vtt";

    /**
     * 根据指定语言获取合适的字幕文件地址
     * 
     * @param baseFilename 基础文件名
     * @param targetLanguage 目标语言
     * @return 字幕文件路径，如果未找到返回null
     */
    public String findVttSubtitlePathForLanguage(String baseFilename, String targetLanguage) {
        Path subtitlesDirPath = Paths.get(subtitlesDirectory);
        
        String[] langVariants = getLangVariants(targetLanguage);

        for (String lang : langVariants) {
            Path vttPath = subtitlesDirPath.resolve(baseFilename + "." + lang + SUBTITLE_EXTENSION);
            if (Files.exists(vttPath) && Files.isRegularFile(vttPath)) {
                // 文件存在，但可能正在写入，添加验证和等待
                try {
                    if (validateSubtitleFile(vttPath)) {
                        return "/subtitles/" + vttPath.getFileName().toString();
                    }
                } catch (Exception e) {
                    log.error("检查字幕文件时发生错误: {}", vttPath.getFileName(), e);
                }
            }
        }
        return null;
    }

    /**
     * 获取语言变体数组
     */
    private String[] getLangVariants(String targetLanguage) {
        if ("zh".equals(targetLanguage)) {
            // 优先级: zh (通用) -> zh-Hans (简体) -> zh-Hant (繁体) -> zh-CN -> zh-TW -> zh-HK
            return new String[]{"zh", "zh-Hans", "zh-Hant", "zh-CN", "zh-TW", "zh-HK"};
        } else if ("ja".equals(targetLanguage)) {
            // 支持多种可能的日语语言代码
            return new String[]{"ja", "ja-JP", "jpn"};
        } else {
            return new String[]{targetLanguage};
        }
    }

    /**
     * 验证字幕文件是否有效且完整
     */
    private boolean validateSubtitleFile(Path vttPath) throws InterruptedException {
        int maxAttempts = 3;
        int attemptCount = 0;
        
        while (attemptCount < maxAttempts) {
            attemptCount++;
            
            try {
                // 检查文件是否可读
                if (Files.isReadable(vttPath)) {
                    // 检查文件大小是否大于最小阈值(50字节)
                    long fileSize = Files.size(vttPath);
                    if (fileSize > 50) {
                        // 尝试读取文件，以确认它完整且未被锁定
                        String content = Files.readString(vttPath);
                        // 除检查WEBVTT标记外，还检查是否含有字幕内容标记
                        if (content.contains("WEBVTT") && content.contains("-->")) {
                            // 附加检查:确保文件包含至少2对有效时间标记
                            int timeMarksCount = countTimeMarkers(content);
                            if (timeMarksCount >= 2) {
                                // 新增：检查是否包含实际的字幕文本内容
                                if (hasActualSubtitleContent(content)) {
                                    log.info("字幕文件已验证可用: {}, 尝试次数: {}, 包含{}个时间点，包含实际内容", 
                                            vttPath.getFileName(), attemptCount, timeMarksCount);
                                    return true;
                                } else {
                                    log.warn("字幕文件只包含时间戳，没有实际文本内容 (尝试 {}/{}): {}", 
                                            attemptCount, maxAttempts, vttPath.getFileName());
                                }
                            } else {
                                log.warn("字幕文件不包含足够的时间标记 ({}个，尝试 {}/{}): {}", 
                                        timeMarksCount, attemptCount, maxAttempts, vttPath.getFileName());
                            }
                        } else {
                            log.warn("字幕文件似乎不是有效的VTT格式 (尝试 {}/{}): {}", attemptCount, maxAttempts, vttPath.getFileName());
                        }
                    } else {
                        log.warn("字幕文件过小({}字节) (尝试 {}/{}): {}", fileSize, attemptCount, maxAttempts, vttPath.getFileName());
                    }
                } else {
                    log.warn("字幕文件不可读 (尝试 {}/{}): {}", attemptCount, maxAttempts, vttPath.getFileName());
                }
            } catch (Exception e) {
                log.warn("验证字幕文件时出错 (尝试 {}/{}): {} - {}", attemptCount, maxAttempts, vttPath.getFileName(), e.getMessage());
            }
            
            // 如果文件未准备好，等待一段时间后重试
            if (attemptCount < maxAttempts) {
                Thread.sleep(300 * (1 << (attemptCount - 1))); // 300ms, 600ms, 1200ms...
            }
        }
        
        log.error("字幕文件验证失败，经过{}次尝试后: {}", maxAttempts, vttPath.getFileName());
        return false;
    }

    /**
     * 统计VTT字幕文件中时间标记的数量
     */
    public int countTimeMarkers(String vttContent) {
        // 查找类似 "00:00:00.000 --> 00:00:05.000" 的时间标记
        Pattern pattern = Pattern.compile("\\d{2}:\\d{2}:\\d{2}\\.\\d{3} --> \\d{2}:\\d{2}:\\d{2}\\.\\d{3}");
        Matcher matcher = pattern.matcher(vttContent);
        
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        
        return count;
    }

    /**
     * 检查字幕文件是否包含实际的文本内容
     * @param vttContent VTT文件内容
     * @return 是否包含实际内容
     */
    private boolean hasActualSubtitleContent(String vttContent) {
        if (vttContent == null || vttContent.isEmpty()) {
            return false;
        }
        
        String[] lines = vttContent.split("\n");
        boolean headerPassed = false;
        int contentLines = 0;
        
        for (String line : lines) {
            // 跳过头部信息
            if (!headerPassed) {
                if (line.trim().isEmpty()) {
                    headerPassed = true;
                }
                continue;
            }
            
            // 跳过空行和时间戳行
            if (line.trim().isEmpty() || line.contains("-->")) {
                continue;
            }
            
            // 检查是否为有效的字幕文本行
            String cleanedLine = line.replaceAll("<[^>]*>", "").trim();
            if (!cleanedLine.isEmpty() && !cleanedLine.matches("\\d+")) {
                contentLines++;
            }
        }
        
        // 至少需要有5行有效的字幕内容才认为是有效的
        boolean hasContent = contentLines >= 5;
        log.debug("字幕文件内容检查: 有效内容行数={}, 是否有效={}", contentLines, hasContent);
        return hasContent;
    }

    /**
     * 清理指定会话的字幕文件
     * 
     * @param sessionId 会话ID
     * @return 清理结果信息
     */
    public String cleanupSubtitleFiles(String sessionId) {
        log.info("尝试清理VTT字幕 sessionId: {}", sessionId);
        
        try {
            Path subtitlesPath = Paths.get(subtitlesDirectory).toAbsolutePath();
            if (Files.exists(subtitlesPath) && Files.isDirectory(subtitlesPath)) {
                // 查找 .vtt 文件
                File[] filesToDelete = subtitlesPath.toFile().listFiles((dir, name) ->
                        name.contains(sessionId) && name.toLowerCase().endsWith(SUBTITLE_EXTENSION));
                
                if (filesToDelete != null && filesToDelete.length > 0) {
                    for (File file : filesToDelete) {
                        log.info("删除VTT字幕文件: {}", file.getAbsolutePath());
                        if (file.delete()) {
                            log.info("成功删除: {}", file.getName());
                        } else {
                            log.warn("删除失败: {}. 尝试退出时删除.", file.getName());
                            file.deleteOnExit();
                        }
                    }
                    return "VTT字幕文件清理任务已执行。";
                } else {
                    log.info("没有找到需要清理的VTT字幕文件 sessionId: {}", sessionId);
                    return "没有找到需要清理的VTT字幕文件。";
                }
            } else {
                log.warn("字幕目录不存在或不是目录: {}", subtitlesPath);
                return "字幕目录配置错误。";
            }
        } catch (Exception e) {
            log.error("清理VTT字幕文件时发生严重错误 sessionId: {}", sessionId, e);
            throw new RuntimeException("清理VTT字幕时发生内部错误。", e);
        }
    }

    /**
     * 清理无效的字幕文件（只有时间戳没有内容的文件）
     * @param sessionId 会话ID
     * @return 清理结果
     */
    public Map<String, Object> cleanupInvalidSubtitles(String sessionId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Path subtitlesDirPath = Paths.get(subtitlesDirectory);
            if (!Files.exists(subtitlesDirPath)) {
                result.put("success", false);
                result.put("message", "字幕目录不存在");
                return result;
            }
            
            // 查找与sessionId相关的所有字幕文件
            File[] subtitleFiles = subtitlesDirPath.toFile().listFiles((dir, name) -> 
                name.contains(sessionId) && name.toLowerCase().endsWith(SUBTITLE_EXTENSION));
            
            if (subtitleFiles == null || subtitleFiles.length == 0) {
                result.put("success", true);
                result.put("message", "没有找到相关的字幕文件");
                result.put("deletedCount", 0);
                return result;
            }
            
            int deletedCount = 0;
            List<String> deletedFiles = new ArrayList<>();
            
            for (File file : subtitleFiles) {
                try {
                    String content = Files.readString(file.toPath());
                    if (!hasActualSubtitleContent(content)) {
                        boolean deleted = file.delete();
                        if (deleted) {
                            deletedCount++;
                            deletedFiles.add(file.getName());
                            log.info("删除无效字幕文件: {}", file.getName());
                        } else {
                            log.warn("无法删除无效字幕文件: {}", file.getName());
                        }
                    }
                } catch (IOException e) {
                    log.error("读取字幕文件时出错: {}", file.getName(), e);
                }
            }
            
            result.put("success", true);
            result.put("message", String.format("成功清理了 %d 个无效字幕文件", deletedCount));
            result.put("deletedCount", deletedCount);
            result.put("deletedFiles", deletedFiles);
            
            log.info("清理无效字幕文件完成: 删除了{}个文件", deletedCount);
            
        } catch (Exception e) {
            log.error("清理无效字幕文件时发生错误", e);
            result.put("success", false);
            result.put("message", "清理失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 确保字幕目录存在
     */
    public void ensureSubtitlesDirectoryExists() throws IOException {
        Path subtitlesDirPath = Paths.get(subtitlesDirectory);
        if (!Files.exists(subtitlesDirPath)) {
            Files.createDirectories(subtitlesDirPath);
            log.info("创建字幕目录: {}", subtitlesDirPath);
        }
    }

    /**
     * 获取字幕目录路径
     */
    public Path getSubtitlesDirectoryPath() {
        return Paths.get(subtitlesDirectory);
    }
} 