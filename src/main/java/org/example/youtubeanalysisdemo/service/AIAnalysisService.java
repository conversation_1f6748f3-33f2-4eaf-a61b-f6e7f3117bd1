package org.example.youtubeanalysisdemo.service;

import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.models.ChatCompletion;
import com.openai.models.ChatCompletionCreateParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * AI分析服务
 * 负责与DashScope API交互，提供语法分析、单词分析和联想生成功能
 */
@Service
public class AIAnalysisService {
    private static final Logger logger = LoggerFactory.getLogger(AIAnalysisService.class);

    @Value("${dashscope.api.key:}")
    private String dashscopeApiKey;
    
    private final String modelName = "deepseek-v3";
//    private final String modelName = "qwen-max-latest";
    private static final String DASHSCOPE_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1";

    private final ChatModel chatModel;

    @Autowired
    public AIAnalysisService(ChatModel chatModel) {
        this.chatModel = chatModel;
    }

    /**
     * 调用DashScope API进行语法分析（支持多语言）
     */
    public String analyzeGrammar(String text, String language) throws IOException {
        logger.info("调用 DashScope API 进行语法分析: text={}, language={}", text, language);
        System.out.println("调用 DashScope API 进行语法分析: text=" + text + ", language=" + language);

        try {
            if (dashscopeApiKey == null || dashscopeApiKey.isEmpty()) {
                logger.error("DashScope API Key 未配置");
                System.err.println("DashScope API Key 未配置");
                throw new IOException("DashScope API Key 未配置");
            }

            OpenAIClient client = OpenAIOkHttpClient.builder()
                    .apiKey(dashscopeApiKey)
                    .baseUrl(DASHSCOPE_API_URL)
                    .build();

            String prompt;
            if ("english".equalsIgnoreCase(language)) {
                prompt = createEnglishGrammarPrompt(text);
            } else {
                prompt = createJapaneseGrammarPrompt(text);
            }

            ChatCompletionCreateParams params = ChatCompletionCreateParams.builder()
                    .addUserMessage(prompt)
                    .model(modelName)
                    .build();

            ChatCompletion chatCompletion = client.chat().completions().create(params);
            String result = chatCompletion.choices().get(0).message().content().orElse("无返回内容").replace(':', ':').replace("「", "").replace("」", "");
            
            logger.debug("DashScope 语法分析返回: {}", result);
            System.out.println("DashScope 语法分析返回: " + result);
            return result;
        } catch (Exception e) {
            logger.error("DashScope API 语法分析调用失败: text={}, language={}", text, language, e);
            System.err.println("DashScope API 语法分析调用失败: text=" + text + ", language=" + language + ", error=" + e.getMessage());
            throw new IOException("DashScope API 调用失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建日语语法分析prompt
     */
    private String createJapaneseGrammarPrompt(String text) {
        return String.format(
                "你是一个专业的日语语言分析助手，擅长将日语句子进行细致解析。请严格按以下结构输出分析结果！！！\n" +
                        "严格约束：\n" +
                        "1. 例文字段要提供展示该语法用法的例句，不是原句\n" +
                        "2. 中文翻译字段只输出例文的中文翻译\n" +
                        "3. 注意字段要说明使用场景和与其他语法的区别\n" +
                        "4. 严格按照指定格式输出，不要偏离，不要输出其他无关的符号\n\n" +
                        "目标句子:%s\n\n" +
                        "【语法分析】\n\n" +
                        "对句子中的主要语法点（如接续、变形、敬语、自谦语、可能形、被动形、使役形、使役被动、授受关系等）进行分析解释\n\n"+
                        "示例参考:\n" +
                        "▲〜ている:\n" +
                        "语法分析:动词て形+いる，表示动作的进行状态或结果状态\n" +
                        "例文:今、友達と電話で話しています。\n" +
                        "中文翻译:现在正在和朋友打电话。\n" +
                        "注意:用于表示动作正在进行或状态持续，区别于单纯的动词现在形\n\n", text
        );
    }
    
    /**
     * 创建英语语法分析prompt
     */
    private String createEnglishGrammarPrompt(String text) {
        return String.format(
                "你是一个专业的英语分析助手，擅长将英语句子中出现的语法点和惯用搭配进行细致的解析。请严格按以下结构输出分析结果！！！\n" +
                        "重要要求:" +
                        "1.请使用纯文本格式，不要使用任何markdown标记（如**、*、#等），直接输出文本内容。" +
                        "2.如果句子当中不存在该句子成分、语法现象、惯用搭配等，不许输出\"无\"等字眼，而是直接跳过该部分。例如，如果句子中没有宾语，就不要输出\"宾语(Object): 无\"！直接跳过。" +
                        "3.不需要举原文以外的例子，分析原文即可\n" +
                        "目标句子:%s\n\n" +
                        "【语法分析】\n\n" +
                        "请按以下格式详细分析句子的语法结构:\n\n" +
                        "▲句子成分分析:\n" +
                        "主语(Subject): \n" +
                        "谓语(Predicate): \n" +
                        "宾语(Object): \n" +
                        "定语(Attributive): \n" +
                        "状语(Adverbial): \n" +
                        "补语(Complement): \n\n" +

                        "▲动词时态:\n" +
                        "分析句子中动词的时态（现在时、过去时、将来时、完成时等）、意义和结构\n" +
                        "原文: has been working(现在完成进行时):表示动作从过去某一时间开始，一直持续到现在，并可能仍在进行。\n\n" +
                        "结构:has/have + been + 现在分词（动词-ing）（主语是第三人称单数时用has，其他人称用have）\n" +

                        "▲从句类型分析:\n" +
                        "如果句子包含从句，请分析从句类型和各部分的功能\n" +
                        "定语从句: （限制性/非限制性定语从句）、名词性从句: （主语从句、宾语从句、表语从句、同位语从句）、状语从句: （时间、地点、原因、条件、让步、方式、比较、结果、目的状语从句）\n" +
                        "原文: The book that I borrowed from the library is very interesting.（我从图书馆借的那本书非常有趣。）\n\n" +
                        "1.主句（Main Clause）: \"The book is very interesting.\""+
                        "主语:The book\n" +
                        "谓语:is very interesting"+
                        "2.定语从句（Attributive Clause）: \"that I borrowed from the library\""+
                        "关系代词（Relative Pronoun）: \"that\"（指代 \"the book\"）"+
                        "从句内容: \"I borrowed from the library\"（说明是哪本书）\n\n"+

                        "▲非谓语动词结构:\n" +
                        "不定式(to do)、分词结构: 现在分词/过去分词、动名词(doing)的功能、意义和结构\n" +
                        "原文:Swimming is good for health.（游泳对健康有益。）\n" +
                        "结构:动名词是动词 + -ing 形式，在句子中起名词的作用，可以作主语、宾语、表语 等\n" +
                        "\"Swimming\"（游泳）是动名词，作主语。"+

                        "▲特殊语法结构:\n" +
                        "被动语态、倒装句、强调句、虚拟语气的含义和用法\n" +
                        "含义:虚拟语气用于表达 假设、愿望、建议、命令 等非现实的情况，通常与事实相反或尚未发生。\n" +
                        "原文:If I were rich, I would travel around the world.（如果我有钱，我就会环游世界。）"+
                        "事实: 我现在没钱，所以不能环游世界。"+
                        "虚拟: 用 were（所有人称都用 were，不用 was）表示假设。"+

                        "▲固定搭配:\n" +
                        "look forward to:期望"+

                        "▲俚语:\n" +
                        "raining cats and dogs:下着倾盆大雨"
                        , text);
    }

    /**
     * 调用DashScope API进行单词分析（支持多语言）
     */
    public String analyzeWord(String word, String language) throws IOException {
        logger.info("使用 Mistral AI 解析单词: word={}, language={}", word, language);

        try {
            String prompt;
            if ("english".equalsIgnoreCase(language)) {
                prompt = createEnglishWordPrompt(word);
            } else {
                prompt = createJapaneseWordPrompt(word);
            }

            String result = chatModel.call(prompt);

            if (result == null || result.trim().isEmpty()) {
                result = "无返回分析内容";
            } else {
                result = result.replace(':', ':').replace("「", "").replace("」", "");
            }

            logger.debug("Mistral 单词解析返回: {}", result);

            return result;
        } catch (Exception e) {
            logger.error("Mistral AI 单词解析调用失败: word={}, language={}", word, language, e);
            throw new IOException("单词解析失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建日语单词分析prompt
     */
    private String createJapaneseWordPrompt(String word) {
        return String.format(
                "你是一个专业的日语语言分析助手，擅长将日语单词进行细致解析。请严格按以下结构输出分析结果！！！\n" +
                        "重要约束：\n" +
                        "1. 只有汉字单词需要注音，片假名单词不需要注音\n" +
                        "2. 含义字段只输出单词的基本中文含义，要简洁明了\n" +
                        "3. 中文翻译字段只输出例文的中文翻译，不要输出单词本身的含义\n" +
                        "4. 严格按照指定格式输出，不要偏离\n\n" +
                        "目标单词:%s\n\n" +
                        "格式为:\n\n" +
                        "单词（假名）:【词性（如一段动词/五段动词、自动词/他动词、形容词/形容动词、助词、名词等）】\n" +
                        "含义:（单词的基本中文含义，简洁明了）\n" +
                        "例文:（句子长度不少于20个字，体现日常对话中真实的场景）\n" +
                        "中文翻译:（上述例文的中文翻译，不是单词含义）\n\n" +
                        "示例参考:\n" +
                        "学校（がっこう）:【名词】\n" +
                        "含义:学校\n" +
                        "例文:毎日学校に行って、友達と一緒に勉強しています。\n" +
                        "中文翻译:每天去学校，和朋友们一起学习。\n\n", word
        );
    }
    
    /**
     * 创建英语单词分析prompt
     */
    private String createEnglishWordPrompt(String word) {
        return String.format(
                "你是一个专业的英语语言分析助手，擅长将英语单词进行细致解析。请严格按以下结构输出分析结果！！！\n" +
                        "重要要求:请使用纯文本格式，不要使用任何markdown标记（如**、*、#等），直接输出文本内容。\n" +
                        "目标单词:%s\n\n" +
                        "格式为:\n\n" +
                        "write /raɪt/:【词性（如动词/名词/形容词/副词/介词/连词等）】\n" +
                        "含义:（提供主要含义和常用含义）\n" +
                        "例文:（句子长度不少于15个单词，体现日常对话中真实的场景）\n" +
                        "中文翻译:\n" +
                        "词根词缀:（如果适用，解释词根、前缀、后缀）\n" +
                        "同义词:（提供2-3个同义词）\n" +
                        "反义词:（如果适用，提供1-2个反义词）\n\n", word
        );
    }
    
    /**
     * 生成关联内容（支持多语言）
     */
    public String generateAssociations(String keyword, String type, String language) throws IOException {
        logger.info("使用 Mistral AI 生成关联内容: keyword={}, type={}, language={}", keyword, type, language);

        try {
            String prompt;
            if ("english".equalsIgnoreCase(language)) {
                prompt = createEnglishAssociationPrompt(keyword, type);
            } else {
                prompt = createJapaneseAssociationPrompt(keyword, type);
            }

            String result = chatModel.call(prompt);

            if (result == null || result.trim().isEmpty()) {
                result = "无返回内容";
            } else {
                result = result.replace(':', ':').replace("「", "").replace("」", "");
            }

            logger.debug("Mistral 联想结果: {}", result);

            return result;
        } catch (Exception e) {
            logger.error("Mistral AI 联想调用失败: keyword={}, type={}, language={}", keyword, type, language, e);
            throw new IOException("联想失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建日语联想prompt
     */
    private String createJapaneseAssociationPrompt(String keyword, String type) {
        if ("word".equalsIgnoreCase(type)) {
            return String.format(
                "你是一个专业的日语词汇联想助手，擅长根据给定的日语单词提供相关联的词汇。请严格按以下结构输出分析结果！！！\n" +
                "重要约束：\n" +
                "1. 不要加序号\n" +
                "2. 严格按照指定格式输出，不要偏离\n" +
                "3. 含义字段只输出单词的基本中文含义，要简洁明了\n" +
                "4. 中文翻译字段只输出例文的中文翻译，不要输出单词本身的含义\n" +
                "5. 只有汉字单词需要注音，片假名单词不需要注音\n\n" +
                "输入单词:%s\n\n" +
                "请提供5个与该单词最相关的日语单词，每个单词都按以下格式详细解析:\n\n" +
                "▲"+"单词（假名）:【词性（如一段动词/五段动词、自动词/他动词、形容词/形容动词、助词、名词等）】\n" +
                "含义:（单词的基本中文含义，简洁明了）\n" +
                "例文:（句子长度不少于20个字，体现日常对话中真实的场景）\n" +
                "中文翻译:（上述例文的中文翻译，不是单词含义）\n\n" +
                "示例参考:\n" +
                "▲学校（がっこう）:【名词】\n" +
                "含义:学校\n" +
                "例文:毎日学校に行って、友達と一緒に勉強しています。\n" +
                "中文翻译:每天去学校，和朋友们一起学习。\n\n" +
                "确保每个相关单词都是真实存在的日语单词，并与输入单词有明确的语义关联。", keyword
            );
        } else {
            return String.format(
                "你是一个专业的日语语法联想助手，擅长根据给定的日语语法点提供相关联的语法知识。请严格按以下结构输出分析结果！！！\n" +
                "重要约束：\n" +
                "1. 不要加序号，只输出语法内容\n" +
                "2. 例文字段要提供展示该语法用法的例句\n" +
                "3. 中文翻译字段只输出例文的中文翻译\n" +
                "4. 注意字段要说明使用场景和与其他语法的区别\n" +
                "5. 严格按照指定格式输出\n\n" +
                "输入语法:%s\n\n" +
                "请提供3个与该语法最相关的日语语法点，每个语法点都按以下格式详细解析:\n\n" +
                "▲语法点:\n" +
                "语法分析:\n" +
                "例文:（句子长度不少于20个字，体现日常对话中真实的场景）\n" +
                "中文翻译:（上述例文的中文翻译）\n" +
                "注意:（使用场景/与易混淆的其他语法的区别）\n\n" +
                "示例参考:\n" +
                "▲〜ている:\n" +
                "语法分析:动词て形+いる，表示动作的进行状态或结果状态\n" +
                "例文:今、友達と電话で話しています。\n" +
                "中文翻译:现在正在和朋友打电话。\n" +
                "注意:用于表示动作正在进行或状态持续，区别于单纯的动词现在形\n\n" +
                "确保每个相关语法都是真实存在的日语语法点，并与输入语法有明确的语义或用法关联。", keyword
            );
        }
    }
    
    /**
     * 创建英语联想prompt
     */
    private String createEnglishAssociationPrompt(String keyword, String type) {
        if ("word".equalsIgnoreCase(type)) {
            return String.format(
                "你是一个专业的英语词汇联想助手，擅长根据给定的英语单词提供相关联的词汇。请严格按以下结构输出分析结果！！！不要加序号\n" +
                "重要要求:请使用纯文本格式，不要使用任何markdown标记（如**、*、#等），直接输出文本内容。\n\n" +
                "输入单词:%s\n\n" +
                "请提供5个与该单词最相关的英语单词（不含该单词），每个单词都按以下格式详细解析:\n\n" +
                "▲"+"write /raɪt/:【（如动词/名词/形容词/副词/介词/连词等）】\n" +
                "含义:（提供主要含义和常用含义）\n" +
                "例文:（句子长度不少于15个单词，体现日常对话中真实的场景）\n" +
                "中文翻译:（提供对应例文的中文翻译）\n" +
                "词根词缀:（如果适用，解释词根、前缀、后缀）\n" +
                "同义词:（提供2-3个同义词）\n" +
                "反义词:（如果适用，提供1-2个反义词）\n\n" +
                "请确保每个相关单词都是真实存在的英语单词，并与输入单词有明确的语义关联。", keyword
            );
        } else {
            return String.format(
                "你是一个专业的英语短语联想助手，擅长根据给定的英语短语提供相关联的短语知识。请严格按以下结构输出分析结果！！！不要加序号\n" +
                "重要要求:请使用纯文本格式，不要使用任何markdown标记（如**、*、#等），直接输出文本内容。\n\n" +
                "输入短语:%s\n\n" +
                "请提供3个与该短语最相关的英语短语（不含该短语），每个短语都按以下格式详细解析:\n\n" +
                "▲"+"give something a twist:\n" +
                "含义:（用中文详细解释这个短语的含义和用法）\n" +
                "例文:（例文不少于15个单词，体现日常对话或书面语中的真实使用场景）\n" +
                "中文翻译:（提供对应例文的中文翻译）\n\n" +
                "请确保每个相关短语都是真实存在的英语短语，并与输入短语有明确的语义或用法关联。", keyword
            );
        }
    }
    
    /**
     * 分析英语固定搭配或俚语
     */
    public String analyzeEnglishPhrase(String phrase) throws IOException {
        logger.info("使用 Mistral AI 分析英语短语: phrase={}", phrase);

        try {
            String prompt = createEnglishPhrasePrompt(phrase);

            String result = chatModel.call(prompt);

            if (result == null || result.trim().isEmpty()) {
                result = "无返回分析内容";
            } else {
                result = result.replace(':', ':').replace("「", "").replace("」", "");
            }

            logger.debug("Mistral 英语短语分析返回: {}", result);

            return result;
        } catch (Exception e) {
            logger.error("Mistral AI 英语短语分析调用失败: phrase={}", phrase, e);
            throw new IOException("英语短语分析失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建英语短语分析prompt
     */
    private String createEnglishPhrasePrompt(String phrase) {
        return String.format(
                "你是一个专业的英语语言分析助手，擅长分析英语固定搭配和俚语。请严格按以下结构输出分析结果！！！\n" +
                        "重要要求:请使用纯文本格式，不要使用任何markdown标记（如**、*、#等），直接输出文本内容。不许生成多个短语含义、例句、中文翻译，一个足够！\n" +
                        "目标短语:%s\n\n" +
                        "请按以下格式详细分析这个固定搭配或俚语:\n\n" +
                        "含义:\n" +
                        "（用中文详细解释这个短语的含义和用法）\n\n" +
                        "例文:\n" +
                        "（例文不少于15个单词，体现日常对话或书面语中的真实使用场景）\n\n" +
                        "中文翻译:\n" +
                        "（提供对应例句的中文翻译）\n\n" , phrase);
    }
} 