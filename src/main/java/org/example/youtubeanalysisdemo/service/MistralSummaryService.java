package org.example.youtubeanalysisdemo.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.mistralai.MistralAiChatOptions;
import org.springframework.stereotype.Service;

/**
 * Mistral AI摘要服务
 * 使用Spring AI ChatModel调用Mistral AI生成视频摘要
 */
@Service
public class MistralSummaryService {

    private static final Logger logger = LoggerFactory.getLogger(MistralSummaryService.class);

    private final ChatModel chatModel;

    public MistralSummaryService(ChatModel chatModel) {
        this.chatModel = chatModel;
    }

    /**
     * 使用Mistral AI生成视频摘要
     * @param videoTitle 视频标题
     * @param subtitleText 字幕文本
     * @return 生成的摘要
     */
    public String generateSummaryFromSubtitles(String videoTitle, String subtitleText) {
        logger.info("使用Mistral AI生成摘要: videoTitle={}", videoTitle);
        
        try {
            // 限制字幕文本长度，避免超出API限制
            String limitedText = subtitleText;
            if (subtitleText.length() > 9000) {
                limitedText = subtitleText.substring(0, 9000);
                logger.info("字幕文本过长，已截断至9000字符");
            }
            
            String promptText = String.format("""
                你是一个专业的视频内容摘要助手。请根据以下字幕内容，为视频生成一个简洁但信息丰富的中文摘要。
                
                要求：
                1. 必须使用中文输出摘要
                2. 摘要字数控制在200-300字之间
                3. 突出视频的主要内容、核心观点和重要信息
                4. 使用简明扼要的语言，不要过度修饰
                5. 摘要仅包含视频内容，不要添加评价或建议
                6. 保持客观，不包含个人观点
                
                视频标题: %s
                
                字幕内容: %s
                
                请直接输出中文摘要内容，不要包含任何额外的前缀或解释。
                """, videoTitle, limitedText);
            
            String result = chatModel.call(promptText);
            
            if (result == null || result.trim().isEmpty()) {
                result = "无法生成视频摘要";
            }
            
            logger.info("Mistral AI摘要生成成功: {}", result);
            return result.trim();
            
        } catch (Exception e) {
            logger.error("调用Mistral AI生成摘要失败", e);
            throw new RuntimeException("生成视频摘要失败: " + e.getMessage(), e);
        }
    }
} 