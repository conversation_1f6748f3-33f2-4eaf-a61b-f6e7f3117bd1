package org.example.youtubeanalysisdemo.service;

import edu.stanford.nlp.ling.CoreAnnotations;
import edu.stanford.nlp.ling.CoreLabel;
import edu.stanford.nlp.pipeline.Annotation;
import edu.stanford.nlp.pipeline.StanfordCoreNLP;
import edu.stanford.nlp.util.CoreMap;
import lombok.extern.slf4j.Slf4j;
import org.example.youtubeanalysisdemo.model.TaggedWord;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * 英语词性标注服务
 * 使用Stanford CoreNLP进行英语文本的词性标注和词形还原
 */
@Slf4j
@Service
public class EnglishPosTaggingService {

    private StanfordCoreNLP pipeline;

    @PostConstruct
    public void init() {
        try {
            log.info("初始化Stanford CoreNLP英语词性标注管道...");
            Properties props = new Properties();
            // 使用默认的英语模型，不指定具体路径
            props.setProperty("annotators", "tokenize,ssplit,pos,lemma");
            props.setProperty("tokenize.language", "en");
            
            this.pipeline = new StanfordCoreNLP(props);
            log.info("Stanford CoreNLP英语词性标注管道初始化完成");
        } catch (Exception e) {
            log.error("初始化Stanford CoreNLP失败", e);
            throw new RuntimeException("无法初始化英语词性标注服务", e);
        }
    }

    /**
     * 对英语文本进行词性标注
     * @param text 要标注的英语文本
     * @return 标注结果列表
     */
    public List<TaggedWord> tagText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return new ArrayList<>();
        }

        List<TaggedWord> taggedWords = new ArrayList<>();
        
        try {
            // 创建文档注释
            Annotation document = new Annotation(text);
            
            // 运行管道
            pipeline.annotate(document);
            
            // 提取句子
            List<CoreMap> sentences = document.get(CoreAnnotations.SentencesAnnotation.class);
            
            for (CoreMap sentence : sentences) {
                // 提取词汇
                List<CoreLabel> tokens = sentence.get(CoreAnnotations.TokensAnnotation.class);
                
                // 预处理：合并复合形容词
                List<CoreLabel> processedTokens = mergeCompoundAdjectives(tokens);
                
                for (int i = 0; i < processedTokens.size(); i++) {
                    CoreLabel token = processedTokens.get(i);
                    String word = token.get(CoreAnnotations.TextAnnotation.class);
                    String pos = token.get(CoreAnnotations.PartOfSpeechAnnotation.class);
                    String lemma = token.get(CoreAnnotations.LemmaAnnotation.class);
                    
                    // 应用上下文相关的词性修正
                    String correctedPos = applyContextualCorrections(processedTokens, i, pos);
                    
                    // 简化词性标签
                    String simplifiedPos = simplifyEnglishPos(correctedPos);
                    
                    // 对于英语，不提供reading信息，只有日语才需要读音
                    String reading = null;
                    
                    // 调试输出：显示原始词性标签（仅在debug级别）
                    log.debug("词汇: '{}', 原始词性: '{}', 修正词性: '{}', 简化词性: '{}', 词形还原: '{}'", 
                             word, pos, correctedPos, simplifiedPos, lemma);
                    
                    taggedWords.add(new TaggedWord(word, simplifiedPos, reading));
                }
            }
            
            log.debug("英语词性标注完成，处理了 {} 个词汇", taggedWords.size());
            
        } catch (Exception e) {
            log.error("英语词性标注失败: {}", text, e);
            // 如果标注失败，返回简单的分词结果
            String[] words = text.split("\\s+");
            for (String word : words) {
                if (!word.trim().isEmpty()) {
                    taggedWords.add(new TaggedWord(word.trim(), "other", null));
                }
            }
        }
        
        return taggedWords;
    }

    /**
     * 应用上下文相关的词性修正
     * 处理一些Stanford CoreNLP可能判断不准确的情况
     * @param tokens 句子中的所有词汇
     * @param currentIndex 当前词汇的索引
     * @param originalPos 原始词性标签
     * @return 修正后的词性标签
     */
    private String applyContextualCorrections(List<CoreLabel> tokens, int currentIndex, String originalPos) {
        if (currentIndex >= tokens.size()) {
            return originalPos;
        }
        
        CoreLabel currentToken = tokens.get(currentIndex);
        String currentWord = currentToken.get(CoreAnnotations.TextAnnotation.class);
        String currentLemma = currentToken.get(CoreAnnotations.LemmaAnnotation.class);
        
        // 规则1: 处理 -ing 形式的词汇在名词前的情况
        // 如 "drinking glass", "running water", "sleeping bag" 等
        if ("NN".equals(originalPos) && currentWord.endsWith("ing") && currentIndex < tokens.size() - 1) {
            CoreLabel nextToken = tokens.get(currentIndex + 1);
            String nextPos = nextToken.get(CoreAnnotations.PartOfSpeechAnnotation.class);
            
            // 如果下一个词是名词，那么当前的-ing词很可能是形容词性的修饰语
            if (nextPos != null && (nextPos.startsWith("NN") || nextPos.equals("NN"))) {
                log.debug("上下文修正: '{}' 从 NN 修正为 JJ (修饰后面的名词)", currentWord);
                return "JJ"; // 修正为形容词
            }
        }
        
        // 规则2: 处理过去分词作为形容词的情况
        // 如 "broken glass", "used car" 等
        if ("VBN".equals(originalPos) && currentIndex < tokens.size() - 1) {
            CoreLabel nextToken = tokens.get(currentIndex + 1);
            String nextPos = nextToken.get(CoreAnnotations.PartOfSpeechAnnotation.class);
            
            // 如果下一个词是名词，过去分词很可能是形容词
            if (nextPos != null && nextPos.startsWith("NN")) {
                log.debug("上下文修正: '{}' 从 VBN 修正为 JJ (修饰后面的名词)", currentWord);
                return "JJ"; // 修正为形容词
            }
        }
        
        // 规则3: 处理现在分词作为形容词的情况
        // 如 "interesting book", "exciting news" 等
        // 但要排除进行时态的情况
        if ("VBG".equals(originalPos) && currentIndex < tokens.size() - 1) {
            CoreLabel nextToken = tokens.get(currentIndex + 1);
            String nextPos = nextToken.get(CoreAnnotations.PartOfSpeechAnnotation.class);
            
            // 检查前面是否有助动词（be动词），如果有则可能是进行时态
            boolean hasAuxiliaryBefore = false;
            if (currentIndex > 0) {
                CoreLabel prevToken = tokens.get(currentIndex - 1);
                String prevPos = prevToken.get(CoreAnnotations.PartOfSpeechAnnotation.class);
                String prevWord = prevToken.get(CoreAnnotations.TextAnnotation.class).toLowerCase();
                
                // 检查是否是be动词或其他助动词
                if ("VBP".equals(prevPos) || "VBZ".equals(prevPos) || "VBD".equals(prevPos) || 
                    "am".equals(prevWord) || "is".equals(prevWord) || "are".equals(prevWord) || 
                    "was".equals(prevWord) || "were".equals(prevWord) || "been".equals(prevWord)) {
                    hasAuxiliaryBefore = true;
                }
            }
            
            // 如果下一个词是名词，且前面没有助动词，现在分词很可能是形容词
            if (nextPos != null && nextPos.startsWith("NN") && !hasAuxiliaryBefore) {
                log.debug("上下文修正: '{}' 从 VBG 修正为 JJ (修饰后面的名词)", currentWord);
                return "JJ"; // 修正为形容词
            }
        }
        

        
        return originalPos; // 没有修正，返回原始词性
    }

    /**
     * 合并复合形容词
     * 处理像 "well-known", "state-of-the-art" 等复合形容词
     * @param tokens 原始token列表
     * @return 处理后的token列表
     */
    private List<CoreLabel> mergeCompoundAdjectives(List<CoreLabel> tokens) {
        List<CoreLabel> result = new ArrayList<>();
        
        for (int i = 0; i < tokens.size(); i++) {
            CoreLabel currentToken = tokens.get(i);
            String currentWord = currentToken.get(CoreAnnotations.TextAnnotation.class);
            String currentPos = currentToken.get(CoreAnnotations.PartOfSpeechAnnotation.class);
            
            // 检查是否是复合形容词的开始
            if (i < tokens.size() - 2) {
                CoreLabel nextToken = tokens.get(i + 1);
                CoreLabel thirdToken = tokens.get(i + 2);
                
                String nextWord = nextToken.get(CoreAnnotations.TextAnnotation.class);
                String thirdWord = thirdToken.get(CoreAnnotations.TextAnnotation.class);
                String nextPos = nextToken.get(CoreAnnotations.PartOfSpeechAnnotation.class);
                String thirdPos = thirdToken.get(CoreAnnotations.PartOfSpeechAnnotation.class);
                
                // 检查模式：word-word (如 well-known)
                if ("-".equals(nextWord) || "HYPH".equals(nextPos)) {
                    // 检查是否是常见的复合形容词模式
                    if (isCompoundAdjectivePattern(currentWord, thirdWord, currentPos, thirdPos)) {
                        // 创建合并的token
                        CoreLabel mergedToken = new CoreLabel();
                        String mergedWord = currentWord + "-" + thirdWord;
                        mergedToken.set(CoreAnnotations.TextAnnotation.class, mergedWord);
                        mergedToken.set(CoreAnnotations.PartOfSpeechAnnotation.class, "JJ"); // 设为形容词
                        mergedToken.set(CoreAnnotations.LemmaAnnotation.class, mergedWord);
                        
                        result.add(mergedToken);
                        log.debug("合并复合形容词: '{}' + '-' + '{}' -> '{}'", currentWord, thirdWord, mergedWord);
                        
                        // 跳过连字符和第三个词
                        i += 2;
                        continue;
                    }
                }
            }
            
            // 如果不是复合形容词，直接添加当前token
            result.add(currentToken);
        }
        
        return result;
    }

    /**
     * 判断是否是复合形容词模式
     * @param firstWord 第一个词
     * @param secondWord 第二个词
     * @param firstPos 第一个词的词性
     * @param secondPos 第二个词的词性
     * @return 是否是复合形容词
     */
    private boolean isCompoundAdjectivePattern(String firstWord, String secondWord, String firstPos, String secondPos) {
        // 常见的复合形容词模式
        String[] commonCompoundAdjectives = {
            "well-known", "well-established", "well-defined", "well-documented",
            "high-quality", "high-level", "high-tech", "high-end",
            "low-cost", "low-level", "low-key",
            "long-term", "short-term", "long-range", "short-range",
            "state-of-the-art", "up-to-date", "out-of-date",
            "self-contained", "self-evident", "self-sufficient",
            "user-friendly", "cost-effective", "time-consuming",
            "hard-working", "good-looking", "fast-moving"
        };
        
        String compound = firstWord.toLowerCase() + "-" + secondWord.toLowerCase();
        
        // 检查是否在常见复合形容词列表中
        for (String commonAdj : commonCompoundAdjectives) {
            if (commonAdj.equals(compound)) {
                return true;
            }
        }
        
        // 检查一些通用模式
        // 副词 + 过去分词/现在分词 (如 well-known, fast-moving)
        if (("RB".equals(firstPos) || "RBR".equals(firstPos) || "RBS".equals(firstPos)) &&
            ("VBN".equals(secondPos) || "VBG".equals(secondPos) || "JJ".equals(secondPos))) {
            return true;
        }
        
        // 形容词 + 名词 (如 high-quality, long-term)
        if (("JJ".equals(firstPos) || "JJR".equals(firstPos) || "JJS".equals(firstPos)) &&
            ("NN".equals(secondPos) || "NNS".equals(secondPos))) {
            return true;
        }
        
        return false;
    }

    /**
     * 简化英语词性标签，映射到与日语一致的类别
     * @param pennPos Penn Treebank词性标签
     * @return 简化的词性标签
     */
    private String simplifyEnglishPos(String pennPos) {
        if (pennPos == null) {
            return "other";
        }
        
        return switch (pennPos.toUpperCase()) {
            // 名词类
            case "NN", "NNS", "NNP", "NNPS" -> "noun";
            
            // 动词类
            case "VB", "VBD", "VBG", "VBN", "VBP", "VBZ" -> "verb";
            
            // 形容词类
            case "JJ", "JJR", "JJS" -> "adjective";
            
            // 副词类
            case "RB", "RBR", "RBS" -> "adverb";
            
            // 代词类
            case "PRP", "PRP$", "WP", "WP$" -> "pron";
            
            // 介词类（在英语中相当于日语的助词）
            case "IN", "TO" -> "particle";
            
            // 助动词类
            case "MD" -> "aux";
            
            // 连词类
            case "CC", "WRB" -> "conj";
            
            // 感叹词类
            case "UH" -> "interjection";
            
            // 标点符号
            case ".", ",", ":", ";", "!", "?", "``", "''", "(", ")", "-LRB-", "-RRB-" -> "punctuation";
            
            // 限定词（冠词等）
            case "DT", "PDT", "WDT" -> "other";
            
            // 数词
            case "CD" -> "other";
            
            // 其他
            default -> "other";
        };
    }
    
    /**
     * 将英语短语中的词汇还原为原形
     * 用于固定搭配和短语的标准化处理
     * @param phrase 英语短语
     * @return 还原后的短语
     */
    public String lemmatizePhrase(String phrase) {
        if (phrase == null || phrase.trim().isEmpty()) {
            return phrase;
        }
        
        try {
            log.debug("开始对短语进行词形还原: {}", phrase);
            
            // 创建文档注释
            Annotation document = new Annotation(phrase);
            
            // 运行管道
            pipeline.annotate(document);
            
            // 提取句子
            List<CoreMap> sentences = document.get(CoreAnnotations.SentencesAnnotation.class);
            
            StringBuilder lemmatizedPhrase = new StringBuilder();
            
            for (CoreMap sentence : sentences) {
                // 提取词汇
                List<CoreLabel> tokens = sentence.get(CoreAnnotations.TokensAnnotation.class);
                
                for (int i = 0; i < tokens.size(); i++) {
                    CoreLabel token = tokens.get(i);
                    String word = token.get(CoreAnnotations.TextAnnotation.class);
                    String pos = token.get(CoreAnnotations.PartOfSpeechAnnotation.class);
                    String lemma = token.get(CoreAnnotations.LemmaAnnotation.class);
                    
                    log.debug("处理词汇: word='{}', pos='{}', lemma='{}'", word, pos, lemma);
                    
                    // 判断是否需要进行词形还原
                    boolean shouldLemmatize = false;
                    String reason = "";
                    
                    if (pos != null) {
                        // 动词类：所有动词变位都需要还原
                        if (pos.startsWith("VB")) {
                            shouldLemmatize = true;
                            reason = "动词变位";
                        }
                        // 名词类：复数形式需要还原
                        else if (pos.equals("NNS") || pos.equals("NNPS")) {
                            shouldLemmatize = true;
                            reason = "名词复数";
                        }
                        // 形容词类：比较级和最高级需要还原
                        else if (pos.equals("JJR") || pos.equals("JJS")) {
                            shouldLemmatize = true;
                            reason = "形容词比较级";
                        }
                        // 副词类：比较级和最高级需要还原
                        else if (pos.equals("RBR") || pos.equals("RBS")) {
                            shouldLemmatize = true;
                            reason = "副词比较级";
                        }
                        // 特殊情况：动名词和现在分词（以-ing结尾）
                        else if (word.endsWith("ing") && lemma != null && !lemma.equals(word)) {
                            shouldLemmatize = true;
                            reason = "动名词/现在分词";
                        }
                        // 特殊情况：过去分词（以-ed结尾）
                        else if (word.endsWith("ed") && lemma != null && !lemma.equals(word)) {
                            shouldLemmatize = true;
                            reason = "过去分词";
                        }
                    }
                    
                    // 进行词形还原
                    if (shouldLemmatize && lemma != null && !lemma.equals(word)) {
                        // 特殊处理一些不应该还原的情况
                        if (shouldKeepOriginalForm(word, pos, phrase)) {
                            lemmatizedPhrase.append(word);
                            log.debug("保持原形: {} ({})", word, pos);
                        } else {
                            lemmatizedPhrase.append(lemma);
                            log.debug("词形还原: {} ({}) -> {} ({})", word, pos, lemma, reason);
                        }
                    } else {
                        // 不需要还原或无法还原，保持原样
                        lemmatizedPhrase.append(word);
                        if (lemma != null && lemma.equals(word)) {
                            log.debug("无需还原: {} ({})", word, pos);
                        } else {
                            log.debug("保持原样: {} ({})", word, pos);
                        }
                    }
                    
                    // 添加空格（除了最后一个词）
                    if (i < tokens.size() - 1) {
                        lemmatizedPhrase.append(" ");
                    }
                }
            }
            
            String result = lemmatizedPhrase.toString().trim();
            log.debug("短语词形还原完成: {} -> {}", phrase, result);
            return result;
            
        } catch (Exception e) {
            log.error("短语词形还原失败: {}", phrase, e);
            return phrase; // 失败时返回原始短语
        }
    }
    
    /**
     * 判断是否应该保持动词的原始形式
     * 某些固定搭配中的动词形式不应该被还原
     * @param word 原始词汇
     * @param pos 词性标签
     * @param phrase 完整短语
     * @return 是否保持原始形式
     */
    private boolean shouldKeepOriginalForm(String word, String pos, String phrase) {
        // 一些固定搭配中的特殊形式不应该还原
        String[] fixedForms = {
            "taken aback",      // 不应该还原为 "take aback"
            "well-known",       // 过去分词作为形容词
            "hard-pressed",     // 过去分词作为形容词
            "worn out",         // 固定的过去分词形式
            "fed up",           // 固定的过去分词形式
            "broken down",      // 在某些语境下是固定形式
            "used to"           // 固定搭配
        };
        
        String lowerPhrase = phrase.toLowerCase();
        for (String fixedForm : fixedForms) {
            if (lowerPhrase.contains(fixedForm)) {
                return true;
            }
        }
        
        // 如果是过去分词且在短语末尾，可能是固定形式
        if ("VBN".equals(pos) && phrase.toLowerCase().endsWith(word.toLowerCase())) {
            // 检查是否是常见的固定过去分词结尾
            String[] fixedEndings = {"taken", "given", "broken", "spoken", "written", "driven"};
            for (String ending : fixedEndings) {
                if (word.toLowerCase().equals(ending)) {
                    return true;
                }
            }
        }
        
        return false;
    }
} 