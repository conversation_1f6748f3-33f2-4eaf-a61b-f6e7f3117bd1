package org.example.youtubeanalysisdemo.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
public class MistralDialogService {

    private static final Logger logger = LoggerFactory.getLogger(MistralDialogService.class);

    private final ChatModel chatModel;

    public MistralDialogService(ChatModel chatModel) {
        this.chatModel = chatModel;
    }

    public String generateDialog(String scenario, String language) throws IOException {
        logger.info("使用Mistral AI生成对话: scenario={}, language={}", scenario, language);

        try {
            String promptText;
            if ("english".equals(language)) {
                promptText = createEnglishDialogPrompt(scenario);
            } else {
                promptText = createJapaneseDialogPrompt(scenario);
            }

            String result = chatModel.call(promptText);

            if (result == null || result.trim().isEmpty()) {
                result = "无法生成对话内容";
            }

            logger.info("Mistral AI对话生成成功");
            return result.trim();

        } catch (Exception e) {
            logger.error("调用Mistral AI生成对话失败", e);
            throw new IOException("调用Mistral AI生成对话失败: " + e.getMessage(), e);
        }
    }

    private String createJapaneseDialogPrompt(String scenario) {
        return String.format(
                "你现在需要模拟一个真实的日语对话场景。以下是生成要求:\n\n" +
                        "场景描述:%s\n\n" +
                        "请生成一段日语对话，符合以下要求:\n" +
                        "1. 对话应该包含15-25句话，一问一答形式，第一人称称为A，第二人称称为B\n" +
                        "2. 对话应该非常贴合日本人的真实用语习惯和口语表达\n" +
                        "3. 对话包含多种日常使用的词汇、语法和表达方式\n" +
                        "4. 所有对话需要包含日文原文、假名标注和中文翻译\n\n" +
                        "输出格式严格遵循如下格式，用###分隔每一轮对话:\n" +
                        "A: 日文原文|假名标注|中文翻译\n" +
                        "B: 日文原文|假名标注|中文翻译\n" +
                        "###\n" +
                        "A: 日文原文|假名标注|中文翻译\n" +
                        "B: 日文原文|假名标注|中文翻译\n" +
                        "###\n" +
                        "......\n\n" +
                        "请确保日文原文、假名和中文之间用|分隔，每一轮对话用###分隔。不要有任何其他多余解释。",
                scenario
        );
    }

    private String createEnglishDialogPrompt(String scenario) {
        return String.format(
                "你现在需要模拟一个真实的英语对话场景。以下是生成要求:\n\n" +
                        "场景描述:%s\n\n" +
                        "请生成一段英语对话，符合以下要求:\n" +
                        "1. 对话应该包含15-25句话，一问一答形式，第一人称称为A，第二人称称为B\n" +
                        "2. 对话应该非常贴合英语母语者的真实用语习惯和口语表达\n" +
                        "3. 对话包含多种日常使用的词汇、语法和表达方式，包括常见的固定搭配和俚语\n" +
                        "4. 所有对话需要包含英文原文、音标标注和中文翻译\n\n" +
                        "输出格式严格遵循如下格式，用###分隔每一轮对话:\n" +
                        "A: 英文原文|音标标注|中文翻译\n" +
                        "B: 英文原文|音标标注|中文翻译\n" +
                        "###\n" +
                        "A: 英文原文|音标标注|中文翻译\n" +
                        "B: 英文原文|音标标注|中文翻译\n" +
                        "###\n" +
                        "......\n\n" +
                        "请确保英文原文、音标和中文之间用|分隔，每一轮对话用###分隔。音标请使用国际音标格式。不要有任何其他多余解释。",
                scenario
        );
    }
} 