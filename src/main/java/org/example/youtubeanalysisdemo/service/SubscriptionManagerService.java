package org.example.youtubeanalysisdemo.service;

import org.example.youtubeanalysisdemo.entity.Subscription;
import org.example.youtubeanalysisdemo.repository.SubscriptionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class SubscriptionManagerService {

    private static final Logger logger = LoggerFactory.getLogger(SubscriptionManagerService.class);

    @Autowired
    private SubscriptionRepository subscriptionRepository;

    /**
     * 定时任务，每小时检查一次订阅状态
     * 1. 将应该开始的pending订阅改为active
     * 2. 将已过期的active订阅改为expired
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // 每小时执行一次（毫秒）
    @Transactional
    public void updateSubscriptionStatuses() {
        LocalDateTime now = LocalDateTime.now();
        
        // 1. 激活应该开始的订阅
        List<Subscription> subscriptionsToActivate = 
                subscriptionRepository.findSubscriptionsToActivate(now);
        
        for (Subscription subscription : subscriptionsToActivate) {
            subscription.setStatus("active");
            subscription.setUpdatedAt(now);
            subscriptionRepository.save(subscription);
            logger.info("激活订阅: id={}, userId={}, planName={}，开始时间={}",
                    subscription.getId(), subscription.getUserId(),
                    subscription.getPlanName(), subscription.getStartDate());
        }
        
        // 2. 过期已经结束的订阅
        List<Subscription> subscriptionsToExpire = 
                subscriptionRepository.findSubscriptionsToExpire(now);
        
        for (Subscription subscription : subscriptionsToExpire) {
            subscription.setStatus("expired");
            subscription.setUpdatedAt(now);
            subscriptionRepository.save(subscription);
            logger.info("过期订阅: id={}, userId={}, planName={}，结束时间={}",
                    subscription.getId(), subscription.getUserId(),
                    subscription.getPlanName(), subscription.getNextBillDate());
        }
    }
    
    /**
     * 手动更新指定用户的订阅状态
     */
    @Transactional
    public void updateUserSubscriptionStatuses(String userId) {
        LocalDateTime now = LocalDateTime.now();
        
        // 获取该用户的所有订阅
        List<Subscription> allSubscriptions = subscriptionRepository.findByUserId(userId);
        
        for (Subscription subscription : allSubscriptions) {
            // 检查pending订阅是否应该激活
            if ("pending".equals(subscription.getStatus()) && 
                subscription.getStartDate() != null && 
                subscription.getStartDate().isBefore(now)) {
                
                subscription.setStatus("active");
                subscription.setUpdatedAt(now);
                logger.info("激活用户订阅: id={}, userId={}, planName={}",
                        subscription.getId(), subscription.getUserId(), subscription.getPlanName());
            }
            
            // 检查active订阅是否应该过期
            else if ("active".equals(subscription.getStatus()) && 
                    subscription.getNextBillDate() != null && 
                    subscription.getNextBillDate().isBefore(now)) {
                
                subscription.setStatus("expired");
                subscription.setUpdatedAt(now);
                logger.info("过期用户订阅: id={}, userId={}, planName={}",
                        subscription.getId(), subscription.getUserId(), subscription.getPlanName());
            }
        }
        
        subscriptionRepository.saveAll(allSubscriptions);
    }
} 