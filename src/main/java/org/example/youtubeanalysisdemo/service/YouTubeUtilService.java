package org.example.youtubeanalysisdemo.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * YouTube相关工具服务
 * 提供URL验证、视频ID提取等基础功能
 */
@Slf4j
@Service
public class YouTubeUtilService {

    /**
     * 验证是否为有效的YouTube URL
     * @param url 待验证的URL
     * @return 是否为有效的YouTube URL
     */
    public boolean isValidYoutubeUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        String regex = "^(https?://)?(www\\.)?(youtube\\.com|youtu\\.?be)/.+$";
        return Pattern.compile(regex).matcher(url).matches();
    }

    /**
     * 从YouTube URL中提取视频ID
     * @param url YouTube视频URL
     * @return 视频ID，如果提取失败返回null
     */
    public String extractVideoId(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }
        
        String pattern = "(?<=watch\\?v=|/videos/|embed\\/|youtu.be\\/|\\/v\\/|\\/e\\/|watch\\?v%3D|watch\\?feature=player_embedded&v=|%2Fvideos%2F|embed%2F|youtu.be%2F|%2Fv%2F)[^#\\&\\?\\n]*";
        Matcher matcher = Pattern.compile(pattern).matcher(url);
        return matcher.find() ? matcher.group() : null;
    }

    /**
     * 验证YouTube URL并提取视频ID
     * @param url YouTube视频URL
     * @return 视频ID，如果URL无效或提取失败返回null
     */
    public String validateAndExtractVideoId(String url) {
        if (!isValidYoutubeUrl(url)) {
            log.warn("无效的YouTube URL: {}", url);
            return null;
        }
        
        String videoId = extractVideoId(url);
        if (videoId == null) {
            log.warn("无法从URL中提取视频ID: {}", url);
        }
        
        return videoId;
    }
} 