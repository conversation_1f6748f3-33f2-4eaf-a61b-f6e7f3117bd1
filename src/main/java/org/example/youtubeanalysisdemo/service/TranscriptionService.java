package org.example.youtubeanalysisdemo.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 视频转录服务，负责下载视频音频、使用Groq API转录音频和翻译字幕
 */
@Service
public class TranscriptionService {

    private static final Logger logger = LoggerFactory.getLogger(TranscriptionService.class);
    
    @Value("${subtitles.save.path}")
    private String subtitleSavePath;
    
    @Value("${audio.temp.path:temp/audio}")
    private String audioTempPath;
    
    @Autowired
    private TranslationService translationService;
    
    @Autowired
    private GroqTranscriptionService groqTranscriptionService;
    
    /**
     * 转录音频文件（使用Groq API，无需排队）
     * @param videoUrl YouTube视频URL
     * @param videoId 视频ID
     * @param language 语言
     * @param subtitleFileId 字幕文件ID
     * @return 一个CompletableFuture，在任务完成时会包含VTT文件路径
     */
    public CompletableFuture<String> transcribeVideo(String videoUrl, String videoId, String language, String subtitleFileId) {
        logger.info("开始转录视频: videoId={}, language={}, subtitleFileId={}", videoId, language, subtitleFileId);
        
        return CompletableFuture.supplyAsync(() -> {
            String audioFilePath = null;
            try {
                // 1. 下载音频
                logger.info("开始下载音频: {}", videoUrl);
                audioFilePath = downloadYouTubeAudio(videoUrl, videoId);
                
                // 2. 使用Groq API转录音频
                logger.info("开始使用Groq API转录音频: {}", audioFilePath);
                String vttFilePath = groqTranscriptionService.transcribeAudio(audioFilePath, language, subtitleFileId);
                
                logger.info("转录任务处理成功: videoId={}, vttFilePath={}", videoId, vttFilePath);
                return vttFilePath;
                
            } catch (Exception e) {
                logger.error("转录任务处理失败: videoId={}", videoId, e);
                throw new RuntimeException("转录失败: " + e.getMessage(), e);
            } finally {
                // 3. 清理下载的音频文件
                if (audioFilePath != null) {
                    try {
                        Files.deleteIfExists(Paths.get(audioFilePath));
                        logger.info("已清理临时音频文件: {}", audioFilePath);
                    } catch (IOException e) {
                        logger.error("清理临时音频文件失败: {}", audioFilePath, e);
                    }
                }
            }
        });
    }
    
    /**
     * 下载YouTube视频的音频
     * @param videoUrl YouTube视频URL
     * @param videoId 视频ID
     * @return 音频文件路径
     * @throws Exception 如果下载过程中出错
     */
    public String downloadYouTubeAudio(String videoUrl, String videoId) throws Exception {
        logger.info("开始下载YouTube音频: {}", videoUrl);
        
        // 确保临时目录存在
        Path audioPath = Paths.get(audioTempPath);
        if (!Files.exists(audioPath)) {
            Files.createDirectories(audioPath);
        }
        
        // 音频文件名
        String audioFileName = videoId + "_" + System.currentTimeMillis() + ".mp3";
        String audioFilePath = audioPath.resolve(audioFileName).toString();
        
        // 构建yt-dlp命令
        ProcessBuilder processBuilder = new ProcessBuilder(
                "yt-dlp",
                "-x",                      // 提取音频
                "--audio-format", "mp3",   // 设置音频格式为mp3
                "--no-playlist",           // 重要：不下载播放列表，只下载指定视频
                "--no-check-certificates", // 不检查SSL证书
                "--geo-bypass",            // 绕过地理限制
                "--socket-timeout", "10",  // 设置超时
                "--retries", "3",          // 重试次数
                "-o", audioFilePath,       // 输出文件
                videoUrl                   // 视频URL
        );
        
        Process process = processBuilder.start();
        
        // 读取标准输出
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String line;
        while ((line = reader.readLine()) != null) {
            logger.debug("yt-dlp 输出: {}", line);
        }
        
        // 读取错误输出
        reader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
        while ((line = reader.readLine()) != null) {
            logger.warn("yt-dlp 错误: {}", line);
        }
        
        // 等待进程完成，设置超时时间
        boolean completed = process.waitFor(5, TimeUnit.MINUTES);
        if (!completed) {
            process.destroyForcibly();
            throw new Exception("yt-dlp下载超时");
        }
        
        int exitCode = process.exitValue();
        if (exitCode != 0) {
            throw new Exception("yt-dlp下载失败，退出代码: " + exitCode);
        }
        
        // 确认文件存在
        File audioFile = new File(audioFilePath);
        if (!audioFile.exists() || audioFile.length() == 0) {
            throw new Exception("音频文件下载失败或文件为空");
        }
        
        logger.info("音频下载完成: {}", audioFilePath);
        return audioFilePath;
    }
    
    /**
     * 翻译字幕文件（使用DashScope API，推荐）
     * @param originalVttPath 原始VTT文件路径
     * @param targetLanguage 目标语言代码
     * @return 翻译后的VTT文件路径
     * @throws Exception 如果翻译过程中出错
     */
    public String translateSubtitles(String originalVttPath, String targetLanguage) throws Exception {
        logger.info("开始翻译字幕: {} 到 {}", originalVttPath, targetLanguage);
        
        // 读取原始VTT文件
        String vttContent = Files.readString(Paths.get(originalVttPath));
        
        // 使用DashScope API进行批量翻译
        String translatedVttContent = translationService.translateSubtitlesWithDashScope(vttContent, targetLanguage);
        
        // 保存翻译后的VTT文件
        String translatedVttPath = originalVttPath.replace(".vtt", "_" + targetLanguage + ".vtt");
        Files.writeString(Paths.get(translatedVttPath), translatedVttContent);
        
        logger.info("字幕翻译完成: {}", translatedVttPath);
        return translatedVttPath;
    }

    /**
     * 清理转录生成的字幕文件
     * @param videoId 视频ID
     * @param taskId 任务ID（可选）
     * @return 清理结果信息
     */
    public String cleanupTranscriptionFiles(String videoId, String taskId) {
        logger.info("开始清理转录文件: videoId={}, taskId={}", videoId, taskId);
        
        try {
            Path subtitlePath = Paths.get(subtitleSavePath);
            if (!Files.exists(subtitlePath) || !Files.isDirectory(subtitlePath)) {
                logger.warn("转录字幕目录不存在: {}", subtitlePath);
                return "转录字幕目录不存在";
            }
            
            List<File> filesToDelete = new ArrayList<>();
            
            // 查找所有与videoId相关的转录文件
            File[] allFiles = subtitlePath.toFile().listFiles((dir, name) -> 
                name.startsWith(videoId + "_") && name.toLowerCase().endsWith(".vtt"));
            
            if (allFiles != null && allFiles.length > 0) {
                // 如果提供了taskId，优先删除与taskId相关的文件
                if (taskId != null && !taskId.trim().isEmpty()) {
                    for (File file : allFiles) {
                        // 检查文件名是否包含taskId相关的标识
                        if (file.getName().contains(taskId)) {
                            filesToDelete.add(file);
                        }
                    }
                }
                
                // 如果没有找到taskId相关的文件，或者没有提供taskId，删除所有videoId相关的文件
                if (filesToDelete.isEmpty()) {
                    // 按修改时间排序，删除最近的转录文件（因为转录文件通常是最新生成的）
                    Arrays.sort(allFiles, Comparator.comparingLong(File::lastModified).reversed());
                    
                    // 限制删除数量，避免误删太多文件
                    int maxFilesToDelete = Math.min(allFiles.length, 10);
                    for (int i = 0; i < maxFilesToDelete; i++) {
                        filesToDelete.add(allFiles[i]);
                    }
                }
            }
            
            // 执行删除
            int deletedCount = 0;
            int failedCount = 0;
            
            for (File file : filesToDelete) {
                logger.info("删除转录字幕文件: {}", file.getAbsolutePath());
                if (file.delete()) {
                    deletedCount++;
                    logger.info("成功删除转录文件: {}", file.getName());
                } else {
                    failedCount++;
                    logger.warn("删除转录文件失败: {}. 尝试退出时删除.", file.getName());
                    file.deleteOnExit();
                }
            }
            
            String result = String.format("转录文件清理完成。成功删除: %d个, 失败: %d个", deletedCount, failedCount);
            logger.info("转录文件清理统计 - videoId: {}, 总共处理: {}个, 成功: {}个, 失败: {}个", 
                    videoId, filesToDelete.size(), deletedCount, failedCount);
            
            return result;
            
        } catch (Exception e) {
            logger.error("清理转录文件时发生错误: videoId={}, taskId={}", videoId, taskId, e);
            return "清理转录文件时发生错误: " + e.getMessage();
        }
    }
    
    /**
     * 清理特定任务的转录文件
     * @param taskId 任务ID
     * @return 清理结果信息
     */
    public String cleanupTranscriptionFilesByTaskId(String taskId) {
        logger.info("开始清理任务相关的转录文件: taskId={}", taskId);
        
        try {
            Path subtitlePath = Paths.get(subtitleSavePath);
            if (!Files.exists(subtitlePath) || !Files.isDirectory(subtitlePath)) {
                logger.warn("转录字幕目录不存在: {}", subtitlePath);
                return "转录字幕目录不存在";
            }
            
            // 查找包含taskId的文件
            File[] taskFiles = subtitlePath.toFile().listFiles((dir, name) -> 
                name.contains(taskId) && name.toLowerCase().endsWith(".vtt"));
            
            if (taskFiles == null || taskFiles.length == 0) {
                logger.info("没有找到与taskId={}相关的转录文件", taskId);
                return "没有找到相关的转录文件";
            }
            
            // 执行删除
            int deletedCount = 0;
            int failedCount = 0;
            
            for (File file : taskFiles) {
                logger.info("删除任务转录文件: {}", file.getAbsolutePath());
                if (file.delete()) {
                    deletedCount++;
                    logger.info("成功删除任务转录文件: {}", file.getName());
                } else {
                    failedCount++;
                    logger.warn("删除任务转录文件失败: {}. 尝试退出时删除.", file.getName());
                    file.deleteOnExit();
                }
            }
            
            String result = String.format("任务转录文件清理完成。成功删除: %d个, 失败: %d个", deletedCount, failedCount);
            logger.info("任务转录文件清理统计 - taskId: {}, 总共处理: {}个, 成功: {}个, 失败: {}个", 
                    taskId, taskFiles.length, deletedCount, failedCount);
            
            return result;
            
        } catch (Exception e) {
            logger.error("清理任务转录文件时发生错误: taskId={}", taskId, e);
            return "清理任务转录文件时发生错误: " + e.getMessage();
        }
    }
} 