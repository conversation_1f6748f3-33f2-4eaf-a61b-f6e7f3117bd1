package org.example.youtubeanalysisdemo.service;

import org.example.youtubeanalysisdemo.entity.Subscription;
import org.example.youtubeanalysisdemo.repository.SubscriptionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Service
public class SubscriptionService {
    
    private static final Logger logger = LoggerFactory.getLogger(SubscriptionService.class);
    
    @Autowired
    private SubscriptionRepository subscriptionRepository;
    
    /**
     * 获取用户所有订阅
     */
    public List<Subscription> getUserSubscriptions(String userId) {
        return subscriptionRepository.findByUserId(userId);
    }
    
    /**
     * 获取用户的活跃订阅
     */
    public List<Subscription> getActiveUserSubscriptions(String userId) {
        return subscriptionRepository.findByUserIdAndStatus(userId, "active");
    }
    
    /**
     * 获取用户当前活跃和等待中的订阅
     */
    public List<Subscription> getUserActiveAndPendingSubscriptions(String userId) {
        return subscriptionRepository.findByUserIdAndStatusInOrderByStartDateAsc(
            userId, Arrays.asList("active", "pending"));
    }
    
    /**
     * 获取用户当前活跃的订阅（如果有）
     */
    public Optional<Subscription> getUserCurrentActiveSubscription(String userId) {
        return subscriptionRepository.findFirstByUserIdAndStatusOrderByStartDateAsc(userId, "active");
    }
    
    /**
     * 获取用户下一个等待生效的订阅（如果有）
     */
    public Optional<Subscription> getUserNextPendingSubscription(String userId) {
        List<Subscription> pendingSubscriptions = 
            subscriptionRepository.findByUserIdAndStatusOrderByStartDateAsc(userId, "pending");
        if (pendingSubscriptions.isEmpty()) {
            return Optional.empty();
        }
        return Optional.of(pendingSubscriptions.get(0));
    }
    
    /**
     * 检查用户是否为Premium会员
     * @param userId 用户ID
     * @return 是否为Premium会员
     */
    public boolean isPremiumMember(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            logger.warn("用户ID为空，无法检查Premium会员状态");
            return false;
        }
        
        Optional<Subscription> activeSubscription = getUserCurrentActiveSubscription(userId);
        
        if (activeSubscription.isPresent()) {
            Subscription subscription = activeSubscription.get();
            String planName = subscription.getPlanName();
            String planId = subscription.getPlanId();
            
            // 检查是否为Premium会员（包括试用版）
            boolean isPremium = (planName != null && planName.toLowerCase().contains("premium")) ||
                               (planId != null && (planId.toLowerCase().contains("premium") || 
                                                 planId.toLowerCase().contains("trial_premium")));
            
            logger.info("用户 {} 的Premium会员状态: {}, 当前计划: {}, 计划ID: {}", 
                       userId, isPremium, planName, planId);
            
            return isPremium;
        }
        
        logger.info("用户 {} 没有活跃订阅，不是Premium会员", userId);
        return false;
    }
    
    /**
     * 检查用户是否为Plus会员或Premium会员
     * @param userId 用户ID
     * @return 是否为Plus会员或Premium会员
     */
    public boolean isPlusOrPremiumMember(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            logger.warn("用户ID为空，无法检查Plus/Premium会员状态");
            return false;
        }
        
        Optional<Subscription> activeSubscription = getUserCurrentActiveSubscription(userId);
        
        if (activeSubscription.isPresent()) {
            Subscription subscription = activeSubscription.get();
            String planName = subscription.getPlanName();
            String planId = subscription.getPlanId();
            
            // 检查是否为Plus会员或Premium会员（包括试用版）
            boolean isPlusOrPremium = (planName != null && (planName.toLowerCase().contains("plus") || planName.toLowerCase().contains("premium"))) ||
                                     (planId != null && (planId.toLowerCase().contains("plus") || 
                                                       planId.toLowerCase().contains("premium") || 
                                                       planId.toLowerCase().contains("trial_premium")));
            
            logger.info("用户 {} 的Plus/Premium会员状态: {}, 当前计划: {}, 计划ID: {}", 
                       userId, isPlusOrPremium, planName, planId);
            
            return isPlusOrPremium;
        }
        
        logger.info("用户 {} 没有活跃订阅，不是Plus/Premium会员", userId);
        return false;
    }

    /**
     * 检查用户是否有任何活跃的付费订阅
     * @param userId 用户ID
     * @return 是否有活跃订阅
     */
    public boolean hasActiveSubscription(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return false;
        }
        
        Optional<Subscription> activeSubscription = getUserCurrentActiveSubscription(userId);
        return activeSubscription.isPresent();
    }
    
    /**
     * 为新用户创建7天Premium试用订阅
     * @param googleId 用户的Google ID
     * @return 创建的试用订阅
     */
    public Subscription createTrialSubscription(String googleId) {
        logger.info("为用户创建7天Premium试用订阅: {}", googleId);
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime trialEndDate = now.plusDays(7);
        
        // 生成试用订阅的唯一标识
        String trialSubscriptionId = "trial_" + googleId + "_" + System.currentTimeMillis();
        
        Subscription trialSubscription = Subscription.builder()
                .userId(googleId)
                .paddleSubscriptionId(trialSubscriptionId)
                .status("active")
                .planId("trial_premium_7d")
                .planName("Premium会员")
                .description("7天免费试用")
                .startDate(now)
                .nextBillDate(trialEndDate)
                .createdAt(now)
                .updatedAt(now)
                .build();
        
        Subscription savedSubscription = subscriptionRepository.save(trialSubscription);
        logger.info("成功创建试用订阅: subscriptionId={}, 有效期至: {}", 
                savedSubscription.getId(), trialEndDate);
        
        return savedSubscription;
    }
} 