package org.example.youtubeanalysisdemo.service;

import org.example.youtubeanalysisdemo.dto.SubtitleRequest;
import org.example.youtubeanalysisdemo.entity.LearningContent;
import org.example.youtubeanalysisdemo.entity.UserSavedContent;
import org.example.youtubeanalysisdemo.repository.LearningContentRepository;
import org.example.youtubeanalysisdemo.repository.UserSavedContentRepository;
import org.example.youtubeanalysisdemo.util.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 学习内容管理服务
 * 负责学习内容的保存、删除、查询和收藏等功能
 */
@Service
@Transactional
public class LearningContentService {
    private static final Logger logger = LoggerFactory.getLogger(LearningContentService.class);

    @Autowired
    private LearningContentRepository learningContentRepository;
    
    @Autowired
    private UserSavedContentRepository userSavedContentRepository;
    
    @Autowired
    private AIAnalysisService aiAnalysisService;
    
    @Autowired
    private JapaneseTextService japaneseTextService;
    
    @Autowired
    private EnglishPosTaggingService englishPosTaggingService;

    // 存储已取消的任务ID
    private static final Set<String> canceledTasks = ConcurrentHashMap.newKeySet();

    /**
     * 保存字幕内容
     */
    public Map<String, Object> saveSubtitles(SubtitleRequest.SaveRequest request) {
        String language = request.getLanguage() != null ? request.getLanguage() : "japanese"; // 默认为日语
        logger.info("收到保存请求: contents={}, types={}, language={}", request.getContents(), request.getTypes(), language);
        System.out.println("收到保存请求: contents=" + request.getContents() + ", types=" + request.getTypes() + ", language=" + language);

        try {
            if (request.getContents() == null || request.getContents().isEmpty()) {
                logger.error("保存内容为空");
                System.err.println("保存内容为空");
                return Map.of("status", "error", "message", "保存内容不能为空");
            }

            String userId = SecurityUtils.getCurrentUserId();
            int savedCount = 0;
            
            for (int i = 0; i < request.getContents().size(); i++) {
                String content = request.getContents().get(i);
                // 获取对应索引的 type，如果不存在则使用自动检测
                String providedType = (request.getTypes() != null && i < request.getTypes().size()) 
                    ? request.getTypes().get(i) : null;
                
                String wordForDb;
                String originalAnalysis = "";
                String finalAnalysis;
                String type = "grammar"; // 默认类型为grammar
                
                int colonIndex = content.indexOf(':');

                if (colonIndex > 0) {
                    // 格式为 "单词:详情"，说明是预分析过的内容，直接保存
                    logger.info("内容已预先分析，直接解析保存: {}", content);
                    String header = content.substring(0, colonIndex).trim();
                    String details = content.substring(colonIndex + 1).trim();

                    if ("english".equalsIgnoreCase(language)) {
                        wordForDb = header.split("/")[0].trim();
                        type = wordForDb.contains(" ") ? "grammar" : "word";

                        if ("word".equals(type)) {
                            // 对于预分析的单词，details部分就是完整的分析结果
                            finalAnalysis = details;
                        } else {
                            // 对于预分析的短语，需要将header和details组合起来
                            finalAnalysis = header + ": " + details;
                        }
                    } else {
                        // 日语内容，header是word
                        wordForDb = header;
                        finalAnalysis = content; // 日语直接使用完整内容
                        if (providedType != null && !providedType.isEmpty()) {
                            type = providedType;
                        } else {
                            type = (wordForDb.contains("（") || japaneseTextService.isAllKatakana(wordForDb)) ? "word" : "grammar";
                        }
                    }
                } else {
                    // 格式为纯单词或短语，需要调用AI进行分析
                    logger.info("内容需要AI分析: {}", content);
                    wordForDb = content; // 此时content就是纯单词/短语
                    
                    if ("english".equalsIgnoreCase(language)) {
                        // 如果前端没提供，则根据内容判断
                        if (providedType != null && !providedType.isEmpty()) {
                            type = providedType;
                        } else {
                            type = wordForDb.contains(" ") ? "grammar" : "word";
                        }
                        
                        // 进行词形还原
                        String lemmatizedWord;
                        try {
                            lemmatizedWord = englishPosTaggingService.lemmatizePhrase(wordForDb);
                            logger.info("英语词形还原: {} -> {}", wordForDb, lemmatizedWord);
                        } catch (Exception e) {
                            logger.warn("英语词形还原失败: {}", e.getMessage());
                            lemmatizedWord = wordForDb;
                        }

                        // 根据类型调用不同的AI服务
                        try {
                            logger.info("开始分析英语内容: {}, 类型: {}", lemmatizedWord, type);
                            if ("word".equals(type)) {
                                finalAnalysis = aiAnalysisService.analyzeWord(lemmatizedWord, "english");
                            } else {
                                finalAnalysis = aiAnalysisService.analyzeEnglishPhrase(lemmatizedWord);
                            }
                            
                            // 如果发生了词形还原，则在分析内容前添加原始形式的提示
                            if (!wordForDb.equalsIgnoreCase(lemmatizedWord)) {
                                finalAnalysis = "原始形式: " + wordForDb + "\n\n" + finalAnalysis;
                            }
                            logger.info("英语内容分析完成: {}", finalAnalysis);
                        } catch (Exception e) {
                            logger.warn("英语内容分析失败: {}", e.getMessage());
                            finalAnalysis = "分析失败，请稍后重试";
                        }
                    } else { 
                        // 日语的纯内容保存（理论上较少出现，但做兼容）
                        finalAnalysis = ""; // 或调用日语分析
                        logger.warn("收到纯日语内容，但未实现相应的AI分析分支: {}", content);
                    }
                }
                
                // 生成内容唯一标识（包含语言参数）
                String contentKey = LearningContent.generateContentKey(wordForDb, type, language);
                
                // 检查内容是否已存在
                LearningContent learningContent;
                Optional<LearningContent> existingContent = learningContentRepository.findByContentKey(contentKey);
                
                if (existingContent.isPresent()) {
                    // 使用已存在的内容
                    learningContent = existingContent.get();
                    logger.info("内容已存在，使用已有记录: {}", contentKey);
                    System.out.println("内容已存在，使用已有记录: " + contentKey);
                } else {
                    // 创建新的内容记录
                    learningContent = new LearningContent();
                    learningContent.setContentKey(contentKey);
                    learningContent.setWord(wordForDb);
                    learningContent.setAnalysis(finalAnalysis);
                    learningContent.setType(type);
                    learningContent.setLang(language); // 设置语言类型
                    learningContent = learningContentRepository.save(learningContent);
                    logger.info("创建新内容记录: {}, language: {}", contentKey, language);
                    System.out.println("创建新内容记录: " + contentKey + ", language: " + language);
                }
                
                // 检查用户是否已收藏该内容
                Optional<UserSavedContent> existingUserContent = 
                        userSavedContentRepository.findByUserIdAndContentId(userId, learningContent.getId());
                
                if (existingUserContent.isPresent()) {
                    logger.info("用户已收藏该内容，跳过: userId={}, contentId={}", userId, learningContent.getId());
                    System.out.println("用户已收藏该内容，跳过: userId=" + userId + ", contentId=" + learningContent.getId());
                } else {
                    // 创建用户收藏关系
                    UserSavedContent userSavedContent = new UserSavedContent();
                    userSavedContent.setUserId(userId);
                    userSavedContent.setContentId(learningContent.getId());
                    userSavedContent.setLearned(0); // 默认未学会
                    userSavedContentRepository.save(userSavedContent);
                    savedCount++;
                    logger.info("创建用户收藏关系: userId={}, contentId={}", userId, learningContent.getId());
                    System.out.println("创建用户收藏关系: userId=" + userId + ", contentId=" + learningContent.getId());
                }
            }
            
            logger.info("成功保存{}条内容到数据库", savedCount);
            System.out.println("成功保存" + savedCount + "条内容到数据库");

            return Map.of("status", "success", "message", "内容已保存");
        } catch (Exception e) {
            logger.error("保存内容失败: contents={}, language={}", request.getContents(), language, e);
            System.err.println("保存内容失败: contents=" + request.getContents() + ", language=" + language + ", error=" + e.getMessage());
            return Map.of("status", "error", "message", "保存失败: " + e.getMessage());
        }
    }

    /**
     * 删除学习内容
     */
    public Map<String, String> deleteItems(SubtitleRequest.DeleteRequest request) {
        String userId = SecurityUtils.getCurrentUserId();
        logger.info("收到删除请求: userId={}, itemIds={}", userId, request.getItemIds());
        System.out.println("收到删除请求: userId=" + userId + ", itemIds=" + request.getItemIds());
        
        try {
            if (request.getItemIds() == null || request.getItemIds().isEmpty()) {
                return Map.of("status", "error", "message", "未指定要删除的条目");
            }
            
            List<Long> itemIds = request.getItemIds().stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            
            // 确保只删除当前用户的条目
            List<UserSavedContent> itemsToDelete = userSavedContentRepository.findAllById(itemIds).stream()
                    .filter(item -> item.getUserId().equals(userId))
                    .collect(Collectors.toList());
            
            if (itemsToDelete.isEmpty()) {
                return Map.of("status", "warning", "message", "未找到可删除的条目");
            }
            
            userSavedContentRepository.deleteAll(itemsToDelete);
            logger.info("成功删除{}条内容", itemsToDelete.size());
            System.out.println("成功删除" + itemsToDelete.size() + "条内容");
            
            return Map.of("status", "success", "message", "删除成功");
        } catch (Exception e) {
            logger.error("删除条目失败: {}", e.getMessage(), e);
            System.err.println("删除条目失败: " + e.getMessage());
            return Map.of("status", "error", "message", "删除失败: " + e.getMessage());
        }
    }

    /**
     * 标记为已学会
     */
    public Map<String, String> markAsLearned(SubtitleRequest.DeleteRequest request) {
        String userId = SecurityUtils.getCurrentUserId();
        logger.info("收到标记为已学会请求: userId={}, itemIds={}", userId, request.getItemIds());
        System.out.println("收到标记为已学会请求: userId=" + userId + ", itemIds=" + request.getItemIds());
        
        try {
            if (request.getItemIds() == null || request.getItemIds().isEmpty()) {
                return Map.of("status", "error", "message", "未指定要标记的条目");
            }
            
            List<Long> itemIds = request.getItemIds().stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            
            // 确保只更新当前用户的条目
            List<UserSavedContent> itemsToUpdate = userSavedContentRepository.findAllById(itemIds).stream()
                    .filter(item -> item.getUserId().equals(userId))
                    .collect(Collectors.toList());
            
            if (itemsToUpdate.isEmpty()) {
                return Map.of("status", "warning", "message", "未找到可标记的条目");
            }
            
            // 更新学习状态
            itemsToUpdate.forEach(item -> item.setLearned(1));
            userSavedContentRepository.saveAll(itemsToUpdate);
            
            logger.info("成功标记{}条内容为已学会", itemsToUpdate.size());
            System.out.println("成功标记" + itemsToUpdate.size() + "条内容为已学会");
            
            return Map.of("status", "success", "message", "已标记为学会");
        } catch (Exception e) {
            logger.error("标记为已学会失败: {}", e.getMessage(), e);
            System.err.println("标记为已学会失败: " + e.getMessage());
            return Map.of("status", "error", "message", "标记失败: " + e.getMessage());
        }
    }

    /**
     * 获取保存的内容列表
     */
    public List<Map<String, String>> getSavedContents(Integer learned) {
        String userId = SecurityUtils.getCurrentUserId();
        logger.info("获取用户[{}]的内容，学习状态={}", userId, learned);
        System.out.println("获取用户[" + userId + "]的内容，学习状态=" + learned);
        
        try {
            List<Map<String, String>> result = new ArrayList<>();
            
            // 使用关联查询获取用户收藏内容的详细信息
            if (learned != null) {
                // 使用联合查询获取特定学习状态的内容
                List<UserSavedContent> userSavedContents = 
                        userSavedContentRepository.findByUserIdAndLearnedOrderByCreatedAtDesc(userId, learned);
                
                // 手动关联查询（由于DTO查询可能存在兼容性问题，这里使用手动关联）
                for (UserSavedContent userContent : userSavedContents) {
                    LearningContent content = learningContentRepository.findById(userContent.getContentId()).orElse(null);
                    if (content != null) {
                        Map<String, String> map = new HashMap<>();
                        map.put("word", content.getWord());
                        map.put("analysis", content.getAnalysis());
                        map.put("id", userContent.getId().toString());
                        map.put("type", content.getType());
                        map.put("lang", content.getLang() != null ? content.getLang() : "japanese"); // 添加语言字段
                        map.put("learned", userContent.getLearned().toString());
                        result.add(map);
                    }
                }
            } else {
                // 获取所有学习状态的内容
                List<UserSavedContent> userSavedContents = 
                        userSavedContentRepository.findByUserIdOrderByCreatedAtDesc(userId);
                
                // 手动关联查询
                for (UserSavedContent userContent : userSavedContents) {
                    LearningContent content = learningContentRepository.findById(userContent.getContentId()).orElse(null);
                    if (content != null) {
                        Map<String, String> map = new HashMap<>();
                        map.put("word", content.getWord());
                        map.put("analysis", content.getAnalysis());
                        map.put("id", userContent.getId().toString());
                        map.put("type", content.getType());
                        map.put("lang", content.getLang() != null ? content.getLang() : "japanese"); // 添加语言字段
                        map.put("learned", userContent.getLearned().toString());
                        result.add(map);
                    }
                }
            }
            
            logger.info("从数据库获取到{}条记录", result.size());
            System.out.println("从数据库获取到" + result.size() + "条记录");
            
            return result;
        } catch (Exception e) {
            logger.error("获取保存内容失败", e);
            System.err.println("获取保存内容失败: " + e.getMessage());
            return List.of();
        }
    }

    /**
     * 收藏词组（异步处理）
     */
    public Map<String, Object> favoriteWord(SubtitleRequest.FavoriteRequest request) {
        String userId = SecurityUtils.getCurrentUserId();
        String lang = request.getLang() != null ? request.getLang() : "japanese"; // 默认为日语
        logger.info("收到收藏请求: userId={}, word={}, lang={}", userId, request.getWord(), lang);
        
        try {
            if (request.getWord() == null || request.getWord().trim().isEmpty()) {
                return Map.of(
                    "success", false,
                    "message", "词组内容不能为空"
                );
            }
            
            // 生成内容唯一标识，包含语言信息
            String contentKey = LearningContent.generateContentKey(request.getWord(), "word", lang);
            
            // 检查内容是否已存在
            Optional<LearningContent> existingContent = learningContentRepository.findByContentKey(contentKey);
            LearningContent learningContent;
            
            // 检查用户是否已收藏该内容
            if (existingContent.isPresent()) {
                learningContent = existingContent.get();
                
                // 检查用户是否已收藏
                Optional<UserSavedContent> existingUserContent = 
                        userSavedContentRepository.findByUserIdAndContentId(userId, learningContent.getId());
                
                if (existingUserContent.isPresent()) {
                    return Map.of(
                        "success", true,
                        "message", "该词组已收藏",
                        "itemId", existingUserContent.get().getId().toString(),
                        "alreadyExists", true
                    );
                }
            }
            
            // 创建任务ID，用于跟踪和可能的取消
            String taskId = userId + "_" + System.currentTimeMillis();
            
            // 异步处理，不阻塞用户
            CompletableFuture.runAsync(() -> {
                try {
                    // 检查任务是否已被取消
                    if (canceledTasks.contains(taskId)) {
                        logger.info("任务已取消: {}", taskId);
                        canceledTasks.remove(taskId);
                        return;
                    }
                    
                    // 调用第三方服务获取词组解析，传递语言参数
                    String analysis = aiAnalysisService.analyzeWord(request.getWord(), lang);
                    
                    // 再次检查任务是否已被取消
                    if (canceledTasks.contains(taskId)) {
                        logger.info("任务已取消: {}", taskId);
                        canceledTasks.remove(taskId);
                        return;
                    }
                    
                    // 处理返回的分析结果
                    String processedAnalysis;
                    if ("english".equalsIgnoreCase(lang)) {
                        // 英语单词不需要特殊处理，直接使用分析结果
                        processedAnalysis = analysis;
                    } else {
                        // 日语单词使用原有的处理逻辑
                        processedAnalysis = japaneseTextService.processAnalysisResult(request.getWord(), analysis);
                    }
                    
                    // 保存到数据库
                    LearningContent content;
                    
                    // 检查内容是否已存在
                    Optional<LearningContent> existingContentCheck = learningContentRepository.findByContentKey(contentKey);
                    
                    if (existingContentCheck.isPresent()) {
                        // 使用已存在的内容
                        content = existingContentCheck.get();
                        logger.info("内容已存在，使用已有记录: {}", contentKey);
                    } else {
                        // 创建新的内容记录
                        content = new LearningContent();
                        content.setContentKey(contentKey);
                        content.setWord(request.getWord());
                        content.setAnalysis(processedAnalysis);
                        content.setType("word"); // 默认为单词类型
                        content.setLang(lang); // 设置语言类型
                        content = learningContentRepository.save(content);
                        logger.info("创建新内容记录: {}", contentKey);
                    }
                    
                    // 创建用户收藏关系
                    UserSavedContent userSavedContent = new UserSavedContent();
                    userSavedContent.setUserId(userId);
                    userSavedContent.setContentId(content.getId());
                    userSavedContent.setLearned(0); // 默认未学会
                    userSavedContentRepository.save(userSavedContent);
                    
                    logger.info("成功收藏{}词组到数据库: {}", "english".equalsIgnoreCase(lang) ? "英语" : "日语", request.getWord());
                } catch (Exception e) {
                    logger.error("收藏词组失败: {}", e.getMessage(), e);
                }
            });
            
            // 立即返回响应给用户，不等待分析完成
            return Map.of(
                "success", true,
                "message", "已开始收藏词组，分析将在后台进行",
                "taskId", taskId
            );
        } catch (Exception e) {
            logger.error("收藏词组请求处理失败: {}", e.getMessage(), e);
            return Map.of(
                "success", false,
                "message", "收藏失败: " + e.getMessage()
            );
        }
    }

    /**
     * 取消收藏词组
     */
    public Map<String, Object> unfavoriteWord(SubtitleRequest.UnfavoriteRequest request) {
        String userId = SecurityUtils.getCurrentUserId();
        String lang = request.getLang() != null ? request.getLang() : "japanese"; // 默认为日语
        logger.info("收到取消收藏请求: userId={}, word={}, lang={}, taskId={}", userId, request.getWord(), lang, request.getTaskId());
        
        try {
            // 如果提供了taskId，说明可能是正在进行的任务
            if (request.getTaskId() != null && !request.getTaskId().isEmpty()) {
                // 添加到取消任务列表
                canceledTasks.add(request.getTaskId());
                logger.info("已标记任务为取消: {}", request.getTaskId());
            }
            
            // 如果提供了词组，则从数据库中删除用户收藏关系
            if (request.getWord() != null && !request.getWord().isEmpty()) {
                // 生成内容唯一标识，包含语言信息
                String contentKey = LearningContent.generateContentKey(request.getWord(), "word", lang);
                
                // 查找内容
                Optional<LearningContent> content = learningContentRepository.findByContentKey(contentKey);
                
                if (content.isPresent()) {
                    // 查找用户收藏关系
                    Optional<UserSavedContent> userContent = 
                            userSavedContentRepository.findByUserIdAndContentId(userId, content.get().getId());
                    
                    if (userContent.isPresent()) {
                        // 删除用户收藏关系
                        userSavedContentRepository.delete(userContent.get());
                        logger.info("已删除用户收藏关系: userId={}, word={}, lang={}", userId, request.getWord(), lang);
                    } else {
                        logger.info("未找到用户收藏关系: userId={}, word={}, lang={}", userId, request.getWord(), lang);
                    }
                } else {
                    logger.info("未找到要删除的内容: {} (语言: {})", request.getWord(), lang);
                }
            }
            
            return Map.of(
                "success", true,
                "message", "已取消收藏"
            );
        } catch (Exception e) {
            logger.error("取消收藏请求处理失败: {}", e.getMessage(), e);
            return Map.of(
                "success", false,
                "message", "取消收藏失败: " + e.getMessage()
            );
        }
    }
} 