package org.example.youtubeanalysisdemo.service;

import lombok.extern.slf4j.Slf4j;
import org.example.youtubeanalysisdemo.strategy.LanguageStrategy;
import org.example.youtubeanalysisdemo.strategy.LanguageStrategyFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 字幕操作服务
 * 负责字幕的下载、清理、文件查找等操作
 */
@Slf4j
@Service
public class SubtitleOperationService {

    @Value("${subtitles.directory:src/main/resources/static/subtitles}")
    private String subtitlesDirectory;
    
    private static final String SUBTITLE_EXTENSION = ".vtt";
    
    // 创建线程池执行器用于并行下载字幕
    private final ExecutorService executorService = Executors.newFixedThreadPool(5);

    @Autowired
    private YouTubeUtilService youTubeUtilService;
    
    @Autowired
    private LanguageStrategyFactory languageStrategyFactory;

    /**
     * 下载字幕文件（带语言参数）
     * @param videoId 视频ID
     * @param subtitleFileId 字幕文件ID
     * @param languageIdentifier 语言标识符
     * @return 下载的字幕文件路径映射
     */
    public Map<String, String> downloadSubtitlesVttWithLanguage(String videoId, String subtitleFileId, String languageIdentifier) {
        // 获取对应的语言策略
        LanguageStrategy strategy = languageStrategyFactory.getStrategy(languageIdentifier);
        log.info("使用语言策略: {} -> {}", languageIdentifier, strategy.getDisplayName());
        
        Map<String, String> downloadedPaths = new ConcurrentHashMap<>();
        Path subtitlesDirPath = Paths.get(subtitlesDirectory);

        try {
            if (!Files.exists(subtitlesDirPath)) {
                Files.createDirectories(subtitlesDirPath);
            }
        } catch (IOException e) {
            log.error("创建字幕目录失败: {}", subtitlesDirPath, e);
            return downloadedPaths;
        }

        String baseFilename = videoId + "_" + subtitleFileId;
        String ytDlpOutputTemplate = subtitlesDirPath.resolve(baseFilename).toString();

        // 创建两个并行任务，分别下载主语言和翻译语言字幕
        List<CompletableFuture<Map<String, String>>> futures = new ArrayList<>();

        // 任务1: 下载主语言字幕
        CompletableFuture<Map<String, String>> primaryFuture = CompletableFuture.supplyAsync(() -> 
            downloadLanguageSubtitle(videoId, strategy, ytDlpOutputTemplate, subtitlesDirPath, baseFilename, true), executorService);
        
        // 任务2: 下载翻译语言字幕
        CompletableFuture<Map<String, String>> translationFuture = CompletableFuture.supplyAsync(() -> 
            downloadLanguageSubtitle(videoId, strategy, ytDlpOutputTemplate, subtitlesDirPath, baseFilename, false), executorService);
        
        futures.add(primaryFuture);
        futures.add(translationFuture);
        
        // 等待所有下载任务完成，最多等待25秒
        try {
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.get(25, TimeUnit.SECONDS);
            
            // 合并结果
            for (CompletableFuture<Map<String, String>> future : futures) {
                try {
                    Map<String, String> result = future.get();
                    downloadedPaths.putAll(result);
                } catch (ExecutionException | InterruptedException e) {
                    log.error("获取字幕下载结果时出错", e);
                    if (e instanceof InterruptedException) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        } catch (TimeoutException e) {
            log.error("字幕下载任务超时", e);
            // 取消所有未完成的任务
            for (CompletableFuture<Map<String, String>> future : futures) {
                if (!future.isDone()) {
                    future.cancel(true);
                }
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("等待字幕下载任务完成时出错", e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
        }

        return downloadedPaths;
    }

    /**
     * 下载字幕文件（兼容旧版本）
     * @param videoId 视频ID
     * @param subtitleFileId 字幕文件ID
     * @return 下载的字幕文件路径映射
     */
    public Map<String, String> downloadSubtitlesVtt(String videoId, String subtitleFileId) {
        // 默认使用日语策略，保持向后兼容
        return downloadSubtitlesVttWithLanguage(videoId, subtitleFileId, "japanese");
    }

    /**
     * 下载指定语言的字幕
     * @param videoId 视频ID
     * @param strategy 语言策略
     * @param ytDlpOutputTemplate 输出模板
     * @param subtitlesDirPath 字幕目录路径
     * @param baseFilename 基础文件名
     * @param isPrimary 是否为主语言
     * @return 下载结果映射
     */
    private Map<String, String> downloadLanguageSubtitle(String videoId, LanguageStrategy strategy, 
                                                        String ytDlpOutputTemplate, Path subtitlesDirPath, 
                                                        String baseFilename, boolean isPrimary) {
        Map<String, String> result = new HashMap<>();
        
        String targetLanguage = isPrimary ? strategy.getPrimaryLanguage() : strategy.getTranslationLanguage();
        List<String> languageVariants = isPrimary ? strategy.getPrimaryLanguageVariants() : strategy.getTranslationLanguageVariants();
        String displayName = isPrimary ? ("主语言(" + strategy.getDisplayName() + ")") : "翻译语言(中文)";
        
        log.info("开始并行下载{}: videoId={}, targetLanguage={}", displayName, videoId, targetLanguage);
        
        String langCodes = String.join(",", languageVariants);
        
        // 增加重试机制 - 最多尝试3次
        int maxRetries = 3;
        boolean success = false;
        
        for (int attempt = 1; attempt <= maxRetries && !success; attempt++) {
            log.info("第{}次尝试下载{}: videoId={}", attempt, displayName, videoId);
            
            // 尝试下载用户上传的字幕
            if (invokeYtDlpForVtt(videoId, langCodes, ytDlpOutputTemplate, false)) {
                String subtitlePath = findVttSubtitlePathForLanguage(subtitlesDirPath, baseFilename, targetLanguage);
                if (subtitlePath != null) {
                    result.put(targetLanguage, subtitlePath);
                    log.info("成功下载用户上传的{}: {} -> 键名: {}", displayName, subtitlePath, targetLanguage);
                    success = true;
                    break;
                } else {
                    log.warn("用户上传的{}下载后验证失败，尝试自动生成字幕", displayName);
                }
            }
            
            // 如果没有用户上传的字幕，尝试下载自动生成的
            if (!success && invokeYtDlpForVtt(videoId, langCodes, ytDlpOutputTemplate, true)) {
                String subtitlePath = findVttSubtitlePathForLanguage(subtitlesDirPath, baseFilename, targetLanguage);
                if (subtitlePath != null) {
                    result.put(targetLanguage, subtitlePath);
                    log.info("成功下载自动生成的{}: {} -> 键名: {}", displayName, subtitlePath, targetLanguage);
                    success = true;
                    break;
                } else {
                    log.warn("自动生成的{}下载后验证失败", displayName);
                }
            }
            
            // 如果还是没有，尝试下载自动翻译字幕（仅对翻译语言）
            if (!success && !isPrimary) {
                log.info("尝试下载自动翻译的{}: videoId={}", displayName, videoId);
                String translationCodes = buildAutoTranslationCodes(strategy);
                if (invokeYtDlpForVtt(videoId, translationCodes, ytDlpOutputTemplate, true)) {
                    String subtitlePath = findVttSubtitlePathForLanguage(subtitlesDirPath, baseFilename, targetLanguage);
                    if (subtitlePath != null) {
                        result.put(targetLanguage, subtitlePath);
                        log.info("成功下载自动翻译的{}: {} -> 键名: {}", displayName, subtitlePath, targetLanguage);
                        success = true;
                        break;
                    } else {
                        log.warn("自动翻译的{}下载后验证失败", displayName);
                    }
                } else {
                    log.warn("下载{}翻译字幕失败", displayName);
                }
            }
            
            // 如果当前尝试失败，且还有重试机会，等待后重试
            if (!success && attempt < maxRetries) {
                try {
                    int waitTime = 1000; // 固定1秒间隔，快速重试
                    log.info("{}下载失败，{}ms后进行第{}次重试...", 
                            displayName, waitTime, attempt + 1);
                    Thread.sleep(waitTime);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("等待重试时被中断");
                    break;
                }
            }
        }
        
        if (!success) {
            log.error("经过{}次尝试后，{}下载失败", maxRetries, displayName);
        }
        
        return result;
    }

    /**
     * 构建自动翻译语言代码
     * @param strategy 语言策略
     * @return 自动翻译语言代码字符串
     */
    private String buildAutoTranslationCodes(LanguageStrategy strategy) {
        List<String> codes = new ArrayList<>();
        List<String> primaryVariants = strategy.getPrimaryLanguageVariants();
        List<String> translationVariants = strategy.getTranslationLanguageVariants();
        
        // 构建翻译代码：目标语言-源语言格式
        for (String transLang : translationVariants) {
            for (String primaryLang : primaryVariants) {
                codes.add(transLang + "-" + primaryLang);
            }
        }
        
        return String.join(",", codes);
    }

    /**
     * 原有的下载字幕文件方法（保持不变，用于向后兼容）
     */
    public Map<String, String> downloadSubtitlesVttOld(String videoId, String subtitleFileId) {
        // 使用新的方法，传入默认的日语策略
        return downloadSubtitlesVttWithLanguage(videoId, subtitleFileId, "japanese");
    }

    /**
     * 清理字幕文件
     * @param subtitleFileId 字幕文件ID
     * @return 清理结果
     */
    public Map<String, Object> cleanupSubtitles(String subtitleFileId) {
        Map<String, Object> response = new HashMap<>();
        
        log.info("开始统一清理字幕文件，会话ID: {}", subtitleFileId);
        try {
            Path subtitlesPath = Paths.get(subtitlesDirectory).toAbsolutePath();
            if (!Files.exists(subtitlesPath) || !Files.isDirectory(subtitlesPath)) {
                response.put("success", false);
                response.put("message", "字幕目录不存在或不是目录。");
                log.warn("字幕目录不存在: {}", subtitlesPath);
                return response;
            }
            
            // 查找所有文件名包含subtitleFileId的VTT文件
            File[] filesToDelete = subtitlesPath.toFile().listFiles((dir, name) ->
                    name.contains(subtitleFileId) && name.toLowerCase().endsWith(SUBTITLE_EXTENSION));
            
            if (filesToDelete != null && filesToDelete.length > 0) {
                int deletedCount = 0;
                int failedCount = 0;
                
                for (File file : filesToDelete) {
                    log.info("删除字幕文件: {}", file.getAbsolutePath());
                    if (file.delete()) {
                        deletedCount++;
                        log.info("成功删除: {}", file.getName());
                    } else {
                        failedCount++;
                        log.warn("删除失败: {}. 尝试在退出时删除.", file.getName());
                        file.deleteOnExit();
                    }
                }
                
                String message = String.format("字幕文件清理完成。成功删除: %d个, 失败: %d个", deletedCount, failedCount);
                response.put("message", message);
                log.info("清理统计 - 会话ID: {}, 总共找到: {}个, 成功: {}个, 失败: {}个", 
                        subtitleFileId, filesToDelete.length, deletedCount, failedCount);
            } else {
                log.info("没有找到需要清理的字幕文件，会话ID: {}", subtitleFileId);
                response.put("message", "没有找到需要清理的字幕文件。");
            }
            
            response.put("success", true);
            return response;
            
        } catch (Exception e) {
            log.error("清理字幕文件时发生严重错误，会话ID: {}", subtitleFileId, e);
            response.put("success", false);
            response.put("message", "清理字幕时发生内部错误。");
            return response;
        }
    }
    
    /**
     * 调用yt-dlp下载VTT字幕
     */
    private boolean invokeYtDlpForVtt(String videoId, String langCodes, String baseOutputName, boolean autoGenerated) {
        log.info("执行yt-dlp (VTT) 命令，视频ID: {}, 语言: {}, 自动生成: {}", videoId, langCodes, autoGenerated);
        
        boolean success = executeYtDlpCommand(videoId, langCodes, baseOutputName, autoGenerated);
        
        if (success) {
            log.info("成功下载字幕。视频ID: {}, 语言: {}", videoId, langCodes);
        } else {
            log.error("字幕下载失败。视频ID: {}, 语言: {}", videoId, langCodes);
        }
        
        return success;
    }
    
    /**
     * 执行yt-dlp命令
     */
    private boolean executeYtDlpCommand(String videoId, String langCodes, String baseOutputName, boolean autoGenerated) {
        List<String> command = new ArrayList<>();
        command.add("yt-dlp");
        command.add("--skip-download");
        if (autoGenerated) {
            command.add("--write-auto-subs");
        } else {
            command.add("--write-sub");
        }
        command.add("--sub-langs");
        command.add(langCodes);
        command.add("--sub-format");
        command.add("vtt");

        // 优化下载速度的参数
        command.add("--extractor-args");
        command.add("youtube:player_client=android,youtube:skip=hls,dash,youtube:player_skip=js,youtube:player_skip=configs");
        
        command.add("--retries");
        command.add("2");
        command.add("--socket-timeout");
        command.add("10");
        command.add("--force-ipv4");
        command.add("--concurrent-fragments");
        command.add("5");
        command.add("--no-check-certificates");
        command.add("--geo-bypass");
        command.add("--no-playlist");
        command.add("--quiet");
        command.add("--no-simulate");
        command.add("--no-check-formats");
        command.add("--no-warnings");
        command.add("--no-progress");
        command.add("--console-title");
        command.add("--no-config-locations");
        command.add("--ignore-no-formats-error");
        command.add("--output");
        command.add(baseOutputName);
        command.add("https://www.youtube.com/watch?v=" + videoId);

        log.info("执行yt-dlp (VTT) 命令: {}", String.join(" ", command));
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            Process process = processBuilder.start();

            StringBuilder errorOutput = new StringBuilder();
            StringBuilder stdOutput = new StringBuilder();
            
            try (
                BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
                BufferedReader stdReader = new BufferedReader(new InputStreamReader(process.getInputStream()))
            ) {
                Thread errorThread = new Thread(() -> {
                    try {
                        String line;
                        while ((line = errorReader.readLine()) != null) {
                            errorOutput.append(line).append(System.lineSeparator());
                        }
                    } catch (IOException e) {
                        log.error("读取错误输出时发生异常", e);
                    }
                });
                
                Thread stdThread = new Thread(() -> {
                    try {
                        String line;
                        while ((line = stdReader.readLine()) != null) {
                            stdOutput.append(line).append(System.lineSeparator());
                        }
                    } catch (IOException e) {
                        log.error("读取标准输出时发生异常", e);
                    }
                });
                
                errorThread.start();
                stdThread.start();
                
                boolean completed = process.waitFor(20, TimeUnit.SECONDS);
                
                errorThread.join(2000);
                stdThread.join(2000);
                
                if (!completed) {
                    log.warn("yt-dlp (VTT) 命令超时: videoId={}, langCodes={}, auto={}", videoId, langCodes, autoGenerated);
                    process.destroyForcibly();
                    return false;
                }
                
                int exitCode = process.exitValue();
                if (exitCode != 0) {
                    log.warn("yt-dlp (VTT) 命令失败，退出码: {} videoId={}, langCodes={}, auto={}\n错误输出:\n{}\n标准输出:\n{}",
                            exitCode, videoId, langCodes, autoGenerated, 
                            errorOutput.toString(), stdOutput.toString());
                } else {
                    if (stdOutput.length() > 0) {
                        log.info("yt-dlp标准输出: {}", stdOutput);
                    }
                    log.info("成功下载VTT字幕: videoId={}, langCodes={}, auto={}", videoId, langCodes, autoGenerated);
                    
                    // 减少等待时间，确保文件完全写入磁盘
                    try {
                        Thread.sleep(500); // 从2000ms减少到500ms
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.warn("等待文件系统完成写入时被中断");
                    }
                }
                return exitCode == 0;
            }
        } catch (IOException | InterruptedException e) {
            log.error("执行yt-dlp (VTT) 出错: videoId={}, langCodes={}, auto={}", videoId, langCodes, autoGenerated, e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            return false;
        }
    }

    /**
     * 查找指定语言的VTT字幕文件路径
     */
    private String findVttSubtitlePathForLanguage(Path subtitlesDirPath, String baseFilename, String targetLanguage) {
        String[] langVariants;
        if ("zh".equals(targetLanguage)) {
            langVariants = new String[]{"zh", "zh-Hans", "zh-Hant", "zh-CN", "zh-TW", "zh-HK", 
                                       "zh-Hans-en-GB", "zh-Hant-en-GB", "zh-Hans-en", "zh-Hant-en",
                                       "zh-Hans-en-US", "zh-Hant-en-US"};
        } else if ("ja".equals(targetLanguage)) {
            langVariants = new String[]{"ja", "ja-JP", "jpn", "ja-en-GB", "ja-en", "ja-en-US"};
        } else if ("en".equals(targetLanguage)) {
            langVariants = new String[]{"en", "en-GB", "en-US", "en-AU", "en-CA", "en-IN", "en-NZ", "en-ZA"};
        } else {
            langVariants = new String[]{targetLanguage};
        }

        log.info("查找{}字幕文件: baseFilename={}, 检查变体: {}", targetLanguage, baseFilename, String.join(", ", langVariants));

        for (String lang : langVariants) {
            Path vttPath = subtitlesDirPath.resolve(baseFilename + "." + lang + SUBTITLE_EXTENSION);
            log.debug("检查文件: {}", vttPath.getFileName());
            
            if (Files.exists(vttPath) && Files.isRegularFile(vttPath)) {
                log.info("找到{}字幕文件: {}", targetLanguage, vttPath.getFileName());
                try {
                    // 直接验证文件，不使用多次尝试
                    if (Files.isReadable(vttPath)) {
                        long fileSize = Files.size(vttPath);
                        if (fileSize > 100) { // 提高最小文件大小要求
                            // 强制同步文件系统
                            try {
                                // 尝试使用Java NIO的force同步
                                java.nio.channels.FileChannel.open(vttPath, 
                                    java.nio.file.StandardOpenOption.READ).force(true);
                            } catch (Exception e) {
                                log.warn("文件同步尝试失败: {}", e.getMessage());
                                // 忽略错误继续处理
                            }
                            
                            String content = Files.readString(vttPath);
                            if (content.contains("WEBVTT") && content.contains("-->")) {
                                int timeMarksCount = countTimeMarkers(content);
                                if (timeMarksCount >= 2) {
                                    // 新增：检查是否包含实际的字幕文本内容
                                    if (hasActualSubtitleContent(content)) {
                                        log.info("字幕文件已验证可用: {}, 包含{}个时间点，包含实际内容", 
                                                vttPath.getFileName(), timeMarksCount);
                                        
                                        String subtitlePath = "/subtitles/" + vttPath.getFileName().toString();
                                        log.info("返回字幕路径: {}", subtitlePath);
                                        return subtitlePath;
                                    } else {
                                        log.warn("字幕文件只包含时间戳，没有实际文本内容: {}", 
                                                vttPath.getFileName());
                                    }
                                } else {
                                    log.warn("字幕文件不包含足够的时间标记 ({}个): {}", 
                                            timeMarksCount, vttPath.getFileName());
                                }
                            } else {
                                log.warn("字幕文件似乎不是有效的VTT格式: {}", vttPath.getFileName());
                            }
                        } else {
                            log.warn("字幕文件过小({}字节): {}", fileSize, vttPath.getFileName());
                        }
                    } else {
                        log.warn("字幕文件不可读: {}", vttPath.getFileName());
                    }
                } catch (Exception e) {
                    log.error("检查字幕文件时发生错误: {}", vttPath.getFileName(), e);
                }
            }
        }
        
        log.warn("未找到有效的{}字幕文件", targetLanguage);
        return null;
    }
    
    /**
     * 统计VTT内容中的时间标记数量
     */
    private int countTimeMarkers(String vttContent) {
        Pattern pattern = Pattern.compile("\\d{2}:\\d{2}:\\d{2}\\.\\d{3} --> \\d{2}:\\d{2}:\\d{2}\\.\\d{3}");
        Matcher matcher = pattern.matcher(vttContent);
        
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        
        return count;
    }

    /**
     * 检查字幕文件是否包含实际的文本内容
     * @param vttContent VTT文件内容
     * @return 是否包含实际内容
     */
    private boolean hasActualSubtitleContent(String vttContent) {
        if (vttContent == null || vttContent.isEmpty()) {
            return false;
        }
        
        String[] lines = vttContent.split("\n");
        boolean headerPassed = false;
        int contentLines = 0;
        
        for (String line : lines) {
            // 跳过头部信息
            if (!headerPassed) {
                if (line.trim().isEmpty()) {
                    headerPassed = true;
                }
                continue;
            }
            
            // 跳过空行和时间戳行
            if (line.trim().isEmpty() || line.contains("-->")) {
                continue;
            }
            
            // 检查是否为有效的字幕文本行
            String cleanedLine = line.replaceAll("<[^>]*>", "").trim();
            if (!cleanedLine.isEmpty() && !cleanedLine.matches("\\d+")) {
                contentLines++;
            }
        }
        
        // 至少需要有5行有效的字幕内容才认为是有效的
        boolean hasContent = contentLines >= 5;
        log.debug("字幕文件内容检查: 有效内容行数={}, 是否有效={}", contentLines, hasContent);
        return hasContent;
    }

    /**
     * 测试清理功能（用于调试）
     * @param testSubtitleFileId 测试用的字幕文件ID
     * @return 清理测试结果
     */
    public Map<String, Object> testCleanupFunctionality(String testSubtitleFileId) {
        Map<String, Object> testResult = new HashMap<>();
        
        try {
            log.info("开始测试清理功能: testSubtitleFileId={}", testSubtitleFileId);
            
            // 列出清理前的文件
            Path subtitlesPath = Paths.get(subtitlesDirectory).toAbsolutePath();
            if (Files.exists(subtitlesPath) && Files.isDirectory(subtitlesPath)) {
                File[] beforeFiles = subtitlesPath.toFile().listFiles((dir, name) -> 
                    name.toLowerCase().endsWith(SUBTITLE_EXTENSION));
                
                int beforeCount = beforeFiles != null ? beforeFiles.length : 0;
                log.info("清理前字幕文件数量: {}", beforeCount);
                
                // 记录清理前的文件名
                List<String> beforeFileNames = new ArrayList<>();
                if (beforeFiles != null) {
                    for (File file : beforeFiles) {
                        beforeFileNames.add(file.getName());
                    }
                }
                
                // 执行清理
                Map<String, Object> cleanupResult = cleanupSubtitles(testSubtitleFileId);
                
                // 列出清理后的文件
                File[] afterFiles = subtitlesPath.toFile().listFiles((dir, name) -> 
                    name.toLowerCase().endsWith(SUBTITLE_EXTENSION));
                
                int afterCount = afterFiles != null ? afterFiles.length : 0;
                log.info("清理后字幕文件数量: {}", afterCount);
                
                // 记录清理后的文件名
                List<String> afterFileNames = new ArrayList<>();
                if (afterFiles != null) {
                    for (File file : afterFiles) {
                        afterFileNames.add(file.getName());
                    }
                }
                
                // 计算被删除的文件
                List<String> deletedFiles = new ArrayList<>(beforeFileNames);
                deletedFiles.removeAll(afterFileNames);
                
                testResult.put("success", true);
                testResult.put("beforeCount", beforeCount);
                testResult.put("afterCount", afterCount);
                testResult.put("deletedCount", beforeCount - afterCount);
                testResult.put("deletedFiles", deletedFiles);
                testResult.put("cleanupResult", cleanupResult);
                testResult.put("beforeFiles", beforeFileNames);
                testResult.put("afterFiles", afterFileNames);
                
                log.info("清理测试完成: 删除了{}个文件, 删除的文件: {}", 
                        deletedFiles.size(), deletedFiles);
                
            } else {
                testResult.put("success", false);
                testResult.put("message", "字幕目录不存在");
            }
            
        } catch (Exception e) {
            log.error("清理功能测试失败", e);
            testResult.put("success", false);
            testResult.put("error", e.getMessage());
        }
        
        return testResult;
    }
} 