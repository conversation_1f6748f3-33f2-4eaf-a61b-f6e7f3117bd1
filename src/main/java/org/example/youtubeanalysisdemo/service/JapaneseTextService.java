package org.example.youtubeanalysisdemo.service;

import com.atilika.kuromoji.ipadic.Token;
import com.atilika.kuromoji.ipadic.Tokenizer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日语文本处理服务
 * 负责日语文本的分析、处理和转换功能
 */
@Service
public class JapaneseTextService {
    private static final Logger logger = LoggerFactory.getLogger(JapaneseTextService.class);
    
    private static final Tokenizer tokenizer;

    static {
        try {
            tokenizer = new Tokenizer();
            logger.info("Kuromoji Tokenizer 初始化成功");
            System.out.println("Kuromoji Tokenizer 初始化成功");
        } catch (Exception e) {
            logger.error("Kuromoji Tokenizer 初始化失败", e);
            System.err.println("Kuromoji Tokenizer 初始化失败: " + e.getMessage());
            throw new RuntimeException("无法初始化 Kuromoji", e);
        }
    }

    /**
     * 判断字符串是否全部为片假名
     */
    public boolean isAllKatakana(String text) {
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            // 片假名字符的Unicode范围:0x30A0-0x30FF
            if (!(c >= '\u30A0' && c <= '\u30FF') && !Character.isWhitespace(c) && c != '・' && c != 'ー') {
                return false;
            }
        }
        return !text.isEmpty();
    }

    /**
     * 处理第三方服务返回的分析结果，只保留从词性开始的部分
     */
    public String processAnalysisResult(String originalWord, String analysis) {
        if (analysis == null || analysis.isEmpty()) {
            return "";
        }
        logger.debug("Processing analysis result: originalWord='{}', fullAnalysis='{}'", originalWord, analysis);

        int wordStartIndex = analysis.indexOf(originalWord);
        if (wordStartIndex == -1) {
            // 原始词未在分析结果中找到，尝试查找第一个 "【" 作为备选方案
            int fallbackIndex = analysis.indexOf("【");
            if (fallbackIndex != -1) {
                String processed = analysis.substring(fallbackIndex).trim();
                logger.warn("Original word '{}' not found. Using fallback to strip content before first '【'. Result: '{}'", originalWord, processed);
                return processed;
            }
            logger.warn("Original word '{}' not found and no fallback '【' found. Returning full analysis.", originalWord);
            return analysis; 
        }

        // 从 originalWord 之后开始搜索冒号
        int searchFromIndex = wordStartIndex + originalWord.length();
        int colonIndex = -1;

        for (int i = searchFromIndex; i < analysis.length(); i++) {
            char currentChar = analysis.charAt(i);
            // 如果遇到冒号，则标记位置并跳出循环
            if (currentChar == ':' || currentChar == ':') { 
                colonIndex = i;
                break;
            }
            // 如果遇到括号（用于注音），则跳过括号内的内容
            if (currentChar == '（') {
                int closingParenthesisIndex = analysis.indexOf('）', i);
                if (closingParenthesisIndex != -1) {
                    i = closingParenthesisIndex; // 继续从右括号之后搜索
                } else {
                    // 未找到匹配的右括号，按字符继续搜索
                }
            }
        }

        if (colonIndex != -1) {
            // 如果找到冒号，则提取冒号之后的内容作为分析结果
            String processedAnalysis = analysis.substring(colonIndex + 1).trim();
            logger.debug("Successfully processed analysis. Result: '{}'", processedAnalysis);
            return processedAnalysis;
        }

        // 如果上述逻辑未能找到合适的冒号，则再次尝试查找第一个 "【"
        int fallbackIndex = analysis.indexOf("【");
        if (fallbackIndex != -1) {
            // 确保 "【" 在原始词之后，避免错误截断
            if (fallbackIndex >= wordStartIndex) {
                String processed = analysis.substring(fallbackIndex).trim();
                logger.warn("Colon not found after word '{}'. Using fallback to strip content before first '【'. Result: '{}'", originalWord, processed);
                return processed;
            }
        }

        logger.warn("Could not find a suitable colon or '【' to split the analysis for word '{}'. Returning full analysis.", originalWord);
        return analysis; // 如果未能找到冒号或备选的"【"，则返回原始分析结果
    }

    /**
     * 使用Kuromoji进行日语词形还原
     */
    public String normalizeJapaneseWord(String word) {
        if (word == null || word.trim().isEmpty()) {
            return word;
        }
        
        try {
            logger.debug("开始日语词形还原处理: {}", word);
            
            // 特殊情况处理:一些常见的变形映射
            Map<String, String> specialCases = new HashMap<>();
            specialCases.put("行っ", "行く");
            specialCases.put("来", "来る");
            specialCases.put("来た", "来る");
            specialCases.put("来まし", "来る");
            specialCases.put("お越し", "お越しになる");
            specialCases.put("下さい", "下さる");
            specialCases.put("ください", "くださる");
            specialCases.put("いただけ", "いただく");
            specialCases.put("頂け", "頂く");
            specialCases.put("頂き", "頂く");
            specialCases.put("でした", "です");
            specialCases.put("でしょう", "です");
            specialCases.put("だった", "だ");
            specialCases.put("だろう", "だ");
            
            // 先检查特殊情况映射
            for (Map.Entry<String, String> entry : specialCases.entrySet()) {
                if (word.equals(entry.getKey())) {
                    logger.debug("使用特殊映射: {} -> {}", word, entry.getValue());
                    return entry.getValue();
                }
            }
            
            // 使用Kuromoji进行形态素分析
            List<Token> tokens = tokenizer.tokenize(word);
            if (tokens.isEmpty()) {
                logger.debug("无法分析词组，返回原始输入: {}", word);
                return word; // 如果无法分析，返回原始词组
            }
            
            logger.debug("Kuromoji分析结果，共{}个Token:", tokens.size());
            for (int i = 0; i < tokens.size(); i++) {
                Token token = tokens.get(i);
                logger.debug("Token[{}]: surface='{}', baseForm='{}', pos='{}', reading='{}'", 
                           i, token.getSurface(), token.getBaseForm(), 
                           token.getPartOfSpeechLevel1(), token.getReading());
            }
            
            // 对于单个Token的情况，直接返回其原形
            if (tokens.size() == 1) {
                Token token = tokens.get(0);
                String baseForm = token.getBaseForm();
                // 如果原形是"*"或空，则保留原始形式
                String result = "*".equals(baseForm) || baseForm.isEmpty() ? word : baseForm;
                logger.debug("单个Token词形还原: {} -> {}", word, result);
                return result;
            }
            
            // 对于复合词/短语，特别处理日语的进行时态和其他常见模式
            
            // 1. 处理动词+ている的进行时态模式
            if (tokens.size() >= 2) {
                // 查找第一个动词Token
                for (int i = 0; i < tokens.size(); i++) {
                    Token token = tokens.get(i);
                    if ("動詞".equals(token.getPartOfSpeechLevel1())) {
                        String baseForm = token.getBaseForm();
                        if (!"*".equals(baseForm) && !baseForm.isEmpty()) {
                            // 检查是否是进行时态模式 (动词+て+いる)
                            if (isProgressiveForm(tokens, i)) {
                                logger.debug("识别为进行时态，返回动词原形: {} -> {}", word, baseForm);
                                return baseForm;
                            }
                            
                            // 检查是否是其他动词变形模式
                            if (isVerbConjugation(tokens, i)) {
                                logger.debug("识别为动词变形，返回动词原形: {} -> {}", word, baseForm);
                                return baseForm;
                            }
                        }
                    }
                }
            }
            
            // 2. 处理形容词变形
            for (Token token : tokens) {
                if ("形容詞".equals(token.getPartOfSpeechLevel1())) {
                    String baseForm = token.getBaseForm();
                    if (!"*".equals(baseForm) && !baseForm.isEmpty()) {
                        logger.debug("识别为形容词变形，返回形容词原形: {} -> {}", word, baseForm);
                        return baseForm;
                    }
                }
            }
            
            // 3. 如果没有找到明确的变形模式，返回第一个有效的原形
            for (Token token : tokens) {
                String baseForm = token.getBaseForm();
                if (!"*".equals(baseForm) && !baseForm.isEmpty() && !baseForm.equals(token.getSurface())) {
                    logger.debug("使用第一个有效原形: {} -> {}", word, baseForm);
                    return baseForm;
                }
            }
            
            // 4. 如果没有找到任何变形，返回原始输入
            logger.debug("未发现变形，返回原始输入: {}", word);
            return word;
            
        } catch (Exception e) {
            logger.error("Kuromoji词形还原处理失败: {}", e.getMessage(), e);
            return word; // 出错时返回原始输入
        }
    }
    
    /**
     * 判断是否为进行时态模式 (动词+て+いる)
     */
    private boolean isProgressiveForm(List<Token> tokens, int verbIndex) {
        if (verbIndex >= tokens.size() - 1) {
            return false;
        }
        
        // 检查动词后面是否有"て"或"で"
        boolean hasTeForm = false;
        for (int i = verbIndex + 1; i < tokens.size(); i++) {
            String surface = tokens.get(i).getSurface();
            if ("て".equals(surface) || "で".equals(surface)) {
                hasTeForm = true;
                break;
            }
        }
        
        if (!hasTeForm) {
            return false;
        }
        
        // 检查是否有"いる"
        for (int i = verbIndex + 1; i < tokens.size(); i++) {
            Token token = tokens.get(i);
            String surface = token.getSurface();
            String baseForm = token.getBaseForm();
            
            if ("いる".equals(surface) || "いる".equals(baseForm) || 
                "い".equals(surface) || "る".equals(surface)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 判断是否为动词变形模式
     */
    private boolean isVerbConjugation(List<Token> tokens, int verbIndex) {
        if (verbIndex >= tokens.size() - 1) {
            return false;
        }
        
        // 检查动词后面是否有助动词或其他变形标记
        for (int i = verbIndex + 1; i < tokens.size(); i++) {
            Token token = tokens.get(i);
            String pos = token.getPartOfSpeechLevel1();
            
            if ("助動詞".equals(pos) || "動詞".equals(pos)) {
                return true;
            }
        }
        
        return false;
    }
} 