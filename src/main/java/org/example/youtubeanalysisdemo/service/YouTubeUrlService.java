package org.example.youtubeanalysisdemo.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * YouTube URL处理服务
 * 负责YouTube URL的验证和视频ID提取
 */
@Slf4j
@Service
public class YouTubeUrlService {

    /**
     * 验证URL是否为有效的YouTube URL
     * 
     * @param url 待验证的URL
     * @return true如果是有效的YouTube URL，否则false
     */
    public boolean isValidYoutubeUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        String regex = "^(https?://)?(www\\.)?(youtube\\.com|youtu\\.?be)/.+$";
        boolean isValid = Pattern.compile(regex).matcher(url).matches();
        
        log.debug("URL验证结果: {} -> {}", url, isValid);
        return isValid;
    }

    /**
     * 从YouTube URL中提取视频ID
     * 
     * @param url YouTube URL
     * @return 视频ID，如果提取失败返回null
     */
    public String extractVideoId(String url) {
        if (url == null || url.trim().isEmpty()) {
            log.warn("URL为空，无法提取视频ID");
            return null;
        }
        
        String pattern = "(?<=watch\\?v=|/videos/|embed\\/|youtu.be\\/|\\/v\\/|\\/e\\/|watch\\?v%3D|watch\\?feature=player_embedded&v=|%2Fvideos%2F|embed%\u200C\u200B2F|youtu.be%2F|%2Fv%2F)[^#\\&\\?\\n]*";
        Matcher matcher = Pattern.compile(pattern).matcher(url);
        
        String videoId = matcher.find() ? matcher.group() : null;
        
        if (videoId != null) {
            log.debug("成功提取视频ID: {} -> {}", url, videoId);
        } else {
            log.warn("无法从URL提取视频ID: {}", url);
        }
        
        return videoId;
    }
} 