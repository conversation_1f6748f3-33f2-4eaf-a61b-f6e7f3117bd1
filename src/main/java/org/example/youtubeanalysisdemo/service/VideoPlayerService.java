package org.example.youtubeanalysisdemo.service;

import lombok.extern.slf4j.Slf4j;
import org.example.youtubeanalysisdemo.model.PlayHistory;
import org.example.youtubeanalysisdemo.model.User;
import org.example.youtubeanalysisdemo.repository.PlayHistoryRepository;
import org.example.youtubeanalysisdemo.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 视频播放业务服务
 * 负责处理视频播放相关的业务逻辑
 */
@Slf4j
@Service
public class VideoPlayerService {

    @Autowired
    private YouTubeUrlService youTubeUrlService;
    
    @Autowired
    private SubtitleDownloadService subtitleDownloadService;
    
    @Autowired
    private SubtitleFileService subtitleFileService;
    
    @Autowired
    private PlayHistoryRepository playHistoryRepository;
    
    @Autowired
    private UserRepository userRepository;

    /**
     * 处理视频播放请求的业务逻辑
     * 
     * @param youtubeUrl YouTube URL
     * @return 包含视频信息的结果对象
     */
    public VideoPlayerResult processVideoRequest(String youtubeUrl) {
        // 验证YouTube URL
        if (!youTubeUrlService.isValidYoutubeUrl(youtubeUrl)) {
            return VideoPlayerResult.error("invalid_url", "无效的YouTube URL");
        }

        // 提取视频ID
        String videoId = youTubeUrlService.extractVideoId(youtubeUrl);
        if (videoId == null) {
            return VideoPlayerResult.error("invalid_video_id", "无法提取视频ID");
        }

        // 生成会话ID
        String sessionId = UUID.randomUUID().toString();

        return VideoPlayerResult.success(videoId, sessionId);
    }

    /**
     * 处理最近视频播放请求
     * 
     * @param principal OAuth2用户信息
     * @return 包含最近视频信息的结果对象
     */
    public VideoPlayerResult processRecentVideoRequest(OAuth2User principal) {
        // 验证用户登录状态
        if (principal == null) {
            log.error("用户未登录，无法获取最近观看的视频");
            return VideoPlayerResult.error("not_logged_in", "用户未登录");
        }
        
        // 获取用户的googleId
        String googleId = principal.getAttribute("sub");
        log.info("尝试获取用户最近视频，googleId: {}", googleId);
        
        // 检查可能的ID属性
        if (googleId == null) {
            googleId = principal.getAttribute("id");
            log.info("从'id'属性获取googleId: {}", googleId);
        }
        
        if (googleId == null) {
            log.error("用户认证信息中缺少googleId，认证详情: {}", principal.getAttributes());
            return VideoPlayerResult.error("invalid_auth", "用户认证信息无效");
        }
        
        Optional<User> userOpt = userRepository.findByGoogleId(googleId);
        
        if (userOpt.isEmpty()) {
            log.error("在数据库中未找到用户信息，googleId: {}", googleId);
            return VideoPlayerResult.error("user_not_found", "用户信息未找到");
        }
        
        User user = userOpt.get();
        log.info("成功获取用户: ID={}, Name={}, Email={}", user.getId(), user.getName(), user.getEmail());
        
        // 获取最近一条播放历史
        List<PlayHistory> histories = playHistoryRepository.findByUserOrderByPlayedAtDesc(user, PageRequest.of(0, 1));
        
        if (histories.isEmpty()) {
            log.warn("用户没有播放历史记录，userId: {}", user.getId());
            return VideoPlayerResult.error("no_history", "没有播放历史记录");
        }
        
        // 构建YouTube URL
        String videoId = histories.get(0).getVideoId();
        log.info("找到用户最近播放的视频ID: {}", videoId);
        
        // 生成会话ID
        String sessionId = UUID.randomUUID().toString();

        return VideoPlayerResult.success(videoId, sessionId);
    }

    /**
     * 异步获取视频字幕
     * 
     * @param videoId 视频ID
     * @param sessionId 会话ID
     * @return 字幕获取结果
     */
    public SubtitleFetchResult fetchSubtitles(String videoId, String sessionId) {
        try {
            // 先清理可能存在的无效字幕文件
            subtitleFileService.cleanupInvalidSubtitles(sessionId);
            
            // 下载字幕（已优化为并行下载）
            Map<String, String> subtitleFiles = subtitleDownloadService.downloadSubtitlesVtt(videoId, sessionId);
            
            // 构建结果
            SubtitleFetchResult result = new SubtitleFetchResult();
            result.setSuccess(true);
            result.setSubtitleJa(subtitleFiles.getOrDefault("ja", ""));
            result.setSubtitleZh(subtitleFiles.getOrDefault("zh", ""));
            
            // 添加警告信息
            if (!subtitleFiles.containsKey("ja") && !subtitleFiles.containsKey("zh")) {
                result.setSubtitleWarning("无法下载任何字幕，视频将无字幕显示");
                log.warn("无法下载任何字幕，视频ID: {}", videoId);
            } else if (!subtitleFiles.containsKey("ja")) {
                result.setSubtitleWarning("无法下载日文字幕");
                log.warn("无法下载日文字幕，视频ID: {}", videoId);
            } else if (!subtitleFiles.containsKey("zh")) {
                result.setSubtitleWarning("无法下载中文字幕");
                log.warn("无法下载中文字幕，视频ID: {}", videoId);
            } else {
                log.info("成功下载所有字幕，视频ID: {}", videoId);
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取字幕失败", e);
            SubtitleFetchResult result = new SubtitleFetchResult();
            result.setSuccess(false);
            result.setSubtitleWarning("字幕获取失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 清理字幕文件
     * 
     * @param sessionId 会话ID
     * @return 清理结果
     */
    public SubtitleCleanupResult cleanupSubtitles(String sessionId) {
        if (sessionId == null || sessionId.trim().isEmpty()) {
            return SubtitleCleanupResult.error("无效的会话ID");
        }

        try {
            String message = subtitleFileService.cleanupSubtitleFiles(sessionId);
            return SubtitleCleanupResult.success(message);
        } catch (Exception e) {
            log.error("清理字幕文件时发生错误，sessionId: {}", sessionId, e);
            return SubtitleCleanupResult.error("清理字幕时发生内部错误");
        }
    }

    /**
     * 视频播放结果类
     */
    public static class VideoPlayerResult {
        private boolean success;
        private String videoId;
        private String sessionId;
        private String errorCode;
        private String errorMessage;

        public static VideoPlayerResult success(String videoId, String sessionId) {
            VideoPlayerResult result = new VideoPlayerResult();
            result.success = true;
            result.videoId = videoId;
            result.sessionId = sessionId;
            return result;
        }

        public static VideoPlayerResult error(String errorCode, String errorMessage) {
            VideoPlayerResult result = new VideoPlayerResult();
            result.success = false;
            result.errorCode = errorCode;
            result.errorMessage = errorMessage;
            return result;
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getVideoId() { return videoId; }
        public String getSessionId() { return sessionId; }
        public String getErrorCode() { return errorCode; }
        public String getErrorMessage() { return errorMessage; }
    }

    /**
     * 字幕获取结果类
     */
    public static class SubtitleFetchResult {
        private boolean success;
        private String subtitleJa;
        private String subtitleZh;
        private String subtitleWarning;
        private String message;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getSubtitleJa() { return subtitleJa; }
        public void setSubtitleJa(String subtitleJa) { this.subtitleJa = subtitleJa; }
        public String getSubtitleZh() { return subtitleZh; }
        public void setSubtitleZh(String subtitleZh) { this.subtitleZh = subtitleZh; }
        public String getSubtitleWarning() { return subtitleWarning; }
        public void setSubtitleWarning(String subtitleWarning) { this.subtitleWarning = subtitleWarning; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    /**
     * 字幕清理结果类
     */
    public static class SubtitleCleanupResult {
        private boolean success;
        private String message;

        public static SubtitleCleanupResult success(String message) {
            SubtitleCleanupResult result = new SubtitleCleanupResult();
            result.success = true;
            result.message = message;
            return result;
        }

        public static SubtitleCleanupResult error(String message) {
            SubtitleCleanupResult result = new SubtitleCleanupResult();
            result.success = false;
            result.message = message;
            return result;
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
    }
} 