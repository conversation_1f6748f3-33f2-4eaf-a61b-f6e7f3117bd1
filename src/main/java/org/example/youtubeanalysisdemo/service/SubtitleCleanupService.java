package org.example.youtubeanalysisdemo.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

/**
 * 字幕文件清理服务
 * 定时清理超过24小时的VTT文件
 */
@Service
public class SubtitleCleanupService {

    private static final Logger logger = LoggerFactory.getLogger(SubtitleCleanupService.class);
    
    // 字幕文件存储路径
    @Value("${subtitle.storage.path:src/main/resources/static/subtitles}")
    private String subtitleStoragePath;
    
    // 文件保留时间（小时）
    @Value("${subtitle.retention.hours:24}")
    private int retentionHours;

    /**
     * 定时任务：每天凌晨00:00执行
     * 清理超过指定时间的VTT文件
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void cleanupOldSubtitleFiles() {
        logger.info("开始执行字幕文件清理任务...");
        
        Path directory = Paths.get(subtitleStoragePath);
        
        // 确保目录存在
        if (!Files.exists(directory)) {
            logger.warn("字幕存储目录不存在: {}", directory.toAbsolutePath());
            return;
        }
        
        // 计算截止时间（当前时间减去保留时间）
        Instant cutoffTime = Instant.now().minus(Duration.ofHours(retentionHours));
        AtomicInteger deletedCount = new AtomicInteger();
        
        try (Stream<Path> files = Files.list(directory)) {
            // 遍历目录中的所有文件
            files.filter(Files::isRegularFile)
                 .filter(path -> path.toString().endsWith(".vtt"))
                 .forEach(file -> {
                     try {
                         // 获取文件属性
                         BasicFileAttributes attrs = Files.readAttributes(file, BasicFileAttributes.class);
                         
                         // 获取文件创建时间和最后修改时间
                         Instant creationTime = attrs.creationTime().toInstant();
                         Instant lastModifiedTime = attrs.lastModifiedTime().toInstant();
                         
                         // 使用较早的时间作为文件的"年龄"
                         Instant fileTime = creationTime.isBefore(lastModifiedTime) ? creationTime : lastModifiedTime;
                         
                         // 如果文件超过保留时间，则删除
                         if (fileTime.isBefore(cutoffTime)) {
                             logger.info("删除过期字幕文件: {}, 创建时间: {}", file.getFileName(), fileTime);
                             Files.delete(file);
                             deletedCount.getAndIncrement();
                         }
                     } catch (IOException e) {
                         logger.error("处理文件时出错: " + file.getFileName(), e);
                     }
                 });
        } catch (IOException e) {
            logger.error("列出目录文件时出错", e);
        }
        
        logger.info("字幕文件清理任务完成，共删除 {} 个过期文件", deletedCount);
    }
}
