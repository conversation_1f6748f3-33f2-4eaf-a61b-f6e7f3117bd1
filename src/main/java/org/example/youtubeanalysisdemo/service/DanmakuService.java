package org.example.youtubeanalysisdemo.service;

import org.example.youtubeanalysisdemo.entity.DanmakuComment;
import org.example.youtubeanalysisdemo.model.User;
import org.example.youtubeanalysisdemo.repository.DanmakuCommentRepository;
import org.example.youtubeanalysisdemo.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * 弹幕服务类
 * 处理弹幕的发送、获取、过滤等业务逻辑
 */
@Service
@Transactional
public class DanmakuService {
    
    private static final Logger logger = LoggerFactory.getLogger(DanmakuService.class);
    
    // 弹幕内容限制
    private static final int MAX_CONTENT_LENGTH = 500;
    private static final int MIN_CONTENT_LENGTH = 1;
    
    // 频率限制配置
    private static final int MAX_DANMAKU_PER_MINUTE = 10;
    private static final int MAX_DANMAKU_PER_HOUR = 100;
    
    // 敏感词过滤（简单实现）
    private static final Pattern SENSITIVE_PATTERN = Pattern.compile(
        ".*(?:垃圾|傻[逼叉]|stupid|idiot|fuck).*", Pattern.CASE_INSENSITIVE);
    
    @Autowired
    private DanmakuCommentRepository danmakuCommentRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    /**
     * 发送弹幕
     * @param videoId 视频ID
     * @param content 弹幕内容
     * @param videoTimestamp 视频时间戳
     * @param userId 用户ID
     * @param color 弹幕颜色
     * @param type 弹幕类型
     * @return 发送结果
     */
    public DanmakuSendResult sendDanmaku(String videoId, String content, Double videoTimestamp, 
                                        Long userId, String color, Integer type) {
        try {
            // 参数验证
            if (!StringUtils.hasText(videoId) || !StringUtils.hasText(content) || 
                videoTimestamp == null || userId == null) {
                return DanmakuSendResult.error("参数不能为空");
            }
            
            // 验证用户是否存在
            Optional<User> userOpt = userRepository.findById(userId);
            if (!userOpt.isPresent()) {
                return DanmakuSendResult.error("用户不存在");
            }
            
            // 内容长度验证
            if (content.length() < MIN_CONTENT_LENGTH || content.length() > MAX_CONTENT_LENGTH) {
                return DanmakuSendResult.error("弹幕内容长度必须在1-500字符之间");
            }
            
            // 内容过滤
            if (SENSITIVE_PATTERN.matcher(content).matches()) {
                return DanmakuSendResult.error("弹幕内容包含敏感词汇");
            }
            
            // 频率限制检查
            if (!checkRateLimit(userId)) {
                return DanmakuSendResult.error("发送频率过快，请稍后再试");
            }
            
            // 创建弹幕对象
            DanmakuComment danmaku = new DanmakuComment(videoId, content, videoTimestamp, userId);
            
            // 设置可选参数
            if (StringUtils.hasText(color) && isValidColor(color)) {
                danmaku.setColor(color);
            }
            if (type != null && isValidType(type)) {
                danmaku.setType(type);
            }
            
            // 保存弹幕
            DanmakuComment savedDanmaku = danmakuCommentRepository.save(danmaku);
            
            logger.info("用户 {} 在视频 {} 的 {} 秒处发送弹幕: {}", 
                       userId, videoId, videoTimestamp, content);
            
            return DanmakuSendResult.success(savedDanmaku);
            
        } catch (Exception e) {
            logger.error("发送弹幕失败", e);
            return DanmakuSendResult.error("发送弹幕失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取视频弹幕
     * @param videoId 视频ID
     * @param page 页码
     * @param size 每页大小
     * @return 弹幕列表
     */
    public Page<DanmakuComment> getDanmakuList(String videoId, int page, int size) {
        if (!StringUtils.hasText(videoId)) {
            throw new IllegalArgumentException("视频ID不能为空");
        }
        
        Pageable pageable = PageRequest.of(page, size);
        return danmakuCommentRepository.findByVideoIdAndStatusOrderByVideoTimestampAsc(
            videoId, 1, pageable);
    }
    
    /**
     * 获取指定时间范围的弹幕
     * @param videoId 视频ID
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 弹幕列表
     */
    public List<DanmakuComment> getDanmakuByTimeRange(String videoId, Double startTime, Double endTime) {
        if (!StringUtils.hasText(videoId) || startTime == null || endTime == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        return danmakuCommentRepository.findByVideoIdAndVideoTimestampBetweenAndStatusOrderByVideoTimestampAsc(
            videoId, startTime, endTime, 1);
    }
    
    /**
     * 获取视频弹幕总数
     * @param videoId 视频ID
     * @return 弹幕总数
     */
    public long getDanmakuCount(String videoId) {
        if (!StringUtils.hasText(videoId)) {
            return 0;
        }
        return danmakuCommentRepository.countByVideoIdAndStatus(videoId, 1);
    }
    
    /**
     * 获取用户发送的弹幕
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 弹幕列表
     */
    public Page<DanmakuComment> getUserDanmakuList(Long userId, int page, int size) {
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        Pageable pageable = PageRequest.of(page, size);
        return danmakuCommentRepository.findByUserIdAndStatusOrderByCreateTimeDesc(
            userId, 1, pageable);
    }
    
    /**
     * 删除弹幕（软删除）
     * @param danmakuId 弹幕ID
     * @param userId 用户ID（只能删除自己的弹幕）
     * @return 删除结果
     */
    public boolean deleteDanmaku(Long danmakuId, Long userId) {
        if (danmakuId == null || userId == null) {
            return false;
        }
        
        try {
            Optional<DanmakuComment> danmakuOpt = danmakuCommentRepository.findById(danmakuId);
            if (!danmakuOpt.isPresent()) {
                return false;
            }
            
            DanmakuComment danmaku = danmakuOpt.get();
            
            // 验证用户权限
            if (!danmaku.getUserId().equals(userId)) {
                logger.warn("用户 {} 尝试删除其他用户的弹幕 {}", userId, danmakuId);
                return false;
            }
            
            // 软删除
            danmaku.setStatus(0);
            danmakuCommentRepository.save(danmaku);
            
            logger.info("用户 {} 删除弹幕 {}", userId, danmakuId);
            return true;
            
        } catch (Exception e) {
            logger.error("删除弹幕失败", e);
            return false;
        }
    }
    
    /**
     * 检查发送频率限制
     * @param userId 用户ID
     * @return 是否允许发送
     */
    private boolean checkRateLimit(Long userId) {
        LocalDateTime now = LocalDateTime.now();
        
        // 检查每分钟限制
        LocalDateTime oneMinuteAgo = now.minusMinutes(1);
        long countPerMinute = danmakuCommentRepository.countByUserIdAndCreateTimeBetween(
            userId, oneMinuteAgo, now);
        
        if (countPerMinute >= MAX_DANMAKU_PER_MINUTE) {
            logger.warn("用户 {} 每分钟发送弹幕次数超限: {}", userId, countPerMinute);
            return false;
        }
        
        // 检查每小时限制
        LocalDateTime oneHourAgo = now.minusHours(1);
        long countPerHour = danmakuCommentRepository.countByUserIdAndCreateTimeBetween(
            userId, oneHourAgo, now);
        
        if (countPerHour >= MAX_DANMAKU_PER_HOUR) {
            logger.warn("用户 {} 每小时发送弹幕次数超限: {}", userId, countPerHour);
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证颜色格式
     * @param color 颜色值
     * @return 是否有效
     */
    private boolean isValidColor(String color) {
        return color != null && color.matches("^#[0-9A-Fa-f]{6}$");
    }
    
    /**
     * 验证弹幕类型
     * @param type 弹幕类型
     * @return 是否有效
     */
    private boolean isValidType(Integer type) {
        return type != null && type >= 1 && type <= 3;
    }
    
    /**
     * 清理过期弹幕
     * @param expireDays 过期天数
     * @return 清理的弹幕数量
     */
    public int cleanupExpiredDanmaku(int expireDays) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(expireDays);
        int count = danmakuCommentRepository.markExpiredDanmakuAsDeleted(expireTime);
        logger.info("清理了 {} 条过期弹幕", count);
        return count;
    }
    
    /**
     * 弹幕发送结果类
     */
    public static class DanmakuSendResult {
        private boolean success;
        private String message;
        private DanmakuComment danmaku;
        
        private DanmakuSendResult(boolean success, String message, DanmakuComment danmaku) {
            this.success = success;
            this.message = message;
            this.danmaku = danmaku;
        }
        
        public static DanmakuSendResult success(DanmakuComment danmaku) {
            return new DanmakuSendResult(true, "发送成功", danmaku);
        }
        
        public static DanmakuSendResult error(String message) {
            return new DanmakuSendResult(false, message, null);
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public DanmakuComment getDanmaku() { return danmaku; }
    }
} 