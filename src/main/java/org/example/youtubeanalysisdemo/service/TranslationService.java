package org.example.youtubeanalysisdemo.service;

import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.models.ChatCompletion;
import com.openai.models.ChatCompletionCreateParams;
import org.example.youtubeanalysisdemo.dto.SubtitleSegment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 文本翻译服务，调用DashScope API进行单句翻译
 */
@Service
public class TranslationService {

    private static final Logger logger = LoggerFactory.getLogger(TranslationService.class);

    @Value("${dashscope.api.key:}")
    private String dashscopeApiKey;

    private final String modelName = "qwen-plus-latest";
    private static final String DASHSCOPE_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1";
    private static final int MAX_CONCURRENT_REQUESTS = 6; // 最大并发请求数
    private static final int MAX_RETRY_ATTEMPTS = 3; // 最大重试次数
    private static final long RETRY_DELAY_MS = 1000; // 重试延迟（毫秒）
    private static final long REQUEST_DELAY_MS = 200; // 请求间隔（毫秒）

    private final ExecutorService executorService = Executors.newFixedThreadPool(MAX_CONCURRENT_REQUESTS);

    /**
     * 使用DashScope API进行单句字幕翻译
     * 支持多线程并行处理，每次只翻译一句，带重试机制
     * @param vttContent VTT文件内容
     * @param targetLanguage 目标语言代码
     * @return 翻译后的VTT内容
     * @throws Exception 如果翻译过程中出错
     */
    public String translateSubtitlesWithDashScope(String vttContent, String targetLanguage) throws Exception {
        logger.info("开始使用DashScope API翻译字幕，目标语言: {}, 最大并发数: {}", targetLanguage, MAX_CONCURRENT_REQUESTS);
        
        if (dashscopeApiKey == null || dashscopeApiKey.isEmpty()) {
            throw new Exception("DashScope API Key 未配置");
        }
        
        // 解析VTT文件，提取字幕段落
        List<SubtitleSegment> segments = parseVttContent(vttContent);
        if (segments.isEmpty()) {
            logger.warn("未找到有效的字幕内容");
            return vttContent;
        }
        
        logger.info("解析到 {} 条字幕，开始单句翻译", segments.size());
        
        // 为每个字幕段落创建翻译任务
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        AtomicInteger completedCount = new AtomicInteger(0);
        
        for (SubtitleSegment segment : segments) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    translateSingleSegment(segment, targetLanguage);
                    int completed = completedCount.incrementAndGet();
                    if (completed % 10 == 0) {
                        logger.info("翻译进度: {}/{}", completed, segments.size());
                    }
                } catch (Exception e) {
                    logger.error("字幕段落 {} 翻译失败", segment.getSequenceNumber(), e);
                    // 翻译失败时保留原文
                    segment.setTranslatedText(segment.getOriginalText());
                }
            }, executorService);
            futures.add(future);
        }
        
        // 等待所有翻译完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        
        // 重新构建VTT文件
        String translatedVtt = buildTranslatedVtt(segments);
        
        logger.info("字幕翻译完成，共处理 {} 条字幕", segments.size());
        return translatedVtt;
    }
    
    /**
     * 翻译单个字幕段落，带重试机制
     */
    private void translateSingleSegment(SubtitleSegment segment, String targetLanguage) throws Exception {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                logger.debug("翻译字幕段落 {} (尝试 {}/{}): {}", 
                    segment.getSequenceNumber(), attempt, MAX_RETRY_ATTEMPTS, segment.getOriginalText());
                
                OpenAIClient client = OpenAIOkHttpClient.builder()
                        .apiKey(dashscopeApiKey)
                        .baseUrl(DASHSCOPE_API_URL)
                        .build();
                
                String prompt = createSingleTranslationPrompt(segment.getOriginalText(), targetLanguage);
                
                ChatCompletionCreateParams params = ChatCompletionCreateParams.builder()
                        .addUserMessage(prompt)
                        .model(modelName)
                        .build();
                
                ChatCompletion chatCompletion = client.chat().completions().create(params);
                String result = chatCompletion.choices().get(0).message().content().orElse("");
                
                // 清理翻译结果
                String translatedText = cleanTranslationResult(result);
                
                if (translatedText.isEmpty()) {
                    throw new Exception("翻译结果为空");
                }
                
                segment.setTranslatedText(translatedText);
                
                logger.debug("字幕段落 {} 翻译成功: {}", segment.getSequenceNumber(), translatedText);
                
                // 添加延迟避免API限流
                Thread.sleep(REQUEST_DELAY_MS);
                
                return; // 翻译成功，退出重试循环
                
            } catch (Exception e) {
                lastException = e;
                logger.warn("字幕段落 {} 翻译失败 (尝试 {}/{}): {}", 
                    segment.getSequenceNumber(), attempt, MAX_RETRY_ATTEMPTS, e.getMessage());
                
                if (attempt < MAX_RETRY_ATTEMPTS) {
                    try {
                        // 重试前等待
                        Thread.sleep(RETRY_DELAY_MS * attempt);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new Exception("翻译被中断", ie);
                    }
                }
            }
        }
        
        // 所有重试都失败了
        throw new Exception("字幕段落 " + segment.getSequenceNumber() + " 翻译失败，已重试 " + MAX_RETRY_ATTEMPTS + " 次", lastException);
    }
    
    /**
     * 创建单句翻译提示词
     */
    private String createSingleTranslationPrompt(String originalText, String targetLanguage) {
        String targetLanguageName = getLanguageName(targetLanguage);
        
        return String.format(
            "你是一个专业的翻译。请将以下字幕文本翻译成%s。\n\n" +
            "要求：\n" +
            "只返回翻译结果，不要添加任何其他内容\n" +
            "原文：%s\n\n" +
            "翻译结果：",
            targetLanguageName, originalText
        );
    }
    
    /**
     * 清理翻译结果，去除多余的标点和说明
     */
    private String cleanTranslationResult(String result) {
        if (result == null) {
            return "";
        }
        
        // 去除首尾空白
        result = result.trim();
        
        // 去除可能的引号
        if (result.startsWith("\"") && result.endsWith("\"")) {
            result = result.substring(1, result.length() - 1);
        }
        if (result.startsWith("'") && result.endsWith("'")) {
            result = result.substring(1, result.length() - 1);
        }
        
        // 去除可能的"翻译结果："等前缀
        result = result.replaceAll("^(翻译结果：|翻译：|结果：)", "");
        
        // 再次去除首尾空白
        result = result.trim();
        
        return result;
    }
    
    /**
     * 解析VTT文件内容，提取字幕段落
     */
    private List<SubtitleSegment> parseVttContent(String vttContent) {
        List<SubtitleSegment> segments = new ArrayList<>();
        String[] lines = vttContent.split("\\r?\\n");
        
        int sequenceNumber = 1;
        int i = 0;
        
        while (i < lines.length) {
            // 跳过WEBVTT标记和空行
            if (lines[i].trim().equals("WEBVTT") || lines[i].trim().isEmpty()) {
                i++;
                continue;
            }
            
            // 查找时间戳行
            if (lines[i].contains("-->")) {
                String timeStamp = lines[i].trim();
                i++;
                
                // 收集字幕文本
                StringBuilder subtitleText = new StringBuilder();
                while (i < lines.length && !lines[i].trim().isEmpty()) {
                    if (subtitleText.length() > 0) {
                        subtitleText.append(" ");
                    }
                    subtitleText.append(lines[i].trim());
                    i++;
                }
                
                String text = subtitleText.toString().trim();
                if (!text.isEmpty()) {
                    segments.add(new SubtitleSegment(timeStamp, text, sequenceNumber++));
                }
            } else {
                i++;
            }
        }
        
        return segments;
    }
    
    /**
     * 重新构建翻译后的VTT文件
     */
    private String buildTranslatedVtt(List<SubtitleSegment> segments) {
        StringBuilder vttBuilder = new StringBuilder("WEBVTT\n\n");
        
        for (SubtitleSegment segment : segments) {
            vttBuilder.append(segment.getTimeStamp()).append("\n");
            vttBuilder.append(segment.getTranslatedText()).append("\n\n");
        }
        
        return vttBuilder.toString();
    }
    
    /**
     * 获取语言名称
     */
    private String getLanguageName(String languageCode) {
        switch (languageCode.toLowerCase()) {
            case "zh":
            case "zh-cn":
                return "中文";
            case "en":
                return "英语";
            case "ja":
                return "日语";
            case "ko":
                return "韩语";
            case "fr":
                return "法语";
            case "de":
                return "德语";
            case "es":
                return "西班牙语";
            case "ru":
                return "俄语";
            default:
                return "目标语言";
        }
    }
    
    /**
     * 关闭线程池
     */
    public void shutdown() {
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
} 