package org.example.youtubeanalysisdemo.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PreDestroy;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.*;

/**
 * 字幕下载服务
 * 负责使用yt-dlp下载YouTube视频字幕
 */
@Slf4j
@Service
public class SubtitleDownloadService {

    @Autowired
    private SubtitleFileService subtitleFileService;

    // 创建线程池执行器用于并行下载字幕
    private final ExecutorService executorService = Executors.newFixedThreadPool(5);

    /**
     * 下载字幕文件 - 优化版（并行下载）
     * 
     * @param videoId 视频ID
     * @param sessionId 会话ID
     * @return 下载的字幕文件路径映射
     */
    public Map<String, String> downloadSubtitlesVtt(String videoId, String sessionId) {
        Map<String, String> downloadedPaths = new ConcurrentHashMap<>();
        
        try {
            subtitleFileService.ensureSubtitlesDirectoryExists();
        } catch (IOException e) {
            log.error("创建字幕目录失败", e);
            return downloadedPaths;
        }

        String baseFilename = videoId + "_" + sessionId;
        Path subtitlesDirPath = subtitleFileService.getSubtitlesDirectoryPath();
        String ytDlpOutputTemplate = subtitlesDirPath.resolve(baseFilename).toString();

        // 创建两个并行任务，分别下载日文和中文字幕
        List<CompletableFuture<Map<String, String>>> futures = new ArrayList<>();

        // 任务1:下载日文字幕
        CompletableFuture<Map<String, String>> jaFuture = CompletableFuture.supplyAsync(() -> 
            downloadLanguageSubtitle(videoId, "ja", ytDlpOutputTemplate, baseFilename), executorService);
        
        // 任务2:下载中文字幕
        CompletableFuture<Map<String, String>> zhFuture = CompletableFuture.supplyAsync(() -> 
            downloadLanguageSubtitle(videoId, "zh", ytDlpOutputTemplate, baseFilename), executorService);
        
        futures.add(jaFuture);
        futures.add(zhFuture);
        
        // 等待所有下载任务完成，最多等待25秒
        try {
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.get(25, TimeUnit.SECONDS);
            
            // 合并结果
            for (CompletableFuture<Map<String, String>> future : futures) {
                try {
                    Map<String, String> result = future.get();
                    downloadedPaths.putAll(result);
                } catch (ExecutionException | InterruptedException e) {
                    log.error("获取字幕下载结果时出错", e);
                    if (e instanceof InterruptedException) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        } catch (TimeoutException e) {
            log.error("字幕下载任务超时", e);
            // 取消所有未完成的任务
            for (CompletableFuture<Map<String, String>> future : futures) {
                if (!future.isDone()) {
                    future.cancel(true);
                }
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("等待字幕下载任务完成时出错", e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
        }

        return downloadedPaths;
    }

    /**
     * 下载指定语言的字幕
     */
    private Map<String, String> downloadLanguageSubtitle(String videoId, String language, 
                                                        String ytDlpOutputTemplate, String baseFilename) {
        Map<String, String> result = new HashMap<>();
        log.info("开始并行下载{}字幕: videoId={}", getLanguageName(language), videoId);
        
        String langCodes = getLanguageCodes(language);
        
        // 增加重试机制 - 最多尝试3次
        int maxRetries = 3;
        boolean success = false;
        
        for (int attempt = 1; attempt <= maxRetries && !success; attempt++) {
            log.info("第{}次尝试下载{}字幕: videoId={}", attempt, getLanguageName(language), videoId);
            
            // 尝试下载用户上传的字幕
            if (invokeYtDlpForVtt(videoId, langCodes, ytDlpOutputTemplate, false)) {
                String subtitlePath = subtitleFileService.findVttSubtitlePathForLanguage(baseFilename, language);
                if (subtitlePath != null) {
                    result.put(language, subtitlePath);
                    log.info("成功下载用户上传的{}字幕: {}", getLanguageName(language), subtitlePath);
                    success = true;
                    break;
                } else {
                    log.warn("用户上传的{}字幕下载后验证失败，尝试自动生成字幕", getLanguageName(language));
                }
            }
            
            // 如果没有用户上传的字幕，尝试下载自动生成的
            if (!success && invokeYtDlpForVtt(videoId, langCodes, ytDlpOutputTemplate, true)) {
                String subtitlePath = subtitleFileService.findVttSubtitlePathForLanguage(baseFilename, language);
                if (subtitlePath != null) {
                    result.put(language, subtitlePath);
                    log.info("成功下载自动生成的{}字幕: {}", getLanguageName(language), subtitlePath);
                    success = true;
                    break;
                } else {
                    log.warn("自动生成的{}字幕下载后验证失败", getLanguageName(language));
                }
            }
            
            // 如果当前尝试失败，且还有重试机会，等待后重试
            if (!success && attempt < maxRetries) {
                try {
                    int waitTime = 1000; // 固定1秒间隔，快速重试
                    log.info("{}字幕下载失败，{}ms后进行第{}次重试...", 
                            getLanguageName(language), waitTime, attempt + 1);
                    Thread.sleep(waitTime);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("等待重试时被中断");
                    break;
                }
            }
        }
        
        if (!success) {
            log.error("经过{}次尝试后，{}字幕下载失败", maxRetries, getLanguageName(language));
        }
        
        return result;
    }

    /**
     * 获取语言代码
     */
    private String getLanguageCodes(String language) {
        if ("zh".equals(language)) {
            return "zh,zh-Hans,zh-Hant";
        } else if ("ja".equals(language)) {
            return "ja";
        }
        return language;
    }

    /**
     * 获取语言名称
     */
    private String getLanguageName(String language) {
        if ("zh".equals(language)) {
            return "中文";
        } else if ("ja".equals(language)) {
            return "日文";
        }
        return language;
    }

    /**
     * 调用yt-dlp下载VTT字幕
     */
    private boolean invokeYtDlpForVtt(String videoId, String langCodes, String baseOutputName, boolean autoGenerated) {
        // 添加重试逻辑
        int maxRetries = 2;
        int retryCount = 0;
        boolean success = false;
        
        while (!success && retryCount < maxRetries) {
            retryCount++;
            log.info("执行yt-dlp (VTT) 命令第{}次尝试，视频ID: {}, 语言: {}, 自动生成: {}", 
                     retryCount, videoId, langCodes, autoGenerated);
            
            // 执行命令并获取结果
            success = executeYtDlpCommand(videoId, langCodes, baseOutputName, autoGenerated);
            
            // 如果失败且未达到最大重试次数，等待后重试
            if (!success && retryCount < maxRetries) {
                try {
                    int waitTime = 1000 * retryCount;
                    log.info("yt-dlp命令执行失败，{}ms后进行第{}次重试...", waitTime, retryCount + 1);
                    Thread.sleep(waitTime);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("等待重试时被中断");
                    break;
                }
            }
        }
        
        if (success) {
            log.info("成功下载字幕，共尝试{}次。视频ID: {}, 语言: {}", retryCount, videoId, langCodes);
        } else {
            log.error("字幕下载失败，已尝试{}次。视频ID: {}, 语言: {}", maxRetries, videoId, langCodes);
        }
        
        return success;
    }
    
    /**
     * 执行yt-dlp命令
     */
    private boolean executeYtDlpCommand(String videoId, String langCodes, String baseOutputName, boolean autoGenerated) {
        List<String> command = buildYtDlpCommand(videoId, langCodes, baseOutputName, autoGenerated);

        log.info("执行yt-dlp (VTT) 命令: {}", String.join(" ", command));
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            Process process = processBuilder.start();

            StringBuilder errorOutput = new StringBuilder();
            StringBuilder stdOutput = new StringBuilder();
            
            // 同时读取标准输出和错误输出
            try (
                BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
                BufferedReader stdReader = new BufferedReader(new InputStreamReader(process.getInputStream()))
            ) {
                // 创建两个线程分别读取
                Thread errorThread = new Thread(() -> readStream(errorReader, errorOutput));
                Thread stdThread = new Thread(() -> readStream(stdReader, stdOutput));
                
                // 启动线程
                errorThread.start();
                stdThread.start();
                
                // 等待进程结束
                boolean completed = process.waitFor(20, TimeUnit.SECONDS);
                
                // 确保线程完成读取
                errorThread.join(2000);
                stdThread.join(2000);
                
                if (!completed) {
                    log.warn("yt-dlp (VTT) 命令超时: videoId={}, langCodes={}, auto={}", videoId, langCodes, autoGenerated);
                    process.destroyForcibly();
                    return false;
                }
                
                int exitCode = process.exitValue();
                if (exitCode != 0) {
                    log.warn("yt-dlp (VTT) 命令失败，退出码: {} videoId={}, langCodes={}, auto={}\n错误输出:\n{}\n标准输出:\n{}",
                            exitCode, videoId, langCodes, autoGenerated, 
                            errorOutput.toString(), stdOutput.toString());
                } else {
                    if (stdOutput.length() > 0) {
                        log.info("yt-dlp标准输出: {}", stdOutput);
                    }
                    log.info("成功下载VTT字幕: videoId={}, langCodes={}, auto={}", videoId, langCodes, autoGenerated);
                    
                    // 等待文件系统完成写入
                    try {
                        Thread.sleep(800);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.warn("等待文件系统完成写入时被中断");
                    }
                }
                return exitCode == 0;
            }
        } catch (IOException | InterruptedException e) {
            log.error("执行yt-dlp (VTT) 出错: videoId={}, langCodes={}, auto={}", videoId, langCodes, autoGenerated, e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            return false;
        }
    }

    /**
     * 构建yt-dlp命令
     */
    private List<String> buildYtDlpCommand(String videoId, String langCodes, String baseOutputName, boolean autoGenerated) {
        List<String> command = new ArrayList<>();
        command.add("yt-dlp");
        command.add("--skip-download");
        if (autoGenerated) {
            command.add("--write-auto-subs");
        } else {
            command.add("--write-sub");
        }
        command.add("--sub-langs");
        command.add(langCodes);
        command.add("--sub-format");
        command.add("vtt");

        // 优化下载速度的参数
        command.add("--extractor-args");
        command.add("youtube:player_client=android,youtube:skip=hls,dash,translated_subs,youtube:player_skip=js,youtube:player_skip=configs");
        
        // 更积极的重试和超时设置
        command.add("--retries");
        command.add("2");
        command.add("--socket-timeout");
        command.add("10");
        command.add("--force-ipv4");
        
        // 优化网络连接
        command.add("--concurrent-fragments");
        command.add("5");
        
        // YouTube相关优化
        command.add("--no-check-certificates");
        command.add("--geo-bypass");
        
        // 速度优化
        command.add("--no-playlist");
        command.add("--quiet");
        command.add("--no-simulate");
        command.add("--no-check-formats");
        command.add("--no-warnings");
        command.add("--no-progress");
        command.add("--console-title");
        command.add("--no-config-locations");
        command.add("--ignore-no-formats-error");
        command.add("--output");
        command.add(baseOutputName);
        command.add("https://www.youtube.com/watch?v=" + videoId);

        return command;
    }

    /**
     * 读取流内容
     */
    private void readStream(BufferedReader reader, StringBuilder output) {
        try {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append(System.lineSeparator());
            }
        } catch (IOException e) {
            log.error("读取流时发生异常", e);
        }
    }

    @PreDestroy
    public void shutdown() {
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
} 