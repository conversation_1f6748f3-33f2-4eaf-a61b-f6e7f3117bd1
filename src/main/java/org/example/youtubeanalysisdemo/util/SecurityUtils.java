package org.example.youtubeanalysisdemo.util;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.user.OAuth2User;

public class SecurityUtils {

    /**
     * 获取当前登录用户ID
     * @return 用户ID，如果未登录则返回"anonymous"
     */
    public static String getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication != null && authentication.isAuthenticated()) {
            Object principal = authentication.getPrincipal();
            
            if (principal instanceof OAuth2User) {
                OAuth2User oAuth2User = (OAuth2User) principal;
                // 优先使用email作为用户标识
                String email = oAuth2User.getAttribute("email");
                if (email != null && !email.isEmpty()) {
                    return email;
                }
                
                // 如果没有email，尝试使用id作为唯一标识
                String id = oAuth2User.getAttribute("id");
                if (id != null && !id.isEmpty()) {
                    return id;
                }
                
                // 如果都没有，尝试使用name
                String name = oAuth2User.getAttribute("name");
                if (name != null && !name.isEmpty()) {
                    return name;
                }
            }
            
            // 如果不是OAuth2User或无法获取有效属性，使用principal的字符串表示
            return authentication.getName();
        }
        
        // 如果未登录，返回匿名用户标识
        return "anonymous";
    }
} 