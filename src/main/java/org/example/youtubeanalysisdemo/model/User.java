package org.example.youtubeanalysisdemo.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "users")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private String googleId;
    
    private String name;
    
    private String email;
    
    private String picture;
    
    @Enumerated(EnumType.STRING)
    private AuthProvider provider;
    
    public enum AuthProvider {
        GOOGLE
    }
} 