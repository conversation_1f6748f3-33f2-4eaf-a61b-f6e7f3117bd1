package org.example.youtubeanalysisdemo.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Entity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "user_login_logs", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"user_id", "login_date"})
})
public class UserLoginLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(name = "login_date", nullable = false)
    private LocalDate loginDate;
    
    @Column(name = "login_count", nullable = false)
    private Integer loginCount;
} 