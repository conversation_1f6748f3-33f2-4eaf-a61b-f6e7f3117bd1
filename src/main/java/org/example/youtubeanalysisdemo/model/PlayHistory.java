package org.example.youtubeanalysisdemo.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

@Entity
@Table(name = "play_history")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlayHistory {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    private String videoId;
    
    private String videoTitle;
    
    private String thumbnailUrl;
    
    @Column(nullable = false)
    private LocalDateTime playedAt;
} 