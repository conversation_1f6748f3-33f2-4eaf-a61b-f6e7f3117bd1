# YouTube学习分析后端服务

一个基于Spring Boot的YouTube视频学习分析后端服务，为Chrome扩展和Web应用提供AI驱动的日语学习功能，包括字幕分析、单词学习、场景对话、用户管理等。

## 📁 项目结构

```
src/main/java/org/example/youtubeanalysisdemo/
├── YoutubeAnalysisDemoApplication.java  # Spring Boot应用入口
├── config/                             # 配置类
│   └── SecurityConfig.java             # Spring Security配置
├── controller/                         # 控制器层
│   ├── AuthController.java             # 认证控制器
│   ├── ChatController.java             # AI聊天控制器
│   ├── DialogController.java           # 场景对话控制器
│   ├── ExtensionController.java        # Chrome扩展API控制器
│   ├── LoginLogController.java         # 登录日志控制器
│   ├── PaddleWebhookController.java    # Paddle支付回调控制器
│   ├── PlayHistoryController.java      # 播放历史控制器
│   ├── SubscriptionController.java     # 订阅管理控制器
│   ├── SubtitleController.java         # 字幕分析控制器
│   ├── SubtitleTaggerController.java   # 字幕标记控制器
│   ├── SummaryController.java          # 视频摘要控制器
│   ├── TTSController.java              # 文本转语音控制器
│   ├── TranslationController.java      # 翻译控制器
│   ├── UserController.java             # 用户管理控制器
│   ├── UserStatsController.java        # 用户统计控制器
│   └── VideoPlayerController.java      # 视频播放控制器
├── dto/                                # 数据传输对象
│   ├── SavedItemDTO.java               # 保存项目DTO
│   ├── SubtitleRequest.java            # 字幕请求DTO
│   └── TTSRequest.java                 # TTS请求DTO
├── entity/                             # 实体类
│   ├── LearningContent.java            # 学习内容实体
│   ├── Subscription.java               # 订阅实体
│   └── UserSavedContent.java           # 用户保存内容实体
├── model/                              # 数据模型
│   ├── PlayHistory.java                # 播放历史模型
│   ├── TaggedWord.java                 # 标记单词模型
│   ├── User.java                       # 用户模型
│   └── UserLoginLog.java               # 用户登录日志模型
├── repository/                         # 数据访问层
│   ├── LearningContentRepository.java  # 学习内容仓库
│   ├── PlayHistoryRepository.java      # 播放历史仓库
│   ├── SubscriptionRepository.java     # 订阅仓库
│   ├── UserLoginLogRepository.java     # 登录日志仓库
│   ├── UserRepository.java             # 用户仓库
│   └── UserSavedContentRepository.java # 用户保存内容仓库
├── service/                            # 业务逻辑层
│   ├── AIAnalysisService.java          # AI分析服务
│   ├── JapaneseTextService.java        # 日语文本处理服务
│   ├── LearningContentService.java     # 学习内容服务
│   ├── SubscriptionManagerService.java # 订阅管理服务
│   ├── SubscriptionService.java        # 订阅服务
│   ├── SubtitleCleanupService.java     # 字幕清理服务
│   ├── SubtitleDownloadService.java    # 字幕下载服务
│   ├── SubtitleFileService.java        # 字幕文件服务
│   ├── SubtitleOperationService.java   # 字幕操作服务
│   ├── SubtitleService.java            # 字幕服务
│   ├── TokenService.java               # 令牌服务
│   ├── TranscriptionService.java       # 转录服务
│   ├── TranscriptionTaskService.java   # 转录任务服务
│   ├── TranslationService.java         # 翻译服务
│   ├── UserService.java                # 用户服务
│   ├── VideoPlayerService.java         # 视频播放服务
│   ├── YouTubeUrlService.java          # YouTube URL服务
│   └── YouTubeUtilService.java         # YouTube工具服务
└── util/                               # 工具类
    └── SecurityUtils.java              # 安全工具类
```

## 🚀 主要功能模块

### 1. 用户认证与管理
- **Google OAuth2认证**: 集成Google登录系统
- **用户信息管理**: 用户资料、登录日志、统计数据
- **会话管理**: Spring Session支持
- **权限控制**: Spring Security安全框架

### 2. YouTube视频处理
- **视频URL解析**: 支持多种YouTube URL格式
- **字幕下载**: 自动获取日语和中文字幕
- **字幕清理**: 定时清理过期字幕文件
- **播放历史**: 记录用户观看历史

### 3. AI驱动的学习功能
- **字幕分析**: 使用AI分析日语字幕中的单词和语法
- **单词学习**: 智能单词收藏和学习状态管理
- **场景对话**: AI生成日语对话场景
- **视频摘要**: 基于字幕内容生成视频摘要
- **翻译服务**: 多语言翻译支持

### 4. 日语文本处理
- **形态学分析**: 使用Kuromoji进行日语分词
- **文本标准化**: 日语文本规范化处理
- **语法分析**: 识别和解析日语语法结构

### 5. 订阅与支付
- **Paddle集成**: 支付系统集成
- **订阅管理**: 多层级订阅计划
- **Webhook处理**: 支付状态回调处理

### 6. Chrome扩展支持
- **扩展API**: 专门为Chrome扩展提供的API接口
- **跨域支持**: CORS配置支持扩展访问
- **会话同步**: 扩展与Web应用的会话同步

## 🔧 技术架构

### 核心技术栈
- **Spring Boot 3.4.5**: 应用框架
- **Java 17**: 编程语言
- **Spring Data JPA**: 数据持久化
- **Spring Security**: 安全框架
- **Spring OAuth2 Client**: OAuth2认证
- **H2 Database**: 嵌入式数据库（开发环境）
- **MySQL**: 生产数据库支持

### AI与NLP技术
- **OpenAI API**: GPT模型集成
- **DashScope API**: 阿里云AI服务
- **Kuromoji**: 日语形态学分析器
- **自然语言处理**: 文本分析和处理

### 外部服务集成
- **YouTube API**: 视频信息获取
- **Google OAuth2**: 用户认证
- **Paddle**: 支付处理
- **TTS服务**: 文本转语音

### 数据存储
- **JPA/Hibernate**: ORM框架
- **H2数据库**: 开发和测试
- **MySQL支持**: 生产环境
- **文件存储**: 字幕文件本地存储

## 📋 API接口设计

### 认证相关 (/api/auth)
- `GET /api/auth/user` - 获取当前用户信息
- `POST /api/auth/logout` - 用户登出

### 字幕处理 (/api/subtitle)
- `POST /api/subtitle/analyze` - 分析字幕内容
- `POST /api/subtitle/tag` - 标记字幕单词
- `POST /api/subtitle/favorite` - 收藏单词
- `GET /api/subtitle/list` - 获取收藏列表

### Chrome扩展 (/api/extension)
- `POST /api/extension/subtitles/fetch` - 获取字幕
- `POST /api/extension/subtitles/cleanup` - 清理字幕
- `POST /api/extension/transcribe` - 视频转录

### 学习功能
- `POST /api/dialog/generate` - 生成对话场景
- `POST /api/summary/generate` - 生成视频摘要
- `POST /api/tts/synthesize` - 文本转语音
- `POST /api/translate` - 文本翻译

### 用户数据
- `GET /api/stats/learning` - 学习统计
- `POST /api/play-history/save` - 保存播放历史
- `GET /api/subscription/status` - 订阅状态

## 🔄 业务流程

### 视频学习流程
1. **视频URL输入** → YouTube URL解析和验证
2. **字幕获取** → 自动下载日语/中文字幕
3. **AI分析** → 字幕内容智能分析
4. **学习互动** → 单词收藏、对话生成、摘要制作
5. **进度跟踪** → 学习状态和历史记录

### 用户认证流程
1. **Google OAuth2** → 用户授权登录
2. **用户信息同步** → 获取并存储用户资料
3. **会话管理** → 维护登录状态
4. **权限验证** → API访问权限控制

### 订阅管理流程
1. **订阅选择** → 用户选择订阅计划
2. **支付处理** → Paddle支付集成
3. **状态更新** → Webhook回调处理
4. **权限激活** → 订阅功能启用

## 🛠️ 开发规范

### 代码组织
- **分层架构**: Controller → Service → Repository
- **依赖注入**: Spring IoC容器管理
- **异常处理**: 统一异常处理机制
- **日志记录**: SLF4J + Logback日志框架

### 数据库设计
- **实体关系**: JPA注解定义实体关系
- **数据完整性**: 约束和验证规则
- **性能优化**: 索引和查询优化
- **事务管理**: Spring事务管理

### 安全考虑
- **认证授权**: OAuth2 + JWT令牌
- **CORS配置**: 跨域请求安全控制
- **输入验证**: 参数校验和SQL注入防护
- **敏感数据**: 配置文件敏感信息保护

## 🚀 部署配置

### 环境要求
- **Java 17+**: 运行环境
- **Maven 3.6+**: 构建工具
- **MySQL 8.0+**: 生产数据库（可选）

### 配置文件
- **application.properties**: 主配置文件
- **数据库配置**: H2/MySQL数据源配置
- **API密钥**: DashScope、OpenAI等服务密钥
- **OAuth2配置**: Google认证客户端配置

### 启动命令
```bash
# 开发环境
mvn spring-boot:run

# 生产环境
java -jar target/youtube-analysis-demo-0.0.1-SNAPSHOT.jar
```

## 📊 数据模型设计

### 核心实体关系
```
User (用户)
├── PlayHistory (播放历史) - 一对多
├── UserSavedContent (收藏内容) - 一对多
├── Subscription (订阅) - 一对多
└── UserLoginLog (登录日志) - 一对多

LearningContent (学习内容)
└── UserSavedContent (用户收藏) - 一对多

Subscription (订阅)
├── userId (用户ID) - 外键
├── paddleSubscriptionId (Paddle订阅ID)
├── status (订阅状态)
└── planId (计划ID)
```

### 主要数据表
- **users**: 用户基本信息（Google OAuth2）
- **learning_content**: 学习内容库（单词、语法解析）
- **user_saved_content**: 用户收藏的学习内容
- **play_history**: 用户视频播放历史
- **subscriptions**: 用户订阅信息
- **user_login_logs**: 用户登录日志

## 🔌 与Chrome扩展的集成

### API通信协议
- **基础URL**: `http://localhost:8080`
- **认证方式**: Session Cookie + CORS
- **数据格式**: JSON
- **错误处理**: 统一错误响应格式

### 关键集成点
1. **字幕获取**: 扩展请求 → 后端下载 → 返回字幕内容
2. **内容分析**: 扩展发送文本 → AI分析 → 返回解析结果
3. **用户数据**: 扩展操作 → 后端存储 → 数据同步
4. **会话管理**: 扩展与Web应用共享用户会话

### 扩展配置文件
```javascript
// chrome-extension-demo/config.js
const ExtensionConfig = {
    API_BASE_URL: 'http://localhost:8080',
    API_ENDPOINTS: {
        FETCH_SUBTITLES: '/api/extension/subtitles/fetch',
        ANALYZE_SUBTITLE: '/api/subtitle/analyze',
        FAVORITE_WORD: '/api/subtitle/favorite',
        // ... 更多端点
    }
};
```

## 🎯 学习功能详解

### AI分析引擎
- **单词分析**: 词性、读音、释义、例句
- **语法分析**: 语法结构、用法说明、相关语法
- **上下文理解**: 结合字幕上下文的智能分析
- **难度评估**: 基于JLPT等级的难度标记

### 学习内容管理
- **内容去重**: 基于contentKey的唯一性保证
- **学习状态**: 正在学习(0) / 已学会(1)
- **批量操作**: 支持批量标记、删除操作
- **分页查询**: 大数据量的分页处理

### 个性化推荐
- **学习历史分析**: 基于用户学习行为
- **难度适配**: 根据用户水平调整内容
- **复习提醒**: 智能复习时间安排
- **进度跟踪**: 详细的学习统计数据

## 🔒 安全与性能

### 安全措施
- **OAuth2认证**: Google第三方认证
- **CSRF保护**: Spring Security CSRF防护
- **XSS防护**: 输入输出过滤和转义
- **SQL注入防护**: JPA参数化查询
- **敏感数据保护**: API密钥环境变量管理

### 性能优化
- **数据库索引**: 关键字段索引优化
- **连接池**: HikariCP数据库连接池
- **缓存策略**: 字幕文件本地缓存
- **异步处理**: 长时间任务异步执行
- **分页查询**: 大数据量分页加载

### 监控与日志
- **应用日志**: SLF4J + Logback日志框架
- **性能监控**: Spring Boot Actuator
- **错误追踪**: 详细的异常日志记录
- **用户行为**: 登录、操作行为日志

## 🔄 开发工作流

### 本地开发
1. **环境准备**: Java 17 + Maven + IDE
2. **依赖安装**: `mvn clean install`
3. **配置文件**: 设置API密钥和数据库
4. **启动应用**: `mvn spring-boot:run`
5. **测试验证**: 访问 `http://localhost:8080`

### 代码规范
- **命名约定**: 驼峰命名法，见名知意
- **注释规范**: JavaDoc + 行内注释
- **异常处理**: 统一异常处理和错误响应
- **代码复用**: 公共方法抽取和工具类使用

### 测试策略
- **单元测试**: JUnit 5 + Mockito
- **集成测试**: Spring Boot Test
- **API测试**: Postman + 自动化测试
- **性能测试**: 压力测试和性能分析

## 📈 扩展计划

### 功能扩展
- **多语言支持**: 扩展到韩语、英语等语言学习
- **移动端适配**: 响应式设计和移动端优化
- **社交功能**: 学习社区和用户互动
- **AI助手**: 更智能的学习助手和对话机器人

### 技术升级
- **微服务架构**: 服务拆分和容器化部署
- **云原生**: Kubernetes + Docker部署
- **大数据分析**: 用户行为分析和学习效果评估
- **实时通信**: WebSocket实时功能

### 生态集成
- **第三方平台**: 与更多学习平台集成
- **API开放**: 提供开放API供第三方使用
- **插件生态**: 支持更多浏览器和平台
- **数据导出**: 学习数据导出和备份功能
