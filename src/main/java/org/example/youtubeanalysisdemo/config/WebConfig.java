package org.example.youtubeanalysisdemo.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(WebConfig.class);

    @Value("${subtitles.directory}")
    private String subtitlesDirectory;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 确保字幕目录路径以file:开头，并且以/结尾
        String resourceLocation = subtitlesDirectory;
        if (!resourceLocation.startsWith("file:")) {
            resourceLocation = "file:" + resourceLocation;
        }
        if (!resourceLocation.endsWith("/")) {
            resourceLocation += "/";
        }

        // 创建字幕目录（如果不存在）
        File subtitleDir = new File(subtitlesDirectory);
        if (!subtitleDir.exists()) {
            boolean created = subtitleDir.mkdirs();
            logger.info("字幕目录创建结果: {} -> {}", subtitleDir.getAbsolutePath(), created);
        }

        // 映射 /subtitles/** 路径到实际的字幕文件目录
        registry.addResourceHandler("/subtitles/**")
                .addResourceLocations(resourceLocation)
                .setCachePeriod(0) // 禁用缓存，便于开发调试
                .resourceChain(false); // 禁用资源链，直接提供文件

        logger.info("配置静态资源映射: /subtitles/** -> {}", resourceLocation);
        logger.info("字幕目录绝对路径: {}", subtitleDir.getAbsolutePath());
    }
} 