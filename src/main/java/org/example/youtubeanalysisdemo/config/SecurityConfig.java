package org.example.youtubeanalysisdemo.config;

import org.example.youtubeanalysisdemo.model.User;
import org.example.youtubeanalysisdemo.repository.UserRepository;
import org.example.youtubeanalysisdemo.service.SubscriptionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import java.util.Map;
import java.util.Optional;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    private static final Logger logger = LoggerFactory.getLogger(SecurityConfig.class);
    private final UserRepository userRepository;
    private final SubscriptionService subscriptionService;

    public SecurityConfig(UserRepository userRepository, SubscriptionService subscriptionService) {
        this.userRepository = userRepository;
        this.subscriptionService = subscriptionService;
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        logger.info("配置Spring Security过滤链");
        
        // 配置请求缓存，用于保存原始请求
        HttpSessionRequestCache requestCache = new HttpSessionRequestCache();
        requestCache.setCreateSessionAllowed(true);
        
        http
            .csrf(csrf -> csrf.disable())
            .cors(Customizer.withDefaults())
            .headers(headers -> headers.frameOptions().disable())
            .authorizeHttpRequests(authorize -> authorize
                // 1. 优先处理所有静态资源的公共访问
                .requestMatchers(
                        "/", "/index.html", "/plan.html", "/error", "/favicon.png",
                        "/assets/**", "/css/**", "/js/**", "/images/**", "/shared/**",
                        "/subtitles/**", // 字幕文件
                        "/static/**" // 其他静态资源
                ).permitAll()
                
                // 2. 允许公共访问的API端点
                .requestMatchers(
                        "/api/paddle/webhook", "/api/auth/token-login", "/api/auth/preauth",
                        "/api/user/me", "/api/subtitle/tag", "/api/subtitle/normalize",
                        "/api/translate/word"
                ).permitAll()

                // 3. 允许开发者和特殊用途的路径
                .requestMatchers("/h2-console/**", "/cdn-cgi/**", "/login/**").permitAll()
                
                // 4. 需要认证的API端点
                .requestMatchers("/api/chat/**", "/api/extension/**").authenticated()
                
                // 5. 其他所有请求都需要认证
                .anyRequest().authenticated()
            )
            .requestCache(cache -> cache
                .requestCache(requestCache)
            )
            .sessionManagement(session -> session
                .sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
                .maximumSessions(10)
                .expiredUrl("/index.html")
            )
            .oauth2Login(oauth2 -> oauth2
                .loginPage("/index.html")
                .defaultSuccessUrl("/index.html", true)
                .successHandler((request, response, authentication) -> {
                    logger.info("OAuth2登录成功: {}", authentication.getName());
                    
                    // 获取用户信息并保存到数据库
                    if (authentication.getPrincipal() instanceof OAuth2User) {
                        OAuth2User oauth2User = (OAuth2User) authentication.getPrincipal();
                        Map<String, Object> attributes = oauth2User.getAttributes();
                        String googleId = (String) attributes.get("sub");
                        
                        // 检查用户是否已存在
                        Optional<User> existingUser = userRepository.findByGoogleId(googleId);
                        boolean isNewUser = false;
                        
                        if (!existingUser.isPresent()) {
                            User newUser = new User();
                            newUser.setGoogleId(googleId);
                            newUser.setEmail((String) attributes.get("email"));
                            newUser.setName((String) attributes.get("name"));
                            newUser.setPicture((String) attributes.get("picture"));
                            newUser.setProvider(User.AuthProvider.GOOGLE);
                            userRepository.save(newUser);
                            isNewUser = true;
                            logger.info("创建新用户: {}", googleId);
                            
                            // 为新用户创建7天Premium试用订阅
                            try {
                                subscriptionService.createTrialSubscription(googleId);
                                logger.info("成功为新用户创建试用订阅: {}", googleId);
                            } catch (Exception e) {
                                logger.error("为新用户创建试用订阅失败: {}", googleId, e);
                                // 不影响用户登录流程，继续执行
                            }
                        } else {
                            logger.info("用户已存在，不更新信息: {}", googleId);
                        }
                    }
                    
                    // 获取之前保存的请求
                    String redirectUrl = "/index.html";
                    response.sendRedirect(redirectUrl);
                })
            )
            .logout(logout -> logout
                .logoutRequestMatcher(new AntPathRequestMatcher("/api/auth/logout"))
                .logoutSuccessUrl("/index.html")
                .invalidateHttpSession(true)
                .clearAuthentication(true)
                .deleteCookies("JSESSIONID")
            );
            
        return http.build();
    }
}
