package org.example.youtubeanalysisdemo.config;

import org.example.youtubeanalysisdemo.service.TranslationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PreDestroy;

/**
 * 翻译服务配置类
 * 负责管理翻译服务的生命周期
 */
@Configuration
public class TranslationConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(TranslationConfig.class);
    
    @Autowired
    private TranslationService translationService;
    
    /**
     * 应用关闭时清理资源
     */
    @PreDestroy
    public void cleanup() {
        logger.info("开始清理翻译服务资源");
        try {
            translationService.shutdown();
            logger.info("翻译服务资源清理完成");
        } catch (Exception e) {
            logger.error("清理翻译服务资源时发生错误", e);
        }
    }
} 