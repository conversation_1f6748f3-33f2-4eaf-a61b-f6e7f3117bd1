package org.example.youtubeanalysisdemo.dto;

/**
 * 字幕段落数据结构
 * 用于存储VTT文件中的时间戳和文本内容
 */
public class SubtitleSegment {
    private String timeStamp;
    private String originalText;
    private String translatedText;
    private int sequenceNumber;

    public SubtitleSegment() {}

    public SubtitleSegment(String timeStamp, String originalText, int sequenceNumber) {
        this.timeStamp = timeStamp;
        this.originalText = originalText;
        this.sequenceNumber = sequenceNumber;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getOriginalText() {
        return originalText;
    }

    public void setOriginalText(String originalText) {
        this.originalText = originalText;
    }

    public String getTranslatedText() {
        return translatedText;
    }

    public void setTranslatedText(String translatedText) {
        this.translatedText = translatedText;
    }

    public int getSequenceNumber() {
        return sequenceNumber;
    }

    public void setSequenceNumber(int sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }

    @Override
    public String toString() {
        return "SubtitleSegment{" +
                "timeStamp='" + timeStamp + '\'' +
                ", originalText='" + originalText + '\'' +
                ", translatedText='" + translatedText + '\'' +
                ", sequenceNumber=" + sequenceNumber +
                '}';
    }
} 