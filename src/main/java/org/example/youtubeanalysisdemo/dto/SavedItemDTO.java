package org.example.youtubeanalysisdemo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 收藏内容数据传输对象
 * 用于联合查询用户收藏内容的详细信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SavedItemDTO {
    
    /**
     * 用户收藏关系ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 单词或语法名称
     */
    private String word;
    
    /**
     * 详细解析
     */
    private String analysis;
    
    /**
     * 内容类型
     */
    private String type;
    
    /**
     * 学习状态
     */
    private Integer learned;
    
    /**
     * 收藏时间
     */
    private LocalDateTime createdAt;
}
