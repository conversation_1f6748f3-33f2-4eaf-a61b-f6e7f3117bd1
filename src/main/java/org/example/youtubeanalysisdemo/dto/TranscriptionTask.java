package org.example.youtubeanalysisdemo.dto;

import org.example.youtubeanalysisdemo.service.TranscriptionTaskService;

import java.util.concurrent.CompletableFuture;

/**
 * 转录任务的数据传输对象
 * 封装了转录任务所需的所有信息，并用于在队列中传递
 */
public class TranscriptionTask {
    private final String taskId;
    private final String videoUrl;
    private final String videoId;
    private String language;
    private String status;
    private String currentStep;
    private int progress;
    private String error;
    private String originalSubtitlePath;
    private String translatedSubtitlePath;
    private long createdAt;
    private long completedAt;
    private final CompletableFuture<String> future;
    private final transient TranscriptionTaskService taskService; // 使用transient避免序列化问题
    private final String createdBy;
    private String subtitleFileId; // 关联的字幕文件ID

    public TranscriptionTask(String taskId, String videoUrl, String videoId, String language, String createdBy, TranscriptionTaskService taskService, String subtitleFileId) {
        this.taskId = taskId;
        this.videoUrl = videoUrl;
        this.videoId = videoId;
        this.language = language;
        this.status = "pending";
        this.currentStep = "in_queue";
        this.progress = 0;
        this.createdAt = System.currentTimeMillis();
        this.future = new CompletableFuture<>();
        this.taskService = taskService;
        this.createdBy = createdBy;
        this.subtitleFileId = subtitleFileId;
    }

    public String getTaskId() {
        return taskId;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public String getVideoId() {
        return videoId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCurrentStep() {
        return currentStep;
    }

    public void setCurrentStep(String currentStep) {
        this.currentStep = currentStep;
    }

    public int getProgress() {
        return progress;
    }

    public void setProgress(int progress) {
        this.progress = progress;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getOriginalSubtitlePath() {
        return originalSubtitlePath;
    }

    public void setOriginalSubtitlePath(String originalSubtitlePath) {
        this.originalSubtitlePath = originalSubtitlePath;
    }

    public String getTranslatedSubtitlePath() {
        return translatedSubtitlePath;
    }

    public void setTranslatedSubtitlePath(String translatedSubtitlePath) {
        this.translatedSubtitlePath = translatedSubtitlePath;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(long completedAt) {
        this.completedAt = completedAt;
    }

    public CompletableFuture<String> getFuture() {
        return future;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public String getSubtitleFileId() {
        return subtitleFileId;
    }

    public void setSubtitleFileId(String subtitleFileId) {
        this.subtitleFileId = subtitleFileId;
    }

    /**
     * 更新任务状态的回调方法
     * @param status 新的状态
     * @param currentStep 当前步骤
     * @param progress 进度
     */
    public void updateStatus(String status, String currentStep, int progress) {
        this.status = status;
        this.currentStep = currentStep;
        this.progress = progress;
        if (taskService != null) {
            taskService.updateTaskStatus(taskId, status, currentStep, progress);
        }
    }
} 