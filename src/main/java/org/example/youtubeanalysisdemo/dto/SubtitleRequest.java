package org.example.youtubeanalysisdemo.dto;

import java.util.List;

/**
 * 字幕相关请求DTO类集合
 */
public class SubtitleRequest {

    /**
     * 保存字幕内容请求
     */
    public static class SaveRequest {
        private List<String> contents;
        private List<String> types;
        private String language; // 语言类型：japanese 或 english

        public List<String> getContents() {
            return contents;
        }

        public void setContents(List<String> contents) {
            this.contents = contents;
        }
        
        public List<String> getTypes() {
            return types;
        }

        public void setTypes(List<String> types) {
            this.types = types;
        }
        
        public String getLanguage() {
            return language;
        }

        public void setLanguage(String language) {
            this.language = language;
        }
    }
    
    /**
     * 删除条目请求
     */
    public static class DeleteRequest {
        private List<String> itemIds;

        public List<String> getItemIds() {
            return itemIds;
        }

        public void setItemIds(List<String> itemIds) {
            this.itemIds = itemIds;
        }
    }

    /**
     * 收藏词组请求
     */
    public static class FavoriteRequest {
        private String word;
        private String lang; // 语言类型：japanese 或 english
        
        public String getWord() {
            return word;
        }
        
        public void setWord(String word) {
            this.word = word;
        }
        
        public String getLang() {
            return lang;
        }
        
        public void setLang(String lang) {
            this.lang = lang;
        }
    }
    
    /**
     * 取消收藏请求
     */
    public static class UnfavoriteRequest {
        private String word;
        private String taskId;
        private String lang; // 语言类型：japanese 或 english
        
        public String getWord() {
            return word;
        }
        
        public void setWord(String word) {
            this.word = word;
        }
        
        public String getTaskId() {
            return taskId;
        }
        
        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }
        
        public String getLang() {
            return lang;
        }
        
        public void setLang(String lang) {
            this.lang = lang;
        }
    }

    /**
     * 词形还原请求
     */
    public static class NormalizeRequest {
        private String word;
        private String language; // 语言类型：japanese 或 english
        
        public String getWord() {
            return word;
        }
        
        public void setWord(String word) {
            this.word = word;
        }
        
        public String getLanguage() {
            return language;
        }
        
        public void setLanguage(String language) {
            this.language = language;
        }
    }
} 