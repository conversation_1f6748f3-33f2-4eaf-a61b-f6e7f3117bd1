package org.example.youtubeanalysisdemo.controller;

import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.example.youtubeanalysisdemo.service.VideoPlayerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 视频播放控制器
 * 负责处理视频播放相关的HTTP请求
 */
@Slf4j
@Controller
public class VideoPlayerController {

    @Autowired
    private VideoPlayerService videoPlayerService;

    /**
     * 处理视频播放请求
     */
    @GetMapping("/player")
    public String player(@RequestParam("url") String youtubeUrl, Model model, HttpSession session) {
        try {
            VideoPlayerService.VideoPlayerResult result = videoPlayerService.processVideoRequest(youtubeUrl);
            
            if (!result.isSuccess()) {
                return "redirect:/?error=" + result.getErrorCode();
            }

            // 存储会话信息
            session.setAttribute("videoSessionId", result.getSessionId());
            session.setAttribute("videoId", result.getVideoId());
            session.setAttribute("videoUrl", youtubeUrl);

            // 将数据传递到视图（不再等待字幕下载）
            model.addAttribute("videoId", result.getVideoId());
            model.addAttribute("sessionId", result.getSessionId());
            model.addAttribute("subtitleJa", "");  // 初始为空，由前端异步加载
            model.addAttribute("subtitleZh", "");

            return "player";
        } catch (Exception e) {
            log.error("处理视频请求时出错: {}", youtubeUrl, e);
            return "redirect:/?error=unexpected_error";
        }
    }

    /**
     * 处理最近视频播放请求
     */
    @GetMapping("/player/recent")
    public String recentPlayer(@AuthenticationPrincipal OAuth2User principal, Model model, HttpSession session) {
        try {
            VideoPlayerService.VideoPlayerResult result = videoPlayerService.processRecentVideoRequest(principal);
            
            if (!result.isSuccess()) {
                return "redirect:/?error=" + result.getErrorCode();
            }

            // 存储会话信息
            session.setAttribute("videoSessionId", result.getSessionId());
            session.setAttribute("videoId", result.getVideoId());
            
            // 将数据传递到视图（不再等待字幕下载）
            model.addAttribute("videoId", result.getVideoId());
            model.addAttribute("sessionId", result.getSessionId());
            model.addAttribute("subtitleJa", "");  // 初始为空，由前端异步加载
            model.addAttribute("subtitleZh", "");

            return "player";
        } catch (Exception e) {
            log.error("处理最近视频请求时出错", e);
            return "redirect:/?error=unexpected_error";
        }
    }

    /**
     * 异步获取视频字幕
     */
    @GetMapping("/api/subtitles/fetch")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> fetchSubtitles(HttpSession session) {
        String sessionId = (String) session.getAttribute("videoSessionId");
        String videoId = (String) session.getAttribute("videoId");
        
        if (sessionId == null || videoId == null) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "会话信息不存在，请重新加载页面");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
        
        try {
            VideoPlayerService.SubtitleFetchResult result = videoPlayerService.fetchSubtitles(videoId, sessionId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            
            if (result.isSuccess()) {
                response.put("subtitleJa", result.getSubtitleJa());
                response.put("subtitleZh", result.getSubtitleZh());
                if (result.getSubtitleWarning() != null) {
                    response.put("subtitleWarning", result.getSubtitleWarning());
                }
            } else {
                response.put("message", result.getMessage());
            }
            
            return result.isSuccess() ? 
                ResponseEntity.ok(response) : 
                ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        } catch (Exception e) {
            log.error("异步获取字幕API出错: videoId={}, sessionId={}", videoId, sessionId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取字幕时发生意外错误");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 清理字幕文件（DELETE方法）
     */
    @DeleteMapping("/api/subtitles/cleanup")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> cleanupSubtitlesDelete(HttpSession session) {
        return cleanupSubtitlesInternal(session);
    }

    /**
     * 清理字幕文件（POST方法，支持navigator.sendBeacon）
     */
    @PostMapping("/api/subtitles/cleanup")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> cleanupSubtitlesPost(HttpSession session) {
        return cleanupSubtitlesInternal(session);
    }

    /**
     * 内部清理字幕文件的实际实现
     */
    private ResponseEntity<Map<String, Object>> cleanupSubtitlesInternal(HttpSession session) {
        String sessionId = (String) session.getAttribute("videoSessionId");
        
        Map<String, Object> response = new HashMap<>();
        
        VideoPlayerService.SubtitleCleanupResult result = videoPlayerService.cleanupSubtitles(sessionId);
        
        response.put("success", result.isSuccess());
        response.put("message", result.getMessage());
        
        if (result.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }
} 