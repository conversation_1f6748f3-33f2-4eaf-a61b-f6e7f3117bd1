package org.example.youtubeanalysisdemo.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.example.youtubeanalysisdemo.service.*;
import org.example.youtubeanalysisdemo.strategy.LanguageStrategyFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.security.Principal;
import java.util.*;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.example.youtubeanalysisdemo.service.SubscriptionService;

@Slf4j
@RestController
@RequestMapping("/api/extension")
public class ExtensionController {

    @Autowired
    private YouTubeUtilService youTubeUtilService;
    
    @Autowired
    private SubtitleOperationService subtitleOperationService;
    
    @Autowired
    private TranscriptionTaskService transcriptionTaskService;
    
    @Autowired
    private TranscriptionService transcriptionService;
    
    @Autowired
    private LanguageStrategyFactory languageStrategyFactory;
    
    @Autowired
    private SubscriptionService subscriptionService;

    /**
     * Chrome插件专用字幕获取接口
     */
    @PostMapping("/subtitles/fetch")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> fetchSubtitlesForExtension(
            @RequestBody ExtensionSubtitleRequest request,
            HttpServletRequest httpRequest) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("插件字幕请求: videoUrl={}, language={}, forceNewSession={}", 
                     request.getVideoUrl(), request.getLanguage(), request.isForceNewSession());
            
            // 验证请求参数
            if (request.getVideoUrl() == null || request.getVideoUrl().trim().isEmpty()) {
                response.put("success", false);
                response.put("error", "视频URL不能为空");
                return ResponseEntity.badRequest().body(response);
            }

            // 验证YouTube URL并提取视频ID
            String videoId = youTubeUtilService.validateAndExtractVideoId(request.getVideoUrl());
            if (videoId == null) {
                response.put("success", false);
                response.put("error", "无效的YouTube URL或无法提取视频ID");
                return ResponseEntity.badRequest().body(response);
            }

            // 获取语言策略
            String languageIdentifier = request.getLanguage();
            if (languageIdentifier == null || languageIdentifier.trim().isEmpty()) {
                languageIdentifier = "japanese"; // 默认为日语，保持向后兼容
            }
            
            // 获取语言策略实例
            var languageStrategy = languageStrategyFactory.getStrategy(languageIdentifier);
            String primaryLanguageCode = languageStrategy.getPrimaryLanguage();
            String translationLanguageCode = languageStrategy.getTranslationLanguage();
            String primaryLanguageDisplayName = getLanguageDisplayName(primaryLanguageCode);
            String translationLanguageDisplayName = getLanguageDisplayName(translationLanguageCode);
            
            log.info("开始为插件下载字幕: videoId={}, language={}, primaryCode={}, translationCode={}", 
                     videoId, languageIdentifier, primaryLanguageCode, translationLanguageCode);

            // 生成唯一的字幕文件标识符
            String subtitleFileId = UUID.randomUUID().toString();
            
            // 在HttpSession中存储信息（插件使用临时session）
            HttpSession session = httpRequest.getSession(request.isForceNewSession());
            
            // 如果强制使用新会话，记录日志
            if (request.isForceNewSession()) {
                String oldSessionId = session.getId();
                // 使session失效，确保生成新session
                session.invalidate();
                session = httpRequest.getSession(true);
                log.info("应客户端请求强制创建新会话: 旧ID={}, 新ID={}", oldSessionId, session.getId());
            }
            
            session.setAttribute("videoSubtitleFileId", subtitleFileId);
            session.setAttribute("videoId", videoId);
            session.setAttribute("videoUrl", request.getVideoUrl());
            session.setAttribute("language", languageIdentifier);

            log.info("为插件生成字幕文件ID: {}, HTTP会话ID: {}", subtitleFileId, session.getId());

            // 使用字幕操作服务下载字幕文件，传入语言参数
            Map<String, String> subtitleFiles = subtitleOperationService.downloadSubtitlesVttWithLanguage(videoId, subtitleFileId, languageIdentifier);

            // 构建响应 - 为了保持向后兼容性，仍然使用subtitleJa和subtitleZh作为键名
            // 但是内容根据实际选择的语言填充
            String primarySubtitlePath = subtitleFiles.getOrDefault(primaryLanguageCode, "");
            String translationSubtitlePath = subtitleFiles.getOrDefault(translationLanguageCode, "");
            
            response.put("success", true);
            response.put("videoId", videoId);
            response.put("subtitleFileId", subtitleFileId);
            // 保持键名兼容性：subtitleJa放主语言，subtitleZh放翻译语言
            response.put("subtitleJa", primarySubtitlePath);
            response.put("subtitleZh", translationSubtitlePath);
            response.put("httpSessionId", session.getId());
            
            // 添加语言信息供前端使用
            response.put("primaryLanguage", primaryLanguageCode);
            response.put("primaryLanguageDisplayName", primaryLanguageDisplayName);
            response.put("translationLanguage", translationLanguageCode);
            response.put("translationLanguageDisplayName", translationLanguageDisplayName);

            // 添加警告信息 - 使用动态语言名称
            boolean hasPrimary = !primarySubtitlePath.isEmpty();
            boolean hasTranslation = !translationSubtitlePath.isEmpty();
            
            if (!hasPrimary && !hasTranslation) {
                response.put("warning", "无法下载任何字幕");
                log.warn("插件请求无法下载任何字幕: videoId={}", videoId);
            } else if (!hasPrimary) {
                response.put("warning", "无法下载" + primaryLanguageDisplayName + "字幕");
                log.warn("插件请求无法下载{}字幕: videoId={}", primaryLanguageDisplayName, videoId);
            } else if (!hasTranslation) {
                response.put("warning", "无法下载" + translationLanguageDisplayName + "字幕");
                log.warn("插件请求无法下载{}字幕: videoId={}", translationLanguageDisplayName, videoId);
            } else {
                log.info("插件请求成功下载所有字幕: videoId={}, primary={}, translation={}", 
                         videoId, primaryLanguageDisplayName, translationLanguageDisplayName);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("插件字幕请求处理失败: videoUrl={}", request.getVideoUrl(), e);
            response.put("success", false);
            response.put("error", "服务器内部错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 插件专用字幕清理接口
     */
    @DeleteMapping("/subtitles/cleanup")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> cleanupSubtitlesForExtension(
            @RequestParam String subtitleFileId,
            HttpServletRequest httpRequest) {
        
        try {
            log.info("插件清理字幕请求: subtitleFileId={}", subtitleFileId);

            // 验证subtitleFileId
            if (subtitleFileId == null || subtitleFileId.trim().isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("error", "无效的字幕文件ID");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 使用字幕操作服务清理字幕
            Map<String, Object> result = subtitleOperationService.cleanupSubtitles(subtitleFileId);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("插件清理字幕失败: subtitleFileId={}", subtitleFileId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", "清理失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 清理转录生成的字幕文件
     */
    @DeleteMapping("/transcription/cleanup")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> cleanupTranscriptionFiles(
            @RequestParam String videoId,
            @RequestParam(required = false) String taskId,
            HttpServletRequest httpRequest) {
        
        try {
            log.info("插件清理转录文件请求: videoId={}, taskId={}", videoId, taskId);

            // 验证videoId
            if (videoId == null || videoId.trim().isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("error", "无效的视频ID");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // 使用转录服务清理转录文件
            String result = transcriptionService.cleanupTranscriptionFiles(videoId, taskId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", result);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("插件清理转录文件失败: videoId={}, taskId={}", videoId, taskId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", "清理失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    // 请求体类
    static class ExtensionSubtitleRequest {
        private String videoUrl;
        private String token;
        private boolean forceNewSession;
        private String language; // 新增语言参数

        public String getVideoUrl() {
            return videoUrl;
        }

        public void setVideoUrl(String videoUrl) {
            this.videoUrl = videoUrl;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public boolean isForceNewSession() {
            return forceNewSession;
        }

        public void setForceNewSession(boolean forceNewSession) {
            this.forceNewSession = forceNewSession;
        }

        public String getLanguage() {
            return language;
        }

        public void setLanguage(String language) {
            this.language = language;
        }
    }

    // 视频转录请求
    @PostMapping("/transcribe")
    public ResponseEntity<?> transcribeVideo(
            @RequestBody Map<String, Object> requestBody,
            HttpSession session,
            @AuthenticationPrincipal OAuth2User principal) {
        
        String videoUrl = (String) requestBody.get("videoUrl");
        String language = (String) requestBody.get("language");
        String subtitleFileId = (String) requestBody.get("subtitleFileId");
        
        try {
            // 1. 验证用户认证状态
            if (principal == null) {
                log.warn("用户未认证，无法使用字幕转录功能");
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("error", "AUTHENTICATION_REQUIRED");
                response.put("message", "请先登录后再使用字幕转录功能");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }
            
            // 2. 获取用户ID并验证Premium会员权限
            String googleId = principal.getAttribute("sub");
            if (googleId == null) {
                googleId = principal.getAttribute("id");
            }
            
            if (googleId == null) {
                log.error("无法获取用户ID，认证信息异常");
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("error", "INVALID_USER_INFO");
                response.put("message", "用户认证信息异常，请重新登录");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }
            
            // 3. 检查Premium会员权限
            boolean isPremiumMember = subscriptionService.isPremiumMember(googleId);
            if (!isPremiumMember) {
                log.warn("用户 {} 不是Premium会员，无法使用字幕转录功能", googleId);
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("error", "PREMIUM_REQUIRED");
                response.put("message", "字幕转录功能仅限Premium会员使用，请升级您的会员等级");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            
            log.info("用户 {} 是Premium会员，允许使用字幕转录功能", googleId);

            // 4. 验证请求参数
            if (videoUrl == null || videoUrl.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("error", "视频URL不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 使用转录任务服务创建任务
            String taskId = transcriptionTaskService.createTranscriptionTask(
                videoUrl, 
                language, 
                principal.getName(),
                subtitleFileId
            );
            
            // 异步启动转录过程
            transcriptionTaskService.processTranscriptionTaskAsync(taskId);
            
            // 返回任务ID
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("taskId", taskId);
            response.put("message", "转录任务已提交，正在处理中");
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            log.error("转录请求处理失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", "服务器内部错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    // 获取转录进度
    @GetMapping("/transcribe/progress")
    public ResponseEntity<?> getTranscriptionProgress(
            @RequestParam String taskId) {
        
        try {
            // 使用转录任务服务获取进度
            Map<String, Object> response = transcriptionTaskService.getTranscriptionProgress(taskId);
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (Exception e) {
            log.error("获取转录进度失败: taskId={}", taskId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", "服务器内部错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    // 获取语言显示名称的辅助方法
    private String getLanguageDisplayName(String languageCode) {
        switch (languageCode.toLowerCase()) {
            case "ja":
                return "日文";
            case "en":
                return "英文";
            case "zh":
                return "中文";
            default:
                return languageCode.toUpperCase();
        }
    }

}
