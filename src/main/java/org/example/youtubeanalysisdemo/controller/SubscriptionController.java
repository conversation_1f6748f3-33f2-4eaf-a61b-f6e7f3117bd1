package org.example.youtubeanalysisdemo.controller;

import org.example.youtubeanalysisdemo.entity.Subscription;
import org.example.youtubeanalysisdemo.model.User;
import org.example.youtubeanalysisdemo.repository.UserRepository;
import org.example.youtubeanalysisdemo.service.SubscriptionManagerService;
import org.example.youtubeanalysisdemo.service.SubscriptionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/subscription")
public class SubscriptionController {
    
    private static final Logger logger = LoggerFactory.getLogger(SubscriptionController.class);
    
    @Autowired
    private SubscriptionService subscriptionService;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private SubscriptionManagerService subscriptionManagerService;
    
    @GetMapping
    public ResponseEntity<Map<String, Object>> getUserSubscription(
            @AuthenticationPrincipal OAuth2User principal) {
        Map<String, Object> response = new HashMap<>();
        
        if (principal == null) {
            response.put("authenticated", false);
            return ResponseEntity.ok(response);
        }
        
        String googleId = principal.getAttribute("sub");
        Optional<User> userOpt = userRepository.findByGoogleId(googleId);
        
        if (userOpt.isEmpty()) {
            logger.error("未找到用户: googleId={}", googleId);
            response.put("authenticated", true);
            response.put("subscriptions", Collections.emptyList());
            return ResponseEntity.ok(response);
        }
        
        User user = userOpt.get();
        
        // 先更新用户订阅状态，确保状态是最新的
        subscriptionManagerService.updateUserSubscriptionStatuses(googleId);
        
        // 获取所有非过期订阅（活跃和等待中）
        List<Subscription> activeAndPendingSubscriptions = subscriptionService.getUserActiveAndPendingSubscriptions(googleId);
        
        // 转换为前端友好格式
        List<Map<String, Object>> subscriptionList = activeAndPendingSubscriptions.stream()
            .map(sub -> {
                Map<String, Object> subMap = new HashMap<>();
                subMap.put("id", sub.getId());
                subMap.put("planName", sub.getPlanName());
                subMap.put("description", sub.getDescription());
                subMap.put("status", sub.getStatus());
                subMap.put("startDate", sub.getStartDate());
                subMap.put("endDate", sub.getNextBillDate());
                return subMap;
            })
            .collect(Collectors.toList());
        
        response.put("authenticated", true);
        response.put("user", Map.of(
                "id", user.getId(),
                "name", user.getName(),
                "email", user.getEmail()
        ));
        response.put("subscriptions", subscriptionList);
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/active")
    public ResponseEntity<Map<String, Object>> getUserActiveSubscription(
            @AuthenticationPrincipal OAuth2User principal) {
        Map<String, Object> response = new HashMap<>();
        
        if (principal == null) {
            response.put("authenticated", false);
            response.put("hasActiveSubscription", false);
            return ResponseEntity.ok(response);
        }
        
        String googleId = principal.getAttribute("sub");
        Optional<User> userOpt = userRepository.findByGoogleId(googleId);
        
        if (userOpt.isEmpty()) {
            logger.error("未找到用户: googleId={}", googleId);
            response.put("authenticated", true);
            response.put("hasActiveSubscription", false);
            return ResponseEntity.ok(response);
        }
        
        User user = userOpt.get();
        
        // 先更新用户订阅状态
        subscriptionManagerService.updateUserSubscriptionStatuses(googleId);
        
        // 获取活跃订阅
        Optional<Subscription> activeSubscriptionOpt = 
                subscriptionService.getUserCurrentActiveSubscription(googleId);
        
        response.put("authenticated", true);
        response.put("hasActiveSubscription", activeSubscriptionOpt.isPresent());
        
        if (activeSubscriptionOpt.isPresent()) {
            Subscription activeSub = activeSubscriptionOpt.get();
            response.put("subscription", Map.of(
                    "id", activeSub.getId(),
                    "planName", activeSub.getPlanName(),
                    "description", activeSub.getDescription(),
                    "startDate", activeSub.getStartDate(),
                    "endDate", activeSub.getNextBillDate()
            ));
            
            // 添加下一个订阅信息（如果有）
            Optional<Subscription> nextSubscriptionOpt = subscriptionService.getUserNextPendingSubscription(googleId);
            if (nextSubscriptionOpt.isPresent()) {
                Subscription nextSub = nextSubscriptionOpt.get();
                response.put("nextSubscription", Map.of(
                        "id", nextSub.getId(),
                        "planName", nextSub.getPlanName(),
                        "description", nextSub.getDescription(),
                        "startDate", nextSub.getStartDate(),
                        "endDate", nextSub.getNextBillDate()
                ));
            }
        }
        
        return ResponseEntity.ok(response);
    }
} 