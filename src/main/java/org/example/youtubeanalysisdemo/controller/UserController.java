package org.example.youtubeanalysisdemo.controller;

import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/user")
public class UserController {

    @GetMapping("/me")
    public Map<String, Object> getCurrentUser(@AuthenticationPrincipal OAuth2User principal) {
        Map<String, Object> userInfo = new HashMap<>();
        if (principal != null) {
            userInfo.put("authenticated", true);
            userInfo.put("name", principal.getAttribute("name"));
            userInfo.put("email", principal.getAttribute("email"));
            userInfo.put("picture", principal.getAttribute("picture"));
        } else {
            userInfo.put("authenticated", false);
        }
        return userInfo;
    }

    @GetMapping("/paddle-details")
    public Map<String, Object> getPaddleUserDetails(@AuthenticationPrincipal OAuth2User principal) {
        Map<String, Object> paddleDetails = new HashMap<>();
        if (principal != null) {
            paddleDetails.put("googleId", principal.getAttribute("sub")); // 'sub'通常是Google ID
            paddleDetails.put("email", principal.getAttribute("email"));
        } else {
            // 如果用户未认证，可以返回错误或空对象，具体取决于前端如何处理
            paddleDetails.put("error", "User not authenticated");
        }
        return paddleDetails;
    }
} 