package org.example.youtubeanalysisdemo.controller;

import org.example.youtubeanalysisdemo.dto.SubtitleRequest;
import org.example.youtubeanalysisdemo.service.AIAnalysisService;
import org.example.youtubeanalysisdemo.service.EnglishPosTaggingService;
import org.example.youtubeanalysisdemo.service.JapaneseTextService;
import org.example.youtubeanalysisdemo.service.LearningContentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/subtitle")
public class SubtitleController {
    private static final Logger logger = LoggerFactory.getLogger(SubtitleController.class);

    @Autowired
    private AIAnalysisService aiAnalysisService;
    
    @Autowired
    private EnglishPosTaggingService englishPosTaggingService;
    
    @Autowired
    private JapaneseTextService japaneseTextService;
    
    @Autowired
    private LearningContentService learningContentService;

    @PostMapping("/analyze")
    public ResponseEntity<Map<String, String>> analyzeSubtitle(
            @RequestParam String ja, 
            @RequestParam String zh,
            @RequestParam(required = false, defaultValue = "japanese") String language) {
        logger.info("收到分析请求: ja={}, zh={}, language={}", ja, zh, language);
        System.out.println("收到分析请求: ja=" + ja + ", zh=" + zh + ", language=" + language);

        try {
            if (ja == null || ja.trim().isEmpty()) {
                logger.error("文本参数为空");
                System.err.println("文本参数为空");
                return ResponseEntity.badRequest().body(Map.of("error", "文本参数不能为空"));
            }

            String analysis = aiAnalysisService.analyzeGrammar(ja, language);
            logger.debug("AI 分析结果: {}", analysis);
            System.out.println("AI 分析结果: " + analysis);

            String ai_analysis = analysis.trim().isEmpty() ? zh : analysis;
            logger.debug("提取翻译: {}", ai_analysis);
            System.out.println("提取翻译: " + ai_analysis);

            String markdown = String.format(
                    "%s",
                    ai_analysis
            );
            logger.info("生成 Markdown 成功");
            System.out.println("生成 Markdown 成功");

            return ResponseEntity.ok(Map.of("markdown", markdown));
        } catch (Exception e) {
            logger.error("分析字幕失败: ja={}, zh={}, language={}", ja, zh, language, e);
            System.err.println("分析字幕失败: ja=" + ja + ", zh=" + zh + ", language=" + language + ", error=" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "分析失败: " + e.getMessage()));
        }
    }



    @PostMapping("/save")
    public ResponseEntity<Map<String, String>> saveSubtitles(@RequestBody SubtitleRequest.SaveRequest request) {
        try {
            Map<String, Object> result = learningContentService.saveSubtitles(request);
            return ResponseEntity.ok(Map.of(
                "status", result.get("status").toString(),
                "message", result.get("message").toString()
            ));
        } catch (Exception e) {
            logger.error("保存内容失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("status", "error", "message", "保存失败: " + e.getMessage()));
        }
    }

    @PostMapping("/delete")
    public ResponseEntity<Map<String, String>> deleteItem(@RequestBody SubtitleRequest.DeleteRequest request) {
        try {
            Map<String, String> result = learningContentService.deleteItems(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("删除条目失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("status", "error", "message", "删除失败: " + e.getMessage()));
        }
    }

    @PostMapping("/mark-learned")
    public ResponseEntity<Map<String, String>> markAsLearned(@RequestBody SubtitleRequest.DeleteRequest request) {
        try {
            Map<String, String> result = learningContentService.markAsLearned(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("标记为已学会失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("status", "error", "message", "标记失败: " + e.getMessage()));
        }
    }
    
    @GetMapping("/list")
    public ResponseEntity<List<Map<String, String>>> getSavedContents(
            @RequestParam(required = false, defaultValue = "0") Integer learned) {
        try {
            List<Map<String, String>> result = learningContentService.getSavedContents(learned);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取保存内容失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(List.of());
        }
    }

    @PostMapping("/associate")
    public ResponseEntity<?> associateContent(
            @RequestParam String keyword, 
            @RequestParam(required = false) String type,
            @RequestParam(required = false, defaultValue = "japanese") String lang) {
        logger.info("收到联想请求: keyword={}, type={}, lang={}", keyword, type, lang);
        
        try {
            // 如果type未指定，尝试根据内容判断是单词还是语法
            if (type == null || type.isEmpty()) {
                // 如果内容较短且不包含详细解释，可能是单词
                if (keyword.length() < 15 && !keyword.contains(":")) {
                    type = "word";
                } else {
                    type = "grammar";
                }
            }
            
            String associationResult = aiAnalysisService.generateAssociations(keyword, type, lang);
            logger.info("联想内容生成成功");
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "result", associationResult
            ));
        } catch (Exception e) {
            logger.error("生成联想内容失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of(
                        "success", false,
                        "error", "生成联想内容失败: " + e.getMessage()
                    ));
        }
    }




    @PostMapping("/favorite")
    public ResponseEntity<Map<String, Object>> favoriteWord(@RequestBody SubtitleRequest.FavoriteRequest request) {
        try {
            Map<String, Object> result = learningContentService.favoriteWord(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("收藏词组请求处理失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of(
                        "success", false,
                        "message", "收藏失败: " + e.getMessage()
                    ));
        }
    }
    
    @PostMapping("/unfavorite")
    public ResponseEntity<Map<String, Object>> unfavoriteWord(@RequestBody SubtitleRequest.UnfavoriteRequest request) {
        try {
            Map<String, Object> result = learningContentService.unfavoriteWord(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("取消收藏请求处理失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of(
                        "success", false,
                        "message", "取消收藏失败: " + e.getMessage()
                    ));
        }
    }




    @PostMapping("/normalize")
    public ResponseEntity<Map<String, Object>> normalizeWord(@RequestBody SubtitleRequest.NormalizeRequest request) {
        logger.info("收到词形还原请求: word={}, language={}", request.getWord(), request.getLanguage());
        
        try {
            if (request.getWord() == null || request.getWord().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "message", "词组内容不能为空",
                    "originalWord", ""
                ));
            }
            
            String normalizedWord;
            String language = request.getLanguage();
            
            // 根据语言类型选择不同的词形还原方法
            if ("english".equalsIgnoreCase(language)) {
                // 使用英语词性标注服务进行词形还原
                normalizedWord = englishPosTaggingService.lemmatizePhrase(request.getWord());
                logger.info("英语词形还原结果: {} -> {}", request.getWord(), normalizedWord);
            } else {
                // 默认使用日语文本服务进行形态素分析，获取单词的原形
                normalizedWord = japaneseTextService.normalizeJapaneseWord(request.getWord());
                logger.info("日语词形还原结果: {} -> {}", request.getWord(), normalizedWord);
            }
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "词形还原成功",
                "originalWord", normalizedWord,
                "inputWord", request.getWord(),
                "language", language != null ? language : "japanese"
            ));
        } catch (Exception e) {
            logger.error("词形还原失败: word={}, language={}", request.getWord(), request.getLanguage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of(
                        "success", false,
                        "message", "词形还原失败: " + e.getMessage(),
                        "originalWord", request.getWord(), // 出错时返回原始输入
                        "language", request.getLanguage() != null ? request.getLanguage() : "japanese"
                    ));
        }
    }
}