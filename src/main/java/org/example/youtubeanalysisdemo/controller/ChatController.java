package org.example.youtubeanalysisdemo.controller;

import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.models.ChatCompletion;
import com.openai.models.ChatCompletionCreateParams;
import org.example.youtubeanalysisdemo.service.SubscriptionService;
import org.example.youtubeanalysisdemo.repository.UserRepository;
import org.example.youtubeanalysisdemo.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

@RestController
@RequestMapping("/api/chat")
public class ChatController {

    private static final Logger logger = LoggerFactory.getLogger(ChatController.class);

    @Value("${dashscope.api.key:}")
    private String dashscopeApiKey;
    
    @Value("${subtitles.directory:src/main/resources/static/subtitles}")
    private String subtitlesDirectory;

    @Autowired
    private SubscriptionService subscriptionService;
    
    @Autowired
    private UserRepository userRepository;

    private final String model_name = "qwen-plus-latest";
    private static final String DASHSCOPE_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1";
    
    // 存储每个会话的消息历史，key为sessionId，value为消息列表
    private final Map<String, List<String>> sessionMessages = new ConcurrentHashMap<>();
    
    // 存储每个会话的完整字幕内容，key为sessionId，value为字幕内容
    private final Map<String, String> sessionSubtitles = new ConcurrentHashMap<>();

    private final ObjectMapper objectMapper = new ObjectMapper();

    @PostMapping("/ask")
    public ResponseEntity<Map<String, Object>> askQuestion(
            @RequestBody ChatRequest request,
            @AuthenticationPrincipal OAuth2User principal) {
        logger.info("收到聊天请求: sessionId={}, question={}, subtitleFileId={}, hasSubtitleContent={}",
                request.getSessionId(), request.getQuestion(), request.getSubtitleFileId(),
                request.getSubtitleContent() != null && !request.getSubtitleContent().trim().isEmpty());

        try {
            // 1. 验证用户认证状态
            if (principal == null) {
                logger.warn("用户未认证，无法使用AI助手");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of(
                    "success", false,
                    "error", "AUTHENTICATION_REQUIRED",
                    "message", "请先登录后再使用AI助手功能"
                ));
            }
            
            // 2. 获取用户ID并验证Premium会员权限
            String googleId = principal.getAttribute("sub");
            if (googleId == null) {
                googleId = principal.getAttribute("id");
            }
            
            if (googleId == null) {
                logger.error("无法获取用户ID，认证信息异常");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(Map.of(
                    "success", false,
                    "error", "INVALID_USER_INFO",
                    "message", "用户认证信息异常，请重新登录"
                ));
            }
            
            // 3. 检查Premium会员权限
            boolean isPremiumMember = subscriptionService.isPremiumMember(googleId);
            if (!isPremiumMember) {
                logger.warn("用户 {} 不是Premium会员，无法使用AI助手功能", googleId);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(Map.of(
                    "success", false,
                    "error", "PREMIUM_REQUIRED",
                    "message", "AI助手功能仅限Premium会员使用，请升级您的会员等级"
                ));
            }
            
            logger.info("用户 {} 是Premium会员，允许使用AI助手功能", googleId);

            // 4. 验证请求参数
            if (request.getQuestion() == null || request.getQuestion().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "error", "INVALID_QUESTION",
                    "message", "问题不能为空"
                ));
            }

            if (request.getSessionId() == null || request.getSessionId().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of(
                    "success", false,
                    "error", "INVALID_SESSION",
                    "message", "会话ID不能为空"
                ));
            }

            String sessionId = request.getSessionId();
            String question = request.getQuestion().trim();
            
            // 获取或创建会话消息列表
            List<String> messages = sessionMessages.computeIfAbsent(sessionId, k -> new ArrayList<>());
            
            // 如果是新会话，需要先加载字幕内容并设置系统消息
            if (messages.isEmpty()) {
                String subtitleContent = getOrLoadSubtitleContent(sessionId, request.getSubtitleFileId(), request.getSubtitleContent());
                if (subtitleContent == null || subtitleContent.trim().isEmpty()) {
                    logger.error("无法获取字幕内容，subtitleFileId: {}, 前端内容: {}",
                        request.getSubtitleFileId(),
                        request.getSubtitleContent() != null ? "有内容(" + request.getSubtitleContent().length() + "字符)" : "无内容");

                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                        "success", false,
                        "error", "SUBTITLE_LOAD_FAILED",
                        "message", "无法加载视频字幕内容，请确保视频有可用的字幕或稍后重试"
                    ));
                }
                
                // 设置系统消息，包含完整字幕内容
                String systemMessage = String.format(
                    "你是一个专业的视频内容助手。以下是视频的完整字幕内容：\n\n%s\n\n" +
                    "请基于这个视频的字幕内容回答用户的问题。回答要准确、简洁，并且与视频内容相关。" +
                    "如果用户的问题与视频内容无关，请礼貌地引导用户提问与视频相关的内容。",
                    subtitleContent
                );
                messages.add("system:" + systemMessage);
                logger.info("为会话{}设置了系统消息和完整字幕内容", sessionId);
            }
            
            // 添加用户问题
            messages.add("user:" + question);
            
            // 调用API获取回答
            String answer = callChatAPI(messages);
            
            // 将AI回答添加到消息历史
            messages.add("assistant:" + answer);
            
            logger.info("成功生成回答: {}", answer);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "answer", answer,
                "sessionId", sessionId
            ));
            
        } catch (Exception e) {
            logger.error("处理聊天请求失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of(
                "success", false,
                "error", "PROCESSING_FAILED",
                "message", "处理请求失败: " + e.getMessage()
            ));
        }
    }
    
    @PostMapping("/clear")
    public ResponseEntity<Map<String, Object>> clearSession(@RequestBody Map<String, String> request) {
        String sessionId = request.get("sessionId");
        if (sessionId != null) {
            sessionMessages.remove(sessionId);
            sessionSubtitles.remove(sessionId);
            logger.info("清除会话: {}", sessionId);
        }
        
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "会话已清除"
        ));
    }
    
    // 调用聊天API
    private String callChatAPI(List<String> messages) throws IOException {
        try {
            if (dashscopeApiKey == null || dashscopeApiKey.isEmpty()) {
                throw new IOException("DashScope API Key 未配置");
            }
            
            OpenAIClient client = OpenAIOkHttpClient.builder()
                    .apiKey(dashscopeApiKey)
                    .baseUrl(DASHSCOPE_API_URL)
                    .build();
            
            // 构建完整的对话上下文
            StringBuilder conversationContext = new StringBuilder();
            for (String message : messages) {
                if (message.startsWith("system:")) {
                    conversationContext.append("系统消息：").append(message.substring(7)).append("\n\n");
                } else if (message.startsWith("user:")) {
                    conversationContext.append("用户：").append(message.substring(5)).append("\n\n");
                } else if (message.startsWith("assistant:")) {
                    conversationContext.append("助手：").append(message.substring(10)).append("\n\n");
                }
            }
            
            // 获取最后一个用户问题
            String lastUserQuestion = "";
            for (int i = messages.size() - 1; i >= 0; i--) {
                if (messages.get(i).startsWith("user:")) {
                    lastUserQuestion = messages.get(i).substring(5);
                    break;
                }
            }
            
            String prompt = conversationContext.toString() + 
                           "请基于以上对话上下文，回答用户的最新问题：" + lastUserQuestion;
            
            ChatCompletionCreateParams params = ChatCompletionCreateParams.builder()
                    .addUserMessage(prompt)
                    .model(model_name)
                    .build();
            
            ChatCompletion chatCompletion = client.chat().completions().create(params);
            return chatCompletion.choices().get(0).message().content().orElse("抱歉，我无法回答这个问题。").trim();
            
        } catch (Exception e) {
            logger.error("调用聊天API失败", e);
            throw new IOException("调用聊天API失败: " + e.getMessage(), e);
        }
    }
    
    // 获取或加载字幕内容（优先使用前端传递的内容）
    private String getOrLoadSubtitleContent(String sessionId, String subtitleFileId, String frontendSubtitleContent) {
        // 先检查是否已有字幕内容
        String existingSubtitles = sessionSubtitles.get(sessionId);
        if (existingSubtitles != null) {
            return existingSubtitles;
        }

        String subtitleContent = null;

        try {
            // 优先使用前端传递的字幕内容
            if (frontendSubtitleContent != null && !frontendSubtitleContent.trim().isEmpty()) {
                logger.info("使用前端传递的字幕内容，大小: {} 字节", frontendSubtitleContent.length());

                // 处理字幕内容
                if (frontendSubtitleContent.trim().startsWith("{") || frontendSubtitleContent.trim().startsWith("[")) {
                    // JSON格式的字幕数据（来自前端缓存）
                    logger.info("处理JSON格式的字幕数据");
                    subtitleContent = processJsonSubtitleData(frontendSubtitleContent);
                } else if (frontendSubtitleContent.contains("WEBVTT") || frontendSubtitleContent.contains("-->")) {
                    // VTT格式的字幕数据（来自文件）
                    logger.info("处理VTT格式的字幕数据");
                    subtitleContent = processSubtitles(frontendSubtitleContent);
                } else {
                    // 纯文本格式的字幕数据（来自前端已解析的数据）
                    logger.info("处理纯文本格式的字幕数据");
                    subtitleContent = frontendSubtitleContent.trim();
                }
            } else {
                // 回退到文件读取方式
                logger.info("前端未提供字幕内容，回退到文件读取方式，subtitleFileId: {}", subtitleFileId);

                if (subtitleFileId == null || subtitleFileId.trim().isEmpty() || "frontend-cache".equals(subtitleFileId)) {
                    logger.warn("subtitleFileId无效或为占位符，无法进行文件读取: {}", subtitleFileId);
                    subtitleContent = null;
                } else {
                    subtitleContent = loadSubtitleContentFromFile(subtitleFileId);
                }
            }

            // 缓存字幕内容
            if (subtitleContent != null && !subtitleContent.trim().isEmpty()) {
                sessionSubtitles.put(sessionId, subtitleContent);
                logger.info("字幕内容已缓存到会话: {}, 内容长度: {} 字符", sessionId, subtitleContent.length());
                return subtitleContent;
            } else {
                logger.error("无法获取有效的字幕内容，前端内容: {}, 文件读取结果: {}",
                    frontendSubtitleContent != null ? "有内容(" + frontendSubtitleContent.length() + "字符)" : "无内容",
                    subtitleContent != null ? "有内容(" + subtitleContent.length() + "字符)" : "无内容");
                return null;
            }

        } catch (Exception e) {
            logger.error("加载字幕内容失败，subtitleFileId: {}, 前端内容长度: {}",
                subtitleFileId,
                frontendSubtitleContent != null ? frontendSubtitleContent.length() : 0, e);
            return null;
        }
    }
    
    // 从字幕文件加载内容
    private String loadSubtitleContentFromFile(String subtitleFileId) throws IOException {
        if (subtitleFileId == null || subtitleFileId.trim().isEmpty()) {
            throw new IOException("字幕文件ID不能为空");
        }
        
        // 查找字幕文件
        File subtitleFile = findSubtitleFile(subtitleFileId);
        if (subtitleFile == null) {
            throw new IOException("未找到字幕文件");
        }
        
        // 读取字幕内容
        String subtitleContent = readSubtitleFile(subtitleFile);
        String processedSubtitles = processSubtitles(subtitleContent);
        
        // 直接返回处理后的字幕内容，不再生成摘要
        return processedSubtitles;
    }
    
    // 查找字幕文件
    private File findSubtitleFile(String subtitleFileId) {
        try {
            File subtitlesDir = Paths.get(subtitlesDirectory).toFile();
            
            // 首先尝试精确匹配
            File[] matchingFiles = subtitlesDir.listFiles((dir, name) -> 
                name.contains(subtitleFileId) && name.toLowerCase().endsWith(".vtt"));
            
            if (matchingFiles != null && matchingFiles.length > 0) {
                // 优先选择中文字幕
                for (File file : matchingFiles) {
                    String fileName = file.getName().toLowerCase();
                    if (fileName.contains(".zh.") || fileName.contains(".zh-hans") || 
                        fileName.contains(".zh-hant") || fileName.contains(".zh-cn.") || 
                        fileName.contains(".zh_hans.") || fileName.contains("_zh.")) {
                        return file;
                    }
                }
                return matchingFiles[0];
            }
            
            // 如果没有找到，尝试从subtitleFileId提取videoId
            String videoId = extractVideoIdFromSubtitleFileId(subtitleFileId);
            if (videoId != null) {
                matchingFiles = subtitlesDir.listFiles((dir, name) -> 
                    name.startsWith(videoId + "_") && name.toLowerCase().endsWith(".vtt"));
                
                if (matchingFiles != null && matchingFiles.length > 0) {
                    return matchingFiles[0];
                }
            }
            
        } catch (Exception e) {
            logger.error("查找字幕文件失败", e);
        }
        
        return null;
    }
    
    // 从subtitleFileId提取videoId
    private String extractVideoIdFromSubtitleFileId(String subtitleFileId) {
        if (subtitleFileId.contains("_")) {
            return subtitleFileId.split("_")[0];
        }
        return null;
    }
    
    // 读取字幕文件
    private String readSubtitleFile(File file) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }
    
    // 处理字幕内容，提取纯文本
    private String processSubtitles(String vttContent) {
        if (vttContent == null || vttContent.isEmpty()) {
            return "";
        }
        
        List<String> textLines = new ArrayList<>();
        String[] lines = vttContent.split("\n");
        boolean headerPassed = false;
        
        for (String line : lines) {
            if (!headerPassed) {
                if (line.trim().isEmpty()) {
                    headerPassed = true;
                }
                continue;
            }
            
            if (line.trim().isEmpty() || line.contains("-->") || line.matches("\\d+")) {
                continue;
            }
            
            String cleanedLine = line.replaceAll("<[^>]*>", "").trim();
            if (!cleanedLine.isEmpty()) {
                textLines.add(cleanedLine);
            }
        }
        
        return String.join(" ", textLines);
    }

    // 处理前端传来的JSON格式字幕数据
    private String processJsonSubtitleData(String jsonContent) {
        if (jsonContent == null || jsonContent.isEmpty()) {
            return "";
        }

        try {
            JsonNode rootNode = objectMapper.readTree(jsonContent);
            List<String> textLines = new ArrayList<>();

            // 检查是否有events数组
            JsonNode eventsNode = rootNode.get("events");
            if (eventsNode != null && eventsNode.isArray()) {
                for (JsonNode eventNode : eventsNode) {
                    // 获取segs数组
                    JsonNode segsNode = eventNode.get("segs");
                    if (segsNode != null && segsNode.isArray()) {
                        for (JsonNode segNode : segsNode) {
                            // 获取utf8文本
                            JsonNode utf8Node = segNode.get("utf8");
                            if (utf8Node != null && !utf8Node.isNull()) {
                                String text = utf8Node.asText().trim();
                                if (!text.isEmpty()) {
                                    textLines.add(text);
                                }
                            }
                        }
                    }
                }
            }

            // 连接所有文本行，使用空格分隔
            return String.join(" ", textLines);

        } catch (Exception e) {
            logger.error("解析JSON字幕数据失败", e);
            return "";
        }
    }

    // 从URL中提取视频ID
    private String extractVideoId(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }

        Pattern pattern = Pattern.compile("(?:youtube\\.com/.*[?&]v=|youtu\\.be/)([^\"&?/\\s]{11})");
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }

        return null;
    }

    // 请求体类
    static class ChatRequest {
        private String sessionId;
        private String question;
        private String subtitleFileId;
        private String subtitleContent; // 新增：前端传递的字幕内容

        public String getSessionId() {
            return sessionId;
        }

        public void setSessionId(String sessionId) {
            this.sessionId = sessionId;
        }

        public String getQuestion() {
            return question;
        }

        public void setQuestion(String question) {
            this.question = question;
        }

        public String getSubtitleFileId() {
            return subtitleFileId;
        }

        public void setSubtitleFileId(String subtitleFileId) {
            this.subtitleFileId = subtitleFileId;
        }

        public String getSubtitleContent() {
            return subtitleContent;
        }

        public void setSubtitleContent(String subtitleContent) {
            this.subtitleContent = subtitleContent;
        }
    }
} 