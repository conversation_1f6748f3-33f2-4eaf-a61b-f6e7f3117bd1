package org.example.youtubeanalysisdemo.controller;

import org.example.youtubeanalysisdemo.model.User;
import org.example.youtubeanalysisdemo.model.UserLoginLog;
import org.example.youtubeanalysisdemo.repository.UserLoginLogRepository;
import org.example.youtubeanalysisdemo.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/login")
public class LoginLogController {

    @Autowired
    private UserLoginLogRepository userLoginLogRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @PostMapping("/log")
    public ResponseEntity<?> logUserLogin(@AuthenticationPrincipal OAuth2User principal) {
        if (principal == null) {
            return ResponseEntity.status(401).body(Map.of("error", "未登录"));
        }
        
        String googleId = principal.getAttribute("sub");
        Optional<User> userOpt = userRepository.findByGoogleId(googleId);
        
        if (userOpt.isEmpty()) {
            return ResponseEntity.status(404).body(Map.of("error", "用户不存在"));
        }
        
        User user = userOpt.get();
        LocalDate today = LocalDate.now();
        
        // 查找今天是否已有登录记录
        Optional<UserLoginLog> existingLog = userLoginLogRepository.findByUserAndLoginDate(user, today);
        
        if (existingLog.isPresent()) {
            // 今天已经有登录记录，递增计数
            UserLoginLog log = existingLog.get();
            log.setLoginCount(log.getLoginCount() + 1);
            userLoginLogRepository.save(log);
        } else {
            // 今天第一次登录，创建新记录
            UserLoginLog log = UserLoginLog.builder()
                    .user(user)
                    .loginDate(today)
                    .loginCount(1)
                    .build();
            userLoginLogRepository.save(log);
        }
        
        return ResponseEntity.ok(Map.of("status", "success"));
    }
} 