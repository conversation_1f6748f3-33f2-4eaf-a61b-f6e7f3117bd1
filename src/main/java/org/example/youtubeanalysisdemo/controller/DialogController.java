package org.example.youtubeanalysisdemo.controller;

import com.openai.client.OpenAIClient;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.models.ChatCompletion;
import com.openai.models.ChatCompletionCreateParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.example.youtubeanalysisdemo.service.MistralDialogService;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/dialog")
public class DialogController {
    private static final Logger logger = LoggerFactory.getLogger(DialogController.class);

    @Value("${dashscope.api.key:}")
    private String dashscopeApiKey;

    @Autowired
    private MistralDialogService mistralDialogService;

    private static final String DASHSCOPE_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1";

    // 保存用户收藏的对话内容
    private static final List<String> savedDialogs = new ArrayList<>();

    @PostMapping("/generate")
    public ResponseEntity<Map<String, Object>> generateDialog(
            @RequestParam String scenario,
            @RequestParam(required = false, defaultValue = "japanese") String language) {
        logger.info("收到场景对话生成请求: scenario={}, language={}", scenario, language);
        System.out.println("收到场景对话生成请求: scenario=" + scenario + ", language=" + language);

        try {
            if (scenario == null || scenario.trim().isEmpty()) {
                logger.error("场景参数为空");
                System.err.println("场景参数为空");
                return ResponseEntity.badRequest().body(Map.of("error", "场景参数不能为空"));
            }

            String dialogContent = mistralDialogService.generateDialog(scenario, language);
            logger.debug("Mistral 对话生成结果: {}", dialogContent);
            System.out.println("Mistral 对话生成结果: " + dialogContent);

            // 解析对话内容为一问一答的格式
            List<Map<String, String>> parsedDialog = parseDialogContent(dialogContent);

            return ResponseEntity.ok(Map.of(
                    "success", true,
                    "dialog", parsedDialog
            ));
        } catch (Exception e) {
            logger.error("生成场景对话失败: scenario={}, language={}", scenario, language, e);
            System.err.println("生成场景对话失败: scenario=" + scenario + ", language=" + language + ", error=" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "生成对话失败: " + e.getMessage()));
        }
    }

    @PostMapping("/analyze")
    public ResponseEntity<Map<String, String>> analyzeDialog(
            @RequestParam String text,
            @RequestParam(required = false, defaultValue = "japanese") String language) {
        logger.info("收到对话分析请求: text={}, language={}", text, language);
        System.out.println("收到对话分析请求: text=" + text + ", language=" + language);

        try {
            if (text == null || text.trim().isEmpty()) {
                logger.error("对话文本参数为空");
                System.err.println("对话文本参数为空");
                return ResponseEntity.badRequest().body(Map.of("error", "对话文本参数不能为空"));
            }

            String analysis = callDashscopeForAnalysis(text, language);
            logger.debug("DashScope 分析结果: {}", analysis);
            System.out.println("DashScope 分析结果: " + analysis);

            String markdown = String.format(
                    "%s",
                    analysis
            );
            logger.info("生成 Markdown 成功");
            System.out.println("生成 Markdown 成功");

            return ResponseEntity.ok(Map.of("markdown", markdown));
        } catch (Exception e) {
            logger.error("分析对话失败: text={}, language={}", text, language, e);
            System.err.println("分析对话失败: text=" + text + ", language=" + language + ", error=" + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "分析失败: " + e.getMessage()));
        }
    }

    private String callDashscopeForAnalysis(String text, String language) throws IOException {
        logger.info("调用 DashScope API 分析对话: text={}, language={}", text, language);
        System.out.println("调用 DashScope API 分析对话: text=" + text + ", language=" + language);

        try {
            if (dashscopeApiKey == null || dashscopeApiKey.isEmpty()) {
                logger.error("DashScope API Key 未配置");
                System.err.println("DashScope API Key 未配置");
                throw new IOException("DashScope API Key 未配置");
            }

            OpenAIClient client = OpenAIOkHttpClient.builder()
                    .apiKey(dashscopeApiKey)
                    .baseUrl(DASHSCOPE_API_URL)
                    .build();

            String prompt;
            if ("english".equals(language)) {
                prompt = createEnglishAnalysisPrompt(text);
            } else {
                prompt = createJapaneseAnalysisPrompt(text);
            }

            ChatCompletionCreateParams params = ChatCompletionCreateParams.builder()
                    .addUserMessage(prompt)
                    .model("deepseek-v3")
                    .build();

            ChatCompletion chatCompletion = client.chat().completions().create(params);
            String result = chatCompletion.choices().get(0).message().content().orElse("无返回内容");
            logger.debug("DashScope 返回: {}", result);
            System.out.println("DashScope 返回: " + result);
            return result;
        } catch (Exception e) {
            logger.error("DashScope API 调用失败: text={}, language={}", text, language, e);
            System.err.println("DashScope API 调用失败: text=" + text + ", language=" + language + ", error=" + e.getMessage());
            throw new IOException("DashScope API 调用失败: " + e.getMessage(), e);
        }
    }

    private String createJapaneseAnalysisPrompt(String text) {
        return String.format(
                "你是一个专业的日语语言分析助手，擅长将日语句子进行细致解析。请严格按以下结构输出分析结果！！！\n" +
                        "严格约束：\n" +
                        "1. 例文字段要提供展示该语法用法的例句，不是原句\n" +
                        "2. 中文翻译字段只输出例文的中文翻译\n" +
                        "3. 注意字段要说明使用场景和与其他语法的区别\n" +
                        "4. 严格按照指定格式输出，不要偏离，不要输出其他无关的符号\n\n" +
                        "目标句子:%s\n\n" +
                        "【语法分析】\n\n" +
                        "对句子中的主要语法点（如接续、变形、敬语、自谦语、可能形、被动形、使役形、使役被动、授受关系等）进行分析解释\n\n"+
                        "示例参考:\n" +
                        "▲〜ている:\n" +
                        "语法分析:动词て形+いる，表示动作的进行状态或结果状态\n" +
                        "例文:今、友達と電話で話しています。\n" +
                        "中文翻译:现在正在和朋友打电话。\n" +
                        "注意:用于表示动作正在进行或状态持续，区别于单纯的动词现在形\n\n", text
        );
    }

    private String createEnglishAnalysisPrompt(String text) {
        return String.format(
                "你是一个专业的英语语言分析助手，擅长将英语句子进行细致解析。请严格按以下结构输出分析结果！！！不要有重复的单词及语法解析！\n" +
                        "重要要求:请使用纯文本格式，不要使用任何markdown标记（如**、*、#等），直接输出文本内容。\n" +
                        "目标句子:%s\n\n" +
                        "【单词分析】\n\n" +
                        "对句子中的重点单词进行解释\n" +
                        "格式为:\n\n" +
                        "▲"+"单词本身 /音标/:【词性（如动词/名词/形容词/副词/介词/连词等）】\n" +
                        "含义:（提供主要含义和常用含义）\n" +
                        "例文:（句子长度不少于15个单词，体现日常对话中真实的场景）\n" +
                        "中文翻译:\n" +
                        "词根词缀:（如果适用，解释词根、前缀、后缀）\n" +
                        "同义词:（提供2-3个同义词）\n\n" +
                        "【语法分析】\n\n" +
                        "对句子中的重点语法点（如时态、语态、从句、虚拟语气等）进行解释\n" +
                        "格式为:\n\n" +
                        "▲"+"语法点名称:\n" +
                        "用法和规则:\n" +
                        "例文:（句子长度不少于15个单词，体现日常对话中真实的场景）\n" +
                        "中文翻译:\n" +
                        "注意:（使用场景/与易混淆的其他语法的区别）\n\n" +
                        "【固定搭配】\n\n" +
                        "对句子中的固定搭配和惯用表达进行解释\n" +
                        "格式为:\n\n" +
                        "▲"+"固定搭配:\n" +
                        "含义:（用中文详细解释这个搭配的含义和用法）\n" +
                        "例文:（例文不少于15个单词，体现日常对话或书面语中的真实使用场景）\n" +
                        "中文翻译:（提供对应例文的中文翻译）\n\n" +
                        "【俚语】\n\n" +
                        "对句子中的俚语和口语表达进行解释\n" +
                        "格式为:\n\n" +
                        "▲"+"俚语表达:\n" +
                        "含义:（用中文详细解释这个俚语的含义和使用场合）\n" +
                        "例文:（例文不少于15个单词，体现日常对话中的真实使用场景）\n" +
                        "中文翻译:（提供对应例文的中文翻译）\n\n", text
        );
    }

    private List<Map<String, String>> parseDialogContent(String content) {
        List<Map<String, String>> result = new ArrayList<>();
        
        // 按轮次分割对话
        String[] rounds = content.split("###");
        
        for (String round : rounds) {
            if (round.trim().isEmpty()) continue;
            
            // 按换行分割A和B的对话
            String[] lines = round.trim().split("\n");
            
            for (String line : lines) {
                if (line.trim().isEmpty()) continue;
                
                // 提取说话者和内容
                String[] parts = line.trim().split(":", 2);
                if (parts.length < 2) continue;
                
                String speaker = parts[0].trim();
                String dialogContent = parts[1].trim();
                
                // 分解内容为日文、假名和中文
                String[] contentParts = dialogContent.split("\\|");
                if (contentParts.length < 3) continue;
                
                String japanese = contentParts[0].trim();
                String kana = contentParts[1].trim();
                String chinese = contentParts[2].trim();
                
                result.add(Map.of(
                    "speaker", speaker,
                    "japanese", japanese,
                    "kana", kana,
                    "chinese", chinese
                ));
            }
        }
        
        return result;
    }

    @PostMapping("/save")
    public ResponseEntity<Map<String, Object>> saveDialog(@RequestBody SaveRequest request) {
        logger.info("收到保存对话请求");
        System.out.println("收到保存对话请求");

        try {
            if (request.getContents() == null || request.getContents().isEmpty()) {
                logger.error("保存内容为空");
                System.err.println("保存内容为空");
                return ResponseEntity.badRequest().body(Map.of("success", false, "message", "保存内容不能为空"));
            }

            // 将内容添加到全局 List 中
            savedDialogs.addAll(request.getContents());
            logger.info("成功保存对话内容，当前收藏数量: {}", savedDialogs.size());
            System.out.println("成功保存对话内容，当前收藏数量: " + savedDialogs.size());

            return ResponseEntity.ok(Map.of("success", true, "message", "内容已保存"));
        } catch (Exception e) {
            logger.error("保存对话内容失败", e);
            System.err.println("保存对话内容失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("success", false, "message", "保存失败: " + e.getMessage()));
        }
    }

    // 获取已保存的对话列表
    @GetMapping("/list")
    public ResponseEntity<List<String>> getSavedDialogs() {
        logger.info("获取已保存对话: 总数={}", savedDialogs.size());
        System.out.println("获取已保存对话: 总数=" + savedDialogs.size());
        return ResponseEntity.ok(savedDialogs);
    }

    // 请求体类
    static class SaveRequest {
        private List<String> contents;

        public List<String> getContents() {
            return contents;
        }

        public void setContents(List<String> contents) {
            this.contents = contents;
        }
    }
} 