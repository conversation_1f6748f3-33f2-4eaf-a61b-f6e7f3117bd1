package org.example.youtubeanalysisdemo.controller;

import org.example.youtubeanalysisdemo.model.User;
import org.example.youtubeanalysisdemo.repository.UserRepository;
import org.example.youtubeanalysisdemo.service.TokenService;
import org.example.youtubeanalysisdemo.service.SubscriptionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextHolderStrategy;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.security.web.context.SecurityContextRepository;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/auth")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);
    private final TokenService tokenService;
    private final UserRepository userRepository;
    private final SubscriptionService subscriptionService;
    private final SecurityContextRepository securityContextRepository = new HttpSessionSecurityContextRepository();

    @Autowired
    public AuthController(TokenService tokenService, UserRepository userRepository, 
                         SubscriptionService subscriptionService) {
        this.tokenService = tokenService;
        this.userRepository = userRepository;
        this.subscriptionService = subscriptionService;
    }

    @GetMapping("/check")
    public ResponseEntity<Map<String, Object>> checkAuthStatus() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Map<String, Object> response = new HashMap<>();
        
        if (authentication == null || authentication instanceof AnonymousAuthenticationToken) {
            response.put("authenticated", false);
            return ResponseEntity.ok(response);
        }
        
        response.put("authenticated", true);
        if (authentication.getPrincipal() instanceof OAuth2User) {
            OAuth2User oauth2User = (OAuth2User) authentication.getPrincipal();
            response.put("user", oauth2User.getAttributes());
        }
        
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/token-login")
    public ResponseEntity<Map<String, Object>> tokenLogin(
            @RequestBody TokenRequest tokenRequest,
            HttpServletRequest request,
            HttpServletResponse response) {
        
        String token = tokenRequest.getToken();
        Map<String, Object> responseMap = new HashMap<>();
        
        try {
            logger.info("接收到token登录请求");
            // 验证Google令牌
            Map<String, Object> userInfo = tokenService.validateGoogleToken(token);
            
            if (userInfo != null) {
                String googleId = (String) userInfo.get("id");
                String email = (String) userInfo.get("email");
                logger.info("Google令牌验证成功，用户: {}, 邮箱: {}", googleId, email);
                
                // 重要:确保userInfo中同时包含"id"和"sub"属性，以便与OAuth2用户一致
                userInfo.put("sub", googleId);
                
                // 在数据库中查找或创建用户
                Optional<User> optionalUser = userRepository.findByGoogleId(googleId);
                User user;
                boolean isNewUser = false;
                
                if (optionalUser.isPresent()) {
                    user = optionalUser.get();
                    logger.info("找到已存在用户: {}", user.getId());
                } else {
                    user = new User();
                    user.setGoogleId(googleId);
                    user.setEmail(email);
                    user.setName((String) userInfo.get("name"));
                    user.setPicture((String) userInfo.get("picture"));
                    user.setProvider(User.AuthProvider.GOOGLE);
                    user = userRepository.save(user);
                    isNewUser = true;
                    logger.info("创建新用户: {}", user.getId());
                    
                    // 为新用户创建7天Premium试用订阅
                    try {
                        subscriptionService.createTrialSubscription(googleId);
                        logger.info("成功为新用户创建试用订阅: {}", googleId);
                    } catch (Exception e) {
                        logger.error("为新用户创建试用订阅失败: {}", googleId, e);
                        // 不影响用户注册流程，继续执行
                    }
                }
                
                // 创建认证令牌
                OAuth2User oauth2User = new DefaultOAuth2User(
                    Collections.singleton(new SimpleGrantedAuthority("ROLE_USER")),
                    userInfo,
                    "email"
                );
                
                OAuth2AuthenticationToken authToken = new OAuth2AuthenticationToken(
                    oauth2User,
                    Collections.singleton(new SimpleGrantedAuthority("ROLE_USER")),
                    "google"
                );
                
                // 为认证令牌设置请求详情
                WebAuthenticationDetails webAuthenticationDetails = new WebAuthenticationDetails(request);
                authToken.setDetails(webAuthenticationDetails);
                
                // 创建新的SecurityContext并存储认证信息
                SecurityContext securityContext = SecurityContextHolder.createEmptyContext();
                securityContext.setAuthentication(authToken);
                
                // 明确地将SecurityContext保存到当前线程和HTTP会话中
                SecurityContextHolder.setContext(securityContext);
                securityContextRepository.saveContext(securityContext, request, response);
                
                // 确保会话已创建并保存
                HttpSession session = request.getSession(true);
                session.setAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY, securityContext);
                
                logger.info("认证成功，设置会话ID: {}", session.getId());
                
                responseMap.put("success", true);
                responseMap.put("message", "登录成功");
                responseMap.put("user", userInfo);
                responseMap.put("sessionId", session.getId());
                responseMap.put("isNewUser", isNewUser);
                return ResponseEntity.ok(responseMap);
            } else {
                logger.warn("令牌验证失败，无法获取用户信息");
                responseMap.put("success", false);
                responseMap.put("message", "无效的令牌");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(responseMap);
            }
        } catch (Exception e) {
            logger.error("认证过程中发生异常", e);
            responseMap.put("success", false);
            responseMap.put("message", "认证失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(responseMap);
        }
    }
    
    @GetMapping("/preauth")
    public ResponseEntity<Map<String, Object>> preAuthenticate(
            HttpServletRequest request,
            @RequestParam(required = false) String sessionId) {
        
        Map<String, Object> responseMap = new HashMap<>();
        
        logger.info("收到预认证请求，sessionId: {}", sessionId);
        
        // 检查当前会话是否已认证
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        boolean isAuthenticated = authentication != null && 
                                  authentication.isAuthenticated() && 
                                  !(authentication instanceof AnonymousAuthenticationToken);
        
        responseMap.put("authenticated", isAuthenticated);
        
        if (isAuthenticated && authentication.getPrincipal() instanceof OAuth2User) {
            OAuth2User oauth2User = (OAuth2User) authentication.getPrincipal();
            responseMap.put("user", oauth2User.getAttributes());
            String googleId = oauth2User.getAttribute("sub");
            logger.info("用户已认证: {}, googleId: {}", oauth2User.getName(), googleId);
            
            // 检查user对象中是否同时包含id和sub属性，如果不包含则添加
            Map<String, Object> attributes = new HashMap<>(oauth2User.getAttributes());
            if (attributes.containsKey("id") && !attributes.containsKey("sub")) {
                attributes.put("sub", attributes.get("id"));
                logger.info("为用户添加了缺失的sub属性: {}", attributes.get("id"));
            } else if (attributes.containsKey("sub") && !attributes.containsKey("id")) {
                attributes.put("id", attributes.get("sub"));
                logger.info("为用户添加了缺失的id属性: {}", attributes.get("sub"));
            }
            responseMap.put("userAttributes", attributes);
        } else {
            logger.info("用户未认证或无效认证");
        }
        
        // 返回当前会话ID供参考
        responseMap.put("currentSessionId", request.getSession(false) != null ? 
                                       request.getSession().getId() : null);
        
        return ResponseEntity.ok(responseMap);
    }
    
    public static class TokenRequest {
        private String token;
        
        public String getToken() {
            return token;
        }
        
        public void setToken(String token) {
            this.token = token;
        }
    }
} 