package org.example.youtubeanalysisdemo.controller;

import org.example.youtubeanalysisdemo.model.PlayHistory;
import org.example.youtubeanalysisdemo.model.User;
import org.example.youtubeanalysisdemo.repository.PlayHistoryRepository;
import org.example.youtubeanalysisdemo.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;

@RestController
@RequestMapping("/api/history")
public class PlayHistoryController {

    @Autowired
    private PlayHistoryRepository playHistoryRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    /**
     * 保存播放历史
     */
    @PostMapping("/save")
    @Transactional
    public ResponseEntity<?> savePlayHistory(
            @AuthenticationPrincipal OAuth2User principal,
            @RequestBody Map<String, String> request) {
        
        System.out.println("收到保存播放历史请求: " + request);
        
        if (principal == null) {
            System.out.println("用户未登录，无法保存播放历史");
            return ResponseEntity.status(401).body(Map.of("error", "未登录"));
        }
        
        String googleId = principal.getAttribute("sub");
        System.out.println("用户Google ID: " + googleId);
        
        Optional<User> userOpt = userRepository.findByGoogleId(googleId);
        
        if (userOpt.isEmpty()) {
            System.out.println("用户不存在: " + googleId);
            return ResponseEntity.status(404).body(Map.of("error", "用户不存在"));
        }
        
        User user = userOpt.get();
        System.out.println("找到用户: " + user.getName() + " (ID: " + user.getId() + ")");
        
        String videoId = request.get("videoId");
        String videoTitle = request.get("videoTitle");
        
        System.out.println("视频信息 - ID: " + videoId + ", 标题: " + videoTitle);
        
        if (videoId == null || videoTitle == null) {
            System.out.println("视频信息不完整");
            return ResponseEntity.badRequest().body(Map.of("error", "视频信息不完整"));
        }
        
        // 检查该视频是否已存在于历史记录中，如果存在则先删除
        Optional<PlayHistory> existingHistory = playHistoryRepository.findByUserAndVideoId(user, videoId);
        if (existingHistory.isPresent()) {
            System.out.println("删除现有历史记录: " + existingHistory.get().getId());
            playHistoryRepository.delete(existingHistory.get());
        }
        
        // 保存新的播放历史
        PlayHistory playHistory = PlayHistory.builder()
                .user(user)
                .videoId(videoId)
                .videoTitle(videoTitle)
                .thumbnailUrl("https://img.youtube.com/vi/" + videoId + "/mqdefault.jpg")
                .playedAt(LocalDateTime.now())
                .build();
        
        PlayHistory savedHistory = playHistoryRepository.save(playHistory);
        System.out.println("新历史记录已保存: " + savedHistory.getId());
        
        // 确保用户最多只有200条历史记录
        List<PlayHistory> histories = playHistoryRepository.findByUserOrderByPlayedAtDesc(user, PageRequest.of(0, 201));
        if (histories.size() > 200) {
            PlayHistory oldestHistory = histories.get(histories.size() - 1);
            System.out.println("删除最旧的历史记录，保持数量不超过200: " + oldestHistory.getId());
            playHistoryRepository.delete(oldestHistory);
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("historyId", savedHistory.getId());
        response.put("videoId", savedHistory.getVideoId());
        response.put("videoTitle", savedHistory.getVideoTitle());
        response.put("timestamp", savedHistory.getPlayedAt().toString());
        
        System.out.println("播放历史保存成功: " + response);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取用户的播放历史
     */
    @GetMapping("/list")
    public ResponseEntity<?> getPlayHistory(@AuthenticationPrincipal OAuth2User principal) {
        if (principal == null) {
            return ResponseEntity.status(401).body(Map.of("error", "未登录"));
        }
        
        String googleId = principal.getAttribute("sub");
        Optional<User> userOpt = userRepository.findByGoogleId(googleId);
        
        if (userOpt.isEmpty()) {
            return ResponseEntity.status(404).body(Map.of("error", "用户不存在"));
        }
        
        User user = userOpt.get();
        
        // 获取最近200条播放历史
        List<PlayHistory> histories = playHistoryRepository.findByUserOrderByPlayedAtDesc(user, PageRequest.of(0, 200));
        
        List<Map<String, Object>> result = new ArrayList<>();
        for (PlayHistory history : histories) {
            Map<String, Object> item = new HashMap<>();
            item.put("id", history.getId());
            item.put("videoId", history.getVideoId());
            item.put("title", history.getVideoTitle());
            item.put("thumbnailUrl", history.getThumbnailUrl());
            item.put("date", history.getPlayedAt().toString());
            result.add(item);
        }
        
        return ResponseEntity.ok(result);
    }
} 