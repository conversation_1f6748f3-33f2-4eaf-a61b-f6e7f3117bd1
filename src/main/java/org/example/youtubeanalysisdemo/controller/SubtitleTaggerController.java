package org.example.youtubeanalysisdemo.controller;

import com.atilika.kuromoji.ipadic.Token;
import com.atilika.kuromoji.ipadic.Tokenizer;
import org.example.youtubeanalysisdemo.model.TaggedWord;
import org.example.youtubeanalysisdemo.service.EnglishPosTaggingService;
import org.example.youtubeanalysisdemo.strategy.LanguageStrategy;
import org.example.youtubeanalysisdemo.strategy.LanguageStrategyFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@RestController
@RequestMapping("/api/subtitle")
public class SubtitleTaggerController {

    private final Tokenizer tokenizer = new Tokenizer();
    private final Pattern kanjiPattern = Pattern.compile("[\u4E00-\u9FFF]");
    
    @Autowired
    private LanguageStrategyFactory languageStrategyFactory;
    
    @Autowired
    private EnglishPosTaggingService englishPosTaggingService;

    @PostMapping("/tag")
    public ResponseEntity<Map<String, List<TaggedWord>>> tagSubtitle(
            @RequestParam String text,
            @RequestParam(required = false, defaultValue = "japanese") String language) {
        
        if (text == null || text.trim().isEmpty()) {
            return ResponseEntity.badRequest().body(Map.of("error", List.of()));
        }

        // 获取语言策略
        LanguageStrategy strategy = languageStrategyFactory.getStrategy(language);
        
        // 检查是否需要词性标注
        if (!strategy.needsPartOfSpeechTagging()) {
            // 对于不需要词性标注的语言，返回简单的分词结果
            List<TaggedWord> taggedWords = new ArrayList<>();
            String[] words = text.split("\\s+");
            for (String word : words) {
                if (!word.trim().isEmpty()) {
                    taggedWords.add(new TaggedWord(word.trim(), "word", null));
                }
            }
            return ResponseEntity.ok(Map.of("tagged_text", taggedWords));
        }

        // 根据语言类型选择不同的词性标注方法
        List<TaggedWord> taggedWords;
        if ("english".equals(language)) {
            // 使用英语词性标注服务
            taggedWords = englishPosTaggingService.tagText(text);
        } else {
            // 对于日语，使用Kuromoji分析
            List<Token> tokens = tokenizer.tokenize(text);
            taggedWords = new ArrayList<>();

            for (Token token : tokens) {
                String pos = simplifyPos(token.getPartOfSpeechLevel1());
                String reading = getReading(token);
                taggedWords.add(new TaggedWord(token.getSurface(), pos, reading));
            }
        }

        return ResponseEntity.ok(Map.of("tagged_text", taggedWords));
    }

    // 简化词性标签
    private String simplifyPos(String posLevel1) {
        return switch (posLevel1) {
            case "名詞" -> "noun";
            case "動詞" -> "verb";
            case "形容詞" -> "adjective";
            case "副詞" -> "adverb";
            case "助詞" -> "particle";
            case "代名詞" -> "pron";
            case "助動詞" -> "aux";
            case "接続詞" -> "conj";
            case "記号" -> "punctuation";
            default -> "other";
        };
    }

    // 获取读音，仅对包含汉字的token返回读音
    private String getReading(Token token) {
        String surface = token.getSurface();
        if (kanjiPattern.matcher(surface).find()) {
            String reading = token.getReading();
            if (reading != null && !reading.equals(surface)) {
                return toHiragana(reading); // 转换为平假名
            }
        }
        return null;
    }

    // 将片假名转换为平假名
    private String toHiragana(String katakana) {
        if (katakana == null) return null;
        StringBuilder hiragana = new StringBuilder();
        for (char c : katakana.toCharArray()) {
            // 片假名范围:U+30A0 到 U+30FF
            if (c >= '\u30A0' && c <= '\u30FF') {
                hiragana.append((char) (c - 0x60)); // 片假名到平假名偏移
            } else {
                hiragana.append(c); // 非片假名字符保持不变
            }
        }
        return hiragana.toString();
    }
}