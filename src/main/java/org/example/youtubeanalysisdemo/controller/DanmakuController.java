package org.example.youtubeanalysisdemo.controller;

import org.example.youtubeanalysisdemo.entity.DanmakuComment;
import org.example.youtubeanalysisdemo.model.User;
import org.example.youtubeanalysisdemo.repository.UserRepository;
import org.example.youtubeanalysisdemo.service.DanmakuService;
import org.example.youtubeanalysisdemo.util.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.oauth2.core.user.OAuth2User;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 弹幕控制器
 * 提供弹幕相关的REST API接口
 */
@RestController
@RequestMapping("/api/danmaku")
public class DanmakuController {
    
    private static final Logger logger = LoggerFactory.getLogger(DanmakuController.class);
    
    @Autowired
    private DanmakuService danmakuService;
    
    @Autowired
    private UserRepository userRepository;
    
    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId(Authentication authentication) {
        if (authentication != null && authentication.getPrincipal() instanceof OAuth2User) {
            OAuth2User oauth2User = (OAuth2User) authentication.getPrincipal();
            String googleId = oauth2User.getAttribute("sub");
            if (googleId == null) {
                googleId = oauth2User.getAttribute("id");
            }
            
            if (googleId != null) {
                // 从数据库中查找用户
                Optional<User> userOpt = userRepository.findByGoogleId(googleId);
                if (userOpt.isPresent()) {
                    return userOpt.get().getId();
                }
            }
        }
        return null;
    }
    
    /**
     * 发送弹幕
     */
    @PostMapping("/send")
    public ResponseEntity<Map<String, Object>> sendDanmaku(
            @RequestBody DanmakuSendRequest request,
            HttpServletRequest httpRequest) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 验证用户认证状态
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated() || 
                authentication instanceof AnonymousAuthenticationToken) {
                response.put("success", false);
                response.put("error", "用户未认证，请先登录");
                return ResponseEntity.status(401).body(response);
            }
            
            // 获取当前用户ID
            Long currentUserId = getCurrentUserId(authentication);
            if (currentUserId == null) {
                response.put("success", false);
                response.put("error", "无法获取用户信息");
                return ResponseEntity.status(401).body(response);
            }
            
            // 参数验证
            if (request.getVideoId() == null || request.getVideoId().trim().isEmpty()) {
                response.put("success", false);
                response.put("error", "视频ID不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (request.getContent() == null || request.getContent().trim().isEmpty()) {
                response.put("success", false);
                response.put("error", "弹幕内容不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (request.getVideoTimestamp() == null || request.getVideoTimestamp() < 0) {
                response.put("success", false);
                response.put("error", "视频时间戳无效");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 发送弹幕
            DanmakuService.DanmakuSendResult result = danmakuService.sendDanmaku(
                request.getVideoId().trim(),
                request.getContent().trim(),
                request.getVideoTimestamp(),
                currentUserId,
                request.getColor(),
                request.getType()
            );
            
            if (result.isSuccess()) {
                response.put("success", true);
                response.put("message", result.getMessage());
                response.put("danmaku", convertToResponseDto(result.getDanmaku()));
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("error", result.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            logger.error("发送弹幕失败", e);
            response.put("success", false);
            response.put("error", "服务器内部错误");
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 获取视频弹幕列表
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getDanmakuList(
            @RequestParam String videoId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "100") int size) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 参数验证
            if (videoId == null || videoId.trim().isEmpty()) {
                response.put("success", false);
                response.put("error", "视频ID不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 限制每页大小
            if (size > 500) {
                size = 500;
            }
            
            // 获取弹幕列表
            Page<DanmakuComment> danmakuPage = danmakuService.getDanmakuList(
                videoId.trim(), page, size);
            
            response.put("success", true);
            response.put("danmaku", danmakuPage.getContent().stream()
                .map(this::convertToResponseDto)
                .toArray());
            response.put("totalElements", danmakuPage.getTotalElements());
            response.put("totalPages", danmakuPage.getTotalPages());
            response.put("currentPage", page);
            response.put("pageSize", size);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取弹幕列表失败", e);
            response.put("success", false);
            response.put("error", "服务器内部错误");
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 获取指定时间范围的弹幕
     */
    @GetMapping("/range")
    public ResponseEntity<Map<String, Object>> getDanmakuByTimeRange(
            @RequestParam String videoId,
            @RequestParam Double startTime,
            @RequestParam Double endTime) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 参数验证
            if (videoId == null || videoId.trim().isEmpty()) {
                response.put("success", false);
                response.put("error", "视频ID不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (startTime == null || endTime == null || startTime < 0 || endTime < startTime) {
                response.put("success", false);
                response.put("error", "时间范围参数无效");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 限制时间范围最大为30分钟
            if (endTime - startTime > 1800) {
                response.put("success", false);
                response.put("error", "时间范围不能超过30分钟");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 获取弹幕
            List<DanmakuComment> danmakuList = danmakuService.getDanmakuByTimeRange(
                videoId.trim(), startTime, endTime);
            
            response.put("success", true);
            response.put("danmaku", danmakuList.stream()
                .map(this::convertToResponseDto)
                .toArray());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取时间范围弹幕失败", e);
            response.put("success", false);
            response.put("error", "服务器内部错误");
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 获取视频弹幕统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getDanmakuStats(@RequestParam String videoId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (videoId == null || videoId.trim().isEmpty()) {
                response.put("success", false);
                response.put("error", "视频ID不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            long totalCount = danmakuService.getDanmakuCount(videoId.trim());
            
            response.put("success", true);
            response.put("totalCount", totalCount);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取弹幕统计失败", e);
            response.put("success", false);
            response.put("error", "服务器内部错误");
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 删除弹幕
     */
    @DeleteMapping("/{danmakuId}")
    public ResponseEntity<Map<String, Object>> deleteDanmaku(
            @PathVariable Long danmakuId,
            HttpServletRequest httpRequest) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 验证用户认证状态
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated() || 
                authentication instanceof AnonymousAuthenticationToken) {
                response.put("success", false);
                response.put("error", "用户未认证，请先登录");
                return ResponseEntity.status(401).body(response);
            }
            
            // 获取当前用户ID
            Long currentUserId = getCurrentUserId(authentication);
            if (currentUserId == null) {
                response.put("success", false);
                response.put("error", "无法获取用户信息");
                return ResponseEntity.status(401).body(response);
            }
            
            // 删除弹幕
            boolean success = danmakuService.deleteDanmaku(danmakuId, currentUserId);
            
            if (success) {
                response.put("success", true);
                response.put("message", "弹幕删除成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("error", "删除失败，弹幕不存在或无权限删除");
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            logger.error("删除弹幕失败", e);
            response.put("success", false);
            response.put("error", "服务器内部错误");
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 转换为响应DTO（不包含敏感信息）
     */
    private Map<String, Object> convertToResponseDto(DanmakuComment danmaku) {
        Map<String, Object> dto = new HashMap<>();
        dto.put("id", danmaku.getId());
        dto.put("content", danmaku.getContent());
        dto.put("videoTimestamp", danmaku.getVideoTimestamp());
        dto.put("color", danmaku.getColor());
        dto.put("type", danmaku.getType());
        dto.put("createTime", danmaku.getCreateTime().toString());
        // 不返回userId，保护用户隐私
        return dto;
    }
    
    /**
     * 弹幕发送请求DTO
     */
    public static class DanmakuSendRequest {
        private String videoId;
        private String content;
        private Double videoTimestamp;
        private String color;
        private Integer type;
        
        // Getters and Setters
        public String getVideoId() { return videoId; }
        public void setVideoId(String videoId) { this.videoId = videoId; }
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        
        public Double getVideoTimestamp() { return videoTimestamp; }
        public void setVideoTimestamp(Double videoTimestamp) { this.videoTimestamp = videoTimestamp; }
        
        public String getColor() { return color; }
        public void setColor(String color) { this.color = color; }
        
        public Integer getType() { return type; }
        public void setType(Integer type) { this.type = type; }
    }
} 