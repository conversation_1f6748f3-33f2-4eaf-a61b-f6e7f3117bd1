package org.example.youtubeanalysisdemo.controller;

import org.springframework.web.bind.annotation.*;
import org.springframework.http.*;
import org.springframework.beans.factory.annotation.Value;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/api/translate")
public class TranslationController {

    private static final Logger logger = LoggerFactory.getLogger(TranslationController.class);

    @Value("${google.translate.api.key}")
    private String googleApiKey;
    
    private final String googleEndpoint = "https://translation.googleapis.com/language/translate/v2";
    
    @PostMapping("/word")
    public ResponseEntity<?> translateWord(@RequestParam String text, 
                                         @RequestParam(defaultValue = "japanese") String language) throws IOException, InterruptedException {
        return translateWithGoogle(text, language);
    }
    
    /**
     * 使用Google翻译API进行翻译
     */
    private ResponseEntity<?> translateWithGoogle(String text, String language) throws IOException, InterruptedException {
        logger.info("尝试使用Google翻译进行翻译，原文: {}, 语言: {}", text, language);
        // 根据语言设置正确的源语言参数
        String fromLanguage;
        switch (language.toLowerCase()) {
            case "english":
                fromLanguage = "en";
                break;
            case "japanese":
            default:
                fromLanguage = "ja";
                break;
        }
        
        // 构建请求参数
        String requestBody = String.format(
            "key=%s&q=%s&source=%s&target=%s&format=text",
            URLEncoder.encode(googleApiKey, StandardCharsets.UTF_8),
            URLEncoder.encode(text, StandardCharsets.UTF_8),
            URLEncoder.encode(fromLanguage, StandardCharsets.UTF_8),
            URLEncoder.encode("zh", StandardCharsets.UTF_8)
        );
        
        // 创建HTTP客户端
        HttpClient client = HttpClient.newHttpClient();
        
        // 构建请求
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(googleEndpoint))
                .header("Content-Type", "application/x-www-form-urlencoded")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .build();
                
        // 发送请求并获取响应
        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        
        // 检查响应状态
        if (response.statusCode() == 200) {
            try {
                // 解析Google响应
                JSONObject jsonResponse = new JSONObject(response.body());
                JSONObject data = jsonResponse.getJSONObject("data");
                JSONArray translations = data.getJSONArray("translations");
                String translation = translations.getJSONObject(0).getString("translatedText");
                
                // 返回简化的响应
                Map<String, Object> result = new HashMap<>();
                result.put("original", text);
                result.put("translation", translation);
                result.put("language", language);
                result.put("fromLanguage", fromLanguage);
                result.put("success", true);
                result.put("provider", "google");
                return ResponseEntity.ok(result);
            } catch (Exception e) {
                Map<String, Object> error = new HashMap<>();
                error.put("error", "解析Google翻译结果失败");
                error.put("message", e.getMessage());
                error.put("original", text);
                error.put("language", language);
                error.put("success", false);
                error.put("provider", "google");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
            }
        } else {
            // Google翻译API调用失败
            throw new RuntimeException("Google翻译API调用失败，状态码: " + response.statusCode());
        }
    }
} 