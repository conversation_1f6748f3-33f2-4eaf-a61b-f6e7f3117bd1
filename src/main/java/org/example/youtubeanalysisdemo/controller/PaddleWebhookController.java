package org.example.youtubeanalysisdemo.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.youtubeanalysisdemo.entity.Subscription;
import org.example.youtubeanalysisdemo.model.User;
import org.example.youtubeanalysisdemo.repository.SubscriptionRepository;
import org.example.youtubeanalysisdemo.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/paddle/webhook")
public class PaddleWebhookController {

    private static final Logger logger = LoggerFactory.getLogger(PaddleWebhookController.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ISO_DATE_TIME;

    @Value("${paddle.webhook.secret:}")
    private String webhookSecret;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private SubscriptionRepository subscriptionRepository;

    @SuppressWarnings("unchecked")
    @PostMapping
    @Transactional
    public ResponseEntity<String> handleWebhook(
            @RequestBody String bodyRaw,
            @RequestHeader(value = "Paddle-Signature", required = false) String paddleSignature) {
        try {
            logger.info("接收到Paddle Webhook，进行签名验证");

            // 1. 验证Paddle-Signature头
            if (paddleSignature == null || paddleSignature.isEmpty()) {
                logger.error("请求头中缺少Paddle-Signature");
                return ResponseEntity.badRequest().body("无效请求:缺少签名");
            }

            if (webhookSecret == null || webhookSecret.isEmpty()) {
                logger.error("Webhook密钥未配置");
                return ResponseEntity.status(500).body("服务器配置错误:缺少密钥");
            }

            // 2. 提取时间戳和签名
            String[] signatureParts = paddleSignature.split(";");
            if (signatureParts.length != 2) {
                logger.error("Paddle-Signature格式无效");
                return ResponseEntity.badRequest().body("无效请求:签名格式错误");
            }

            String timestamp = null;
            String signature = null;

            for (String part : signatureParts) {
                String[] keyValue = part.split("=");
                if (keyValue.length != 2) {
                    logger.error("Paddle-Signature部分格式无效");
                    return ResponseEntity.badRequest().body("无效请求:签名部分格式错误");
                }

                if ("ts".equals(keyValue[0])) {
                    timestamp = keyValue[1];
                } else if ("h1".equals(keyValue[0])) {
                    signature = keyValue[1];
                }
            }

            if (timestamp == null || signature == null) {
                logger.error("无法从Paddle-Signature头中提取时间戳或签名");
                return ResponseEntity.badRequest().body("无效请求:签名不完整");
            }

            // 4. 构建签名负载
            String signedPayload = timestamp + ":" + bodyRaw;

            // 5. 使用HMAC SHA256和密钥对签名负载进行哈希
            String computedHash = computeHMACSHA256(signedPayload, webhookSecret);

            // 6. 比较签名
            if (!timingSafeEquals(computedHash.getBytes(StandardCharsets.UTF_8), signature.getBytes(StandardCharsets.UTF_8))) {
                logger.error("计算的签名与Paddle签名不匹配");
                return ResponseEntity.status(401).body("无效签名");
            }

            logger.info("Paddle Webhook签名验证成功，处理业务逻辑");

            // 将原始请求体解析为Map
            Map<String, Object> payload = objectMapper.readValue(bodyRaw, Map.class);

            // 继续原有的业务逻辑处理
            String eventType = (String) payload.get("event_type");
            if (eventType == null) {
                logger.error("无效的Paddle webhook数据: 缺少event_type字段");
                return ResponseEntity.badRequest().body("缺少event_type字段");
            }

            logger.info("处理Paddle事件: {}", eventType);

            if ("transaction.paid".equals(eventType)) {
                handleTransactionPaid(payload);
                return ResponseEntity.ok("交易支付事件处理成功");
            } else {
                logger.info("忽略非交易支付事件: {}", eventType);
                return ResponseEntity.ok("事件已接收但不处理");
            }
        } catch (Exception e) {
            logger.error("处理Paddle webhook时出错", e);
            return ResponseEntity.internalServerError().body("处理webhook时出错: " + e.getMessage());
        }
    }

    // 计算HMAC SHA256哈希
    private String computeHMACSHA256(String data, String key) throws Exception {
        try {
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256_HMAC.init(secretKey);
            byte[] hash = sha256_HMAC.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hash);
        } catch (Exception e) {
            throw new Exception("HMAC SHA256算法不可用", e);
        }
    }

    // 将字节转换为十六进制字符串
    private String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    // 执行两个字节数组的时间安全比较
    private boolean timingSafeEquals(byte[] a, byte[] b) {
        if (a.length != b.length) {
            return false;
        }
        int result = 0;
        for (int i = 0; i < a.length; i++) {
            result |= a[i] ^ b[i];
        }
        return result == 0;
    }

    @SuppressWarnings("unchecked")
    private void handleTransactionPaid(Map<String, Object> payload) {
        Map<String, Object> data = (Map<String, Object>) payload.get("data");
        if (data == null) {
            logger.error("交易数据(data)为空，无法处理。");
            return;
        }

        String transactionId = (String) data.get("id");
        logger.info("处理交易支付: transaction_id={}", transactionId);

        // 尝试从 data.custom_data 或 data.checkout.custom_data 获取用户信息
        Map<String, Object> customData = (Map<String, Object>) data.get("custom_data");
        if (customData == null) {
            Map<String, Object> checkout = (Map<String, Object>) data.get("checkout");
            if (checkout != null) {
                customData = (Map<String, Object>) checkout.get("custom_data");
            }
        }

        if (customData == null) {
            logger.error("未在 data.custom_data 或 data.checkout.custom_data 中找到用户信息: transaction_id={}", transactionId);
            return;
        }

        String googleId = (String) customData.get("your_user_id");
        String email = (String) customData.get("email");

        if (email == null && googleId == null) {
            logger.error("customData中缺少email和googleId(your_user_id): transaction_id={}", transactionId);
            return;
        }

        Optional<User> userOpt = Optional.empty();
        if (googleId != null) {
            userOpt = userRepository.findByGoogleId(googleId);
        }
        if (userOpt.isEmpty() && email != null) {
            userOpt = userRepository.findByEmail(email);
        }

        if (userOpt.isEmpty()) {
            logger.error("未找到用户: email={}, googleId={}", email, googleId);
            return;
        }
        User user = userOpt.get();

        List<Map<String, Object>> items = (List<Map<String, Object>>) data.get("items");
        if (items == null || items.isEmpty()) {
            logger.error("交易数据中缺少items: transaction_id={}", transactionId);
            return;
        }

        // 通常我们关心第一个item作为主要订阅项
        Map<String, Object> firstItem = items.get(0);
        Map<String, Object> priceInfo = (Map<String, Object>) firstItem.get("price");
        if (priceInfo == null) {
            logger.error("第一个item中缺少price信息: transaction_id={}", transactionId);
            return;
        }

        String planName = (String) priceInfo.get("name"); // 例如:"Plus会员"
        String description = (String) priceInfo.get("description"); // 例如:"Monthly subscription"
        String planId = (String) priceInfo.get("id"); // price.id

        // 新增:从item中获取quantity
        Integer quantity = 1; // 默认数量为1
        if (firstItem.get("quantity") instanceof Integer) {
            quantity = (Integer) firstItem.get("quantity");
        }
        if (quantity == null || quantity < 1) {
            quantity = 1; // 确保quantity至少为1
        }

        // 确定订阅的单个周期单位所代表的月数
        int monthsInBasePeriod = 1; // 默认一个周期单位是一个月
        if (description != null) {
            if (description.toLowerCase().contains("monthly")) {
                monthsInBasePeriod = 1;
            } else if (description.toLowerCase().contains("yearly")) {
                monthsInBasePeriod = 12;
            } else {
                logger.warn("无法从描述确定订阅周期: {}, 默认使用月度订阅的基准周期（1个月）", description);
            }
        }

        // 计算总共需要增加的月数 = 基础周期月数 * 数量
        int totalMonthsToAdd = monthsInBasePeriod * quantity;

        // 处理订阅事件时间
        String billedAtStr = (String) data.get("billed_at");
        LocalDateTime eventTime = billedAtStr != null ? 
                                  LocalDateTime.parse(billedAtStr, DATE_FORMATTER) : 
                                  LocalDateTime.now();

        // 查找用户当前所有活跃或待处理的订阅中结束时间最晚的一个
        List<String> statusesToConsider = Arrays.asList("active", "pending");
        Optional<Subscription> latestExistingSubscriptionOpt =
                subscriptionRepository.findFirstByUserIdAndStatusInOrderByNextBillDateDesc(googleId, statusesToConsider);

        LocalDateTime startDate;

        if (latestExistingSubscriptionOpt.isPresent()) {
            Subscription latestSubscription = latestExistingSubscriptionOpt.get();
            startDate = latestSubscription.getNextBillDate();
            // 安全校验:如果获取到的结束日期为空或者早于当前事件时间，则从当前事件时间开始
            if (startDate == null || startDate.isBefore(eventTime)) {
                startDate = eventTime;
            }
        } else {
            // 没有已存在的有效订阅，新订阅从当前事件时间开始
            startDate = eventTime;
        }

        // 计算新订阅的结束时间
        LocalDateTime endDate = startDate.plusMonths(totalMonthsToAdd);

        // 确定订阅状态
        String status;
        if (startDate.isAfter(eventTime)) {
            // 开始日期在将来，设为pending
            status = "pending";
        } else {
            // 从现在开始，设为active
            status = "active";
        }

        // 创建新订阅
        Subscription newSubscription = Subscription.builder()
                .userId(googleId)
                .paddleSubscriptionId(transactionId)
                .status(status)
                .planId(planId)
                .planName(planName)
                .description(description)
                .startDate(startDate)
                .nextBillDate(endDate)
                .createdAt(eventTime)
                .updatedAt(eventTime)
                .build();

        subscriptionRepository.save(newSubscription);
        
        logger.info("已创建新的订阅记录: id={}, userId={}, plan={}, status={}, 开始时间={}, 结束时间={}", 
                    newSubscription.getId(), googleId, planName, status, startDate, endDate);

        // 检查是否需要将旧的pending订阅改为active
        if ("active".equals(status)) {
            updatePendingSubscriptions(googleId, startDate, endDate);
        }
    }
    
    /**
     * 更新待处理的订阅状态
     * 将所有开始日期已过的pending状态订阅设置为active
     * 并更新可能受到影响的其他pending订阅的开始日期
     */
    private void updatePendingSubscriptions(String userId, LocalDateTime startDate, LocalDateTime endDate) {
        List<Subscription> pendingSubscriptions = 
                subscriptionRepository.findByUserIdAndStatusOrderByStartDateAsc(userId, "pending");
        
        if (pendingSubscriptions.isEmpty()) {
            return;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        for (Subscription pending : pendingSubscriptions) {
            if (pending.getStartDate() != null && pending.getStartDate().isBefore(now)) {
                // 将应该开始的pending订阅设置为active
                pending.setStatus("active");
                pending.setUpdatedAt(now);
                subscriptionRepository.save(pending);
                logger.info("已激活待处理订阅: id={}, userId={}, plan={}", 
                            pending.getId(), userId, pending.getPlanName());
            }
        }
    }
} 