package org.example.youtubeanalysisdemo.controller;

import org.example.youtubeanalysisdemo.entity.UserSavedContent;
import org.example.youtubeanalysisdemo.model.PlayHistory;
import org.example.youtubeanalysisdemo.model.User;
import org.example.youtubeanalysisdemo.model.UserLoginLog;
import org.example.youtubeanalysisdemo.repository.PlayHistoryRepository;
import org.example.youtubeanalysisdemo.repository.UserLoginLogRepository;
import org.example.youtubeanalysisdemo.repository.UserRepository;
import org.example.youtubeanalysisdemo.repository.UserSavedContentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/stats")
public class UserStatsController {

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private UserSavedContentRepository userSavedContentRepository;
    
    @Autowired
    private PlayHistoryRepository playHistoryRepository;
    
    @Autowired
    private UserLoginLogRepository userLoginLogRepository;
    
    @GetMapping("/learning")
    public ResponseEntity<?> getUserLearningStats(@AuthenticationPrincipal OAuth2User principal) {
        if (principal == null) {
            return ResponseEntity.status(401).body(Map.of("error", "未登录"));
        }
        
        String googleId = principal.getAttribute("sub");
        Optional<User> userOpt = userRepository.findByGoogleId(googleId);
        
        if (userOpt.isEmpty()) {
            // 这理论上不应该发生，因为用户已通过OAuth2认证，应该在USERS表中有记录
            // 但为保险起见，如果User实体确实未找到，可以尝试直接用principal中的email
            String userEmailForQuery = principal.getAttribute("email");
            if (userEmailForQuery == null) {
                 return ResponseEntity.status(404).body(Map.of("error", "用户邮箱信息缺失"));
            }
            // 注意:这种情况下，后面依赖User实体的查询（如登录日志）可能会失败或不准确
            // 这是为了优先保证USER_SAVED_CONTENTS的查询能进行
            Map<String, Object> stats = new HashMap<>();
            int totalSavedItems = userSavedContentRepository.findByUserIdOrderByCreatedAtDesc(userEmailForQuery).size();
            stats.put("totalSaved", totalSavedItems);
            int totalLearned = userSavedContentRepository.findByUserIdAndLearnedOrderByCreatedAtDesc(userEmailForQuery, 1).size();
            stats.put("totalLearned", totalLearned);
            stats.put("monthlyWatchedVideos", 0); // 无法获取User实体，暂时设为0
            stats.put("weeklyActiveDays", Collections.emptyList());
            stats.put("weeklyLoginDaysCount", 0);
            return ResponseEntity.ok(stats);
        }
        
        User user = userOpt.get();
        Map<String, Object> stats = new HashMap<>();
        String userEmail = user.getEmail(); // 获取用户的邮箱
        if (userEmail == null) {
            // 如果从User实体中获取的email为空，尝试从principal中获取，虽然通常它们是一致的
            userEmail = principal.getAttribute("email");
            if (userEmail == null) {
                 return ResponseEntity.status(404).body(Map.of("error", "用户邮箱信息缺失(User实体和Principal均无)"));
            }
        }

        // 1. 收藏总数 - 从USER_SAVED_CONTENTS表获取
        // 使用用户的Email作为userId进行查询
        int totalSavedItems = userSavedContentRepository.findByUserIdOrderByCreatedAtDesc(userEmail).size();
        stats.put("totalSaved", totalSavedItems);
        
        // 2. 已学会的单词总数 - 从USER_SAVED_CONTENTS表获取
        // 使用用户的Email作为userId进行查询
        int totalLearned = userSavedContentRepository.findByUserIdAndLearnedOrderByCreatedAtDesc(userEmail, 1).size();
        stats.put("totalLearned", totalLearned);
        
        // 3. 本月观看视频总数 (这部分依赖User实体，不受数据表变更影响)
        LocalDateTime startOfMonth = LocalDate.now().withDayOfMonth(1).atStartOfDay();
        LocalDateTime endOfMonth = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59, 59);
        
        List<PlayHistory> monthlyHistories = playHistoryRepository.findByUserAndPlayedAtBetween(user, startOfMonth, endOfMonth);
        Set<String> uniqueVideos = new HashSet<>();
        for (PlayHistory history : monthlyHistories) {
            uniqueVideos.add(history.getVideoId());
        }
        stats.put("monthlyWatchedVideos", uniqueVideos.size());
        
        // 4. 本周登录天数 (这部分依赖User实体，不受数据表变更影响)
        LocalDate today = LocalDate.now();
        LocalDate startOfWeek = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate endOfWeek = today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        
        List<UserLoginLog> weeklyLogins = userLoginLogRepository.findByUserAndLoginDateBetween(user, startOfWeek, endOfWeek);
        List<Integer> weeklyActiveDays = weeklyLogins.stream()
                .map(log -> log.getLoginDate().getDayOfWeek().getValue()) // 1 (Mon) to 7 (Sun)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        stats.put("weeklyActiveDays", weeklyActiveDays);
        stats.put("weeklyLoginDaysCount", weeklyActiveDays.size()); // 用于显示总天数
        
        // 返回统计结果
        return ResponseEntity.ok(stats);
    }
} 