package org.example.youtubeanalysisdemo.controller;

import org.example.youtubeanalysisdemo.dto.TTSRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/tts")
public class TTSController {
    
    private static final Logger logger = LoggerFactory.getLogger(TTSController.class);
    private static final String TEMP_DIR = System.getProperty("java.io.tmpdir");
    
    @PostMapping("/speak")
    public ResponseEntity<byte[]> textToSpeech(@RequestBody TTSRequest request) {
        if (request.getText() == null || request.getText().trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        
        String voice = request.getVoice();
        if (voice == null || voice.trim().isEmpty()) {
            voice = "zh-CN-XiaoxiaoNeural"; // 默认语音
        }
        
        // 处理语速参数
        double rate = 1.0;
        if (request.getRate() != null && request.getRate() > 0) {
            rate = request.getRate();
        }
        
        String text = request.getText().trim();
        String outputFile = TEMP_DIR + File.separator + "tts-" + UUID.randomUUID() + ".mp3";
        
        try {
            // 构建命令
            ProcessBuilder processBuilder = new ProcessBuilder(
                    "edge-tts",
                    "--voice", voice,
                    "--text", text,
                    "--rate", "+" + (int)((rate - 1.0) * 100) + "%", // 将倍速转换为百分比增减
                    "--write-media", outputFile
            );
            
            logger.info("执行命令: {}", String.join(" ", processBuilder.command()));
            
            // 执行命令
            Process process = processBuilder.start();
            boolean completed = process.waitFor(30, TimeUnit.SECONDS);
            
            // 读取命令输出
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("Edge TTS 输出: {}", line);
                }
            }
            
            // 读取错误输出
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.error("Edge TTS 错误: {}", line);
                }
            }
            
            if (!completed) {
                process.destroyForcibly();
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("TTS生成超时".getBytes());
            }
            
            int exitCode = process.exitValue();
            if (exitCode != 0) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(("TTS生成失败，退出码: " + exitCode).getBytes());
            }
            
            // 读取生成的音频文件
            Path path = Path.of(outputFile);
            byte[] audioBytes = Files.readAllBytes(path);
            
            // 删除临时文件
            Files.delete(path);
            
            // 返回音频
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("audio/mpeg"));
            headers.setContentLength(audioBytes.length);
            
            return new ResponseEntity<>(audioBytes, headers, HttpStatus.OK);
            
        } catch (IOException | InterruptedException e) {
            logger.error("TTS生成错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("TTS生成错误: " + e.getMessage()).getBytes());
        }
    }
    
    @GetMapping("/voices")
    public ResponseEntity<String[]> getAvailableVoices() {
        // 这里可以动态获取edge-tts支持的声音列表
        // 但目前我们只返回固定的中文、日语和英语神经声音列表
        String[] voices = {
                // 中文语音
                "zh-CN-XiaoxiaoNeural",
                "zh-CN-XiaomoNeural",
                "zh-CN-XiaoxuanNeural",
                "zh-CN-YunjianNeural",
                "zh-CN-YunxiNeural",
                "zh-CN-YunyangNeural",
                // 日语语音
                "ja-JP-NanamiNeural",
                "ja-JP-KeitaNeural",
                "ja-JP-AoiNeural",
                "ja-JP-DaichiNeural",
                "ja-JP-MayuNeural",
                "ja-JP-ShioriNeural",
                // 英语语音
                "en-US-JennyNeural",
                "en-US-GuyNeural",
                "en-US-AriaNeural",
                "en-US-DavisNeural",
                "en-US-AmberNeural",
                "en-US-AnaNeural"
        };
        
        return ResponseEntity.ok(voices);
    }
} 