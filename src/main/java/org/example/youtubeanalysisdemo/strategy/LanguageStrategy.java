package org.example.youtubeanalysisdemo.strategy;

import java.util.List;
import java.util.Map;

/**
 * 语言策略接口
 * 用于处理不同语言组合的字幕下载和处理逻辑
 */
public interface LanguageStrategy {
    
    /**
     * 获取主要语言代码
     * @return 主要语言代码（如 "en", "ja"）
     */
    String getPrimaryLanguage();
    
    /**
     * 获取翻译语言代码
     * @return 翻译语言代码（通常是 "zh"）
     */
    String getTranslationLanguage();
    
    /**
     * 获取主要语言的变体列表（按优先级排序）
     * @return 语言变体列表，如 ["en-US", "en-GB", "en"]
     */
    List<String> getPrimaryLanguageVariants();
    
    /**
     * 获取翻译语言的变体列表（按优先级排序）
     * @return 语言变体列表，如 ["zh-Hans", "zh", "zh-CN"]
     */
    List<String> getTranslationLanguageVariants();
    
    /**
     * 获取语言显示名称
     * @return 显示名称，如 "英语", "日语"
     */
    String getDisplayName();
    
    /**
     * 获取语言标识符
     * @return 语言标识符，如 "english", "japanese"
     */
    String getLanguageIdentifier();
    
    /**
     * 是否需要词性标注（主要针对日语）
     * @return true表示需要词性标注
     */
    boolean needsPartOfSpeechTagging();
    
    /**
     * 获取下载优先级配置
     * 定义下载字幕时的优先级策略
     * @return 包含下载策略的映射
     */
    Map<String, Object> getDownloadPriorities();
} 