package org.example.youtubeanalysisdemo.strategy;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 日语语言策略实现
 * 处理日语-中文字幕组合
 */
@Component
public class JapaneseLanguageStrategy implements LanguageStrategy {
    
    @Override
    public String getPrimaryLanguage() {
        return "ja";
    }
    
    @Override
    public String getTranslationLanguage() {
        return "zh";
    }
    
    @Override
    public List<String> getPrimaryLanguageVariants() {
        return Arrays.asList("ja");
    }
    
    @Override
    public List<String> getTranslationLanguageVariants() {
        return Arrays.asList("zh", "zh-Hans", "zh-Hant", "zh-CN", "zh-TW");
    }
    
    @Override
    public String getDisplayName() {
        return "日语";
    }
    
    @Override
    public String getLanguageIdentifier() {
        return "japanese";
    }
    
    @Override
    public boolean needsPartOfSpeechTagging() {
        return true; // 日语需要词性标注
    }
    
    @Override
    public Map<String, Object> getDownloadPriorities() {
        Map<String, Object> priorities = new HashMap<>();
        priorities.put("preferUserUploaded", true);
        priorities.put("fallbackToAutoGenerated", true);
        priorities.put("enableAutoTranslation", true);
        priorities.put("primaryLanguageCodes", "ja");
        priorities.put("translationLanguageCodes", "zh,zh-Hans,zh-Hant");
        return priorities;
    }
} 