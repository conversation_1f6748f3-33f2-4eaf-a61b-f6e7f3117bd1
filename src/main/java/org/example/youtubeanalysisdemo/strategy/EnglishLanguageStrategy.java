package org.example.youtubeanalysisdemo.strategy;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 英语语言策略实现
 * 处理英语-中文字幕组合
 */
@Component
public class EnglishLanguageStrategy implements LanguageStrategy {
    
    @Override
    public String getPrimaryLanguage() {
        return "en";
    }
    
    @Override
    public String getTranslationLanguage() {
        return "zh";
    }
    
    @Override
    public List<String> getPrimaryLanguageVariants() {
        // 按优先级排序：美式英语 > 英式英语 > 通用英语 > 其他变体
        return Arrays.asList("en-US", "en-GB", "en", "en-CA", "en-AU", "en-IN");
    }
    
    @Override
    public List<String> getTranslationLanguageVariants() {
        return Arrays.asList("zh", "zh-Hans", "zh-Han<PERSON>", "zh-C<PERSON>", "zh-TW");
    }
    
    @Override
    public String getDisplayName() {
        return "英语";
    }
    
    @Override
    public String getLanguageIdentifier() {
        return "english";
    }
    
    @Override
    public boolean needsPartOfSpeechTagging() {
        return true; // 修改为支持词性标注
    }
    
    @Override
    public Map<String, Object> getDownloadPriorities() {
        Map<String, Object> priorities = new HashMap<>();
        priorities.put("preferUserUploaded", true);
        priorities.put("fallbackToAutoGenerated", true);
        priorities.put("enableAutoTraslation", true);
        priorities.put("primaryLanguageCodes", "en-US,en-GB,en,en-CA,en-AU");
        priorities.put("translationLanguageCodes", "zh,zh-Hans,zh-Hant");
        return priorities;
    }
} 