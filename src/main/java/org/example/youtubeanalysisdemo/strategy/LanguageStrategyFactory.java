package org.example.youtubeanalysisdemo.strategy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 语言策略工厂
 * 负责创建和管理不同的语言策略实例
 */
@Slf4j
@Component
public class LanguageStrategyFactory {
    
    private final Map<String, LanguageStrategy> strategyMap = new HashMap<>();
    
    /**
     * 构造函数，自动注入所有语言策略实现
     */
    @Autowired
    public LanguageStrategyFactory(List<LanguageStrategy> strategies) {
        for (LanguageStrategy strategy : strategies) {
            strategyMap.put(strategy.getLanguageIdentifier(), strategy);
            log.info("注册语言策略: {} -> {}", strategy.getLanguageIdentifier(), strategy.getDisplayName());
        }
    }
    
    /**
     * 根据语言标识符获取对应的语言策略
     * @param languageIdentifier 语言标识符（如 "english", "japanese"）
     * @return 对应的语言策略实例
     * @throws IllegalArgumentException 如果语言标识符不被支持
     */
    public LanguageStrategy getStrategy(String languageIdentifier) {
        if (languageIdentifier == null || languageIdentifier.trim().isEmpty()) {
            // 默认返回日语策略（保持向后兼容）
            log.info("语言标识符为空，使用默认的日语策略");
            return strategyMap.get("japanese");
        }
        
        LanguageStrategy strategy = strategyMap.get(languageIdentifier.toLowerCase());
        if (strategy == null) {
            log.warn("不支持的语言标识符: {}, 使用默认的日语策略", languageIdentifier);
            return strategyMap.get("japanese");
        }
        
        log.debug("获取语言策略: {} -> {}", languageIdentifier, strategy.getDisplayName());
        return strategy;
    }
    
    /**
     * 获取所有支持的语言策略
     * @return 语言标识符到策略的映射
     */
    public Map<String, LanguageStrategy> getAllStrategies() {
        return new HashMap<>(strategyMap);
    }
    
    /**
     * 检查是否支持指定的语言
     * @param languageIdentifier 语言标识符
     * @return 是否支持该语言
     */
    public boolean isLanguageSupported(String languageIdentifier) {
        return languageIdentifier != null && strategyMap.containsKey(languageIdentifier.toLowerCase());
    }
    
    /**
     * 获取支持的语言列表
     * @return 支持的语言标识符列表
     */
    public List<String> getSupportedLanguages() {
        return strategyMap.keySet().stream().toList();
    }
} 