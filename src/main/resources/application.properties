spring.application.name=LingTube
server.port=8080
server.servlet.session.timeout=7d

# 日志配置已移至logback-spring.xml
# logging.level.org.example.youtubeanalysisdemo=DEBUG
# logging.level.org.springframework=INFO
spring.jpa.open-in-view=false

#h2数据库配置
spring.datasource.url=jdbc:h2:file:./data/appdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.jpa.hibernate.ddl-auto=update
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.h2.console.settings.web-allow-others=false

# subtitles.directory=src/main/resources/static/subtitles
subtitles.directory=${user.dir}/src/main/resources/static/subtitles

spring.thymeleaf.cache=false
spring.thymeleaf.enabled=true
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

spring.web.resources.cache.period=0
spring.web.resources.cache.use-last-modified=false
spring.web.resources.cache.cachecontrol.no-cache=true
spring.web.resources.cache.cachecontrol.no-store=true
spring.web.resources.cache.cachecontrol.must-revalidate=true

#Google认证配置
spring.security.oauth2.client.registration.google.client-id=851857618077-eqhpbq8kfnmgl4a20tijg5ilanh2gt18.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.client-secret=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
spring.security.oauth2.client.registration.google.scope=openid,email,profile
spring.security.oauth2.client.registration.google.redirect-uri={baseUrl}/login/oauth2/code/{registrationId}

# Google翻译API配置
google.translate.api.key=AIzaSyCP3ozcjycqA_XExpD3UngfYQ9IhnWbdEk

# Paddle配置
paddle.webhook.secret=pdl_ntfset_01jvf0qbdny4k0rxf06trtehsq_WNGiVR6EGrtWo/wTRYQmfDEdEoCY+wEg

# Mistral AI配置
spring.ai.mistralai.api-key=7EQz4Z0adf7qZJM6b50SESRHChChHSzx
spring.ai.mistralai.chat.options.model=mistral-small-latest
spring.ai.mistralai.chat.options.temperature=0.7

# 百炼配置
dashscope.api.key=sk-40c76cbc013b44e38bc926c7fe5dd373

# groq配置
groq.api.key=********************************************************

# 视频转录配置
subtitles.save.path=src/main/resources/static/subtitles
audio.temp.path=temp/audio
