<!-- 导航栏组件 -->
<nav class="bg-white shadow-md sticky top-0 z-50">
  <!-- 移除container限制，使用全屏宽度，并增加左右padding -->
  <div class="px-16">
    <div class="flex items-center justify-between py-4">
      <!-- 左侧Logo，靠左对齐 -->
      <div class="w-48 flex justify-start">
        <a href="/index.html" class="flex items-center space-x-2 text-indigo-600 hover:text-indigo-800 transition">
          <img src="/favicon.png" alt="lingtube.net" class="w-6 h-6">
          <span class="text-xl font-bold">lingtube.net</span>
        </a>
      </div>
      
      <!-- 搜索框已移除 -->
      
      <!-- 右侧导航链接，更靠右 -->
      <div class="w-auto flex justify-end space-x-6">
        <!-- 视频学习部分已移除 -->
        <a href="/pages/dialog/dialog.html" id="nav-dialog" class="flex items-center space-x-1 text-gray-700 hover:text-green-600 transition">
          <i class="fas fa-comments text-green-500"></i>
          <span>场景对话</span>
        </a>
        <a href="/pages/review/review.html" id="nav-review" class="flex items-center space-x-1 text-gray-700 hover:text-orange-600 transition">
          <i class="fas fa-book-open text-orange-500"></i>
          <span>单词复习</span>
        </a>
        <a href="/pages/profile/profile.html" id="nav-profile" class="flex items-center space-x-1 text-gray-700 hover:text-cyan-600 transition">
          <i class="fas fa-user-circle text-cyan-500"></i>
          <span>个人主页</span>
        </a>
        <!-- 语言选择器 -->
        <div class="flex items-center ml-6">
          <div class="relative">
            <select id="language-selector" class="appearance-none bg-white border border-gray-300 rounded-lg px-3 py-2 pr-8 text-sm text-gray-700 hover:border-gray-400 focus:outline-none focus:border-indigo-500 transition-colors">
              <option value="japanese">日语</option>
              <option value="english">英语</option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <i class="fas fa-chevron-down text-xs"></i>
            </div>
          </div>
        </div>
        <div class="flex items-center ml-6 relative">
          <label for="dark-mode-toggle" class="switch relative inline-block w-12 h-6 flex items-center cursor-pointer">
            <i class="fas fa-sun text-amber-500 mr-2 text-sm"></i>
            <input type="checkbox" id="dark-mode-toggle" class="opacity-0 w-0 h-0">
            <span class="slider round absolute cursor-pointer top-0 left-0 right-0 bottom-0 bg-gray-300 transition-all duration-300 before:absolute before:h-5 before:w-5 before:left-0.5 before:bottom-0.5 before:bg-white before:transition-all before:duration-300 before:rounded-full"></span>
          </label>
        </div>
      </div>
    </div>
  </div>
</nav>
