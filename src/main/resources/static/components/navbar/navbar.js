// 导航栏加载脚本
document.addEventListener('DOMContentLoaded', function() {
  // 加载导航栏
  fetch('/components/navbar/navbar.html')
    .then(response => response.text())
    .then(data => {
      // 插入导航栏到指定位置
      document.getElementById('navbar-container').innerHTML = data;
      
      // 初始化黑暗模式开关
      initDarkModeToggle();
      
      // 初始化语言选择器
      initLanguageSelector();
      
      // 获取当前页面URL
      const currentPage = window.location.pathname.split('/').pop();
      
      // 重置所有导航项的样式
      const navItems = ['nav-dialog', 'nav-review', 'nav-profile'];
      navItems.forEach(navId => {
        const navElement = document.getElementById(navId);
        if (navElement) {
          navElement.classList.remove('text-blue-600', 'text-green-600', 'text-orange-600', 'text-cyan-600', 'font-medium');
          navElement.classList.add('text-gray-700');
        }
      });
      
      // 根据当前页面设置导航栏高亮
      if (currentPage === 'dialog.html' || currentPage.startsWith('dialog') || window.location.pathname.includes('/pages/dialog/')) {
        const navItem = document.getElementById('nav-dialog');
        if (navItem) {
          navItem.classList.add('text-green-600', 'font-medium');
          navItem.classList.remove('text-gray-700');
        }
      } else if (currentPage === 'review.html' || currentPage.startsWith('review') || window.location.pathname.includes('/pages/review/')) {
        const navItem = document.getElementById('nav-review');
        if (navItem) {
          navItem.classList.add('text-orange-600', 'font-medium');
          navItem.classList.remove('text-gray-700');
        }
      } else if (currentPage === 'profile.html' || currentPage.startsWith('profile') || window.location.pathname.includes('/pages/profile/')) {
        const navItem = document.getElementById('nav-profile');
        if (navItem) {
          navItem.classList.add('text-cyan-600', 'font-medium');
          navItem.classList.remove('text-gray-700');
        }
      }
    })
    .catch(error => console.error('导航栏加载失败:', error));
});

// 初始化黑暗模式开关
function initDarkModeToggle() {
  const darkModeToggle = document.getElementById('dark-mode-toggle');
  if (!darkModeToggle) return;
  
  // 从localStorage读取黑暗模式状态
  const isDarkMode = localStorage.getItem('darkMode') === 'true';
  
  // 初始化黑暗模式状态
  if (isDarkMode) {
    // 同时为 <html> 和 <body> 元素添加 dark-mode 类，确保样式完整应用
    document.documentElement.classList.add('dark-mode');
    document.body.classList.add('dark-mode');
    darkModeToggle.checked = true;
  }
  
  // 监听黑暗模式开关变化
  darkModeToggle.addEventListener('change', function() {
    if (this.checked) {
      // 启用黑暗模式
      // 同时为 <html> 和 <body> 元素添加 dark-mode 类，确保样式完整应用
      document.documentElement.classList.add('dark-mode');
      document.body.classList.add('dark-mode');
      localStorage.setItem('darkMode', 'true');
    } else {
      // 禁用黑暗模式
      // 同时从 <html> 和 <body> 元素移除 dark-mode 类，确保样式完整切换
      document.documentElement.classList.remove('dark-mode');
      document.body.classList.remove('dark-mode');
      localStorage.setItem('darkMode', 'false');
    }
  });
}

// 搜索框相关函数已移除

// 初始化语言选择器
function initLanguageSelector() {
  const languageSelector = document.getElementById('language-selector');
  if (!languageSelector) return;
  
  // 从localStorage读取语言选择
  const savedLanguage = localStorage.getItem('selectedLanguage') || 'japanese';
  languageSelector.value = savedLanguage;
  
  // 监听语言选择变化
  languageSelector.addEventListener('change', function() {
    const selectedLanguage = this.value;
    localStorage.setItem('selectedLanguage', selectedLanguage);
    
    // 如果在review页面，触发数据重新加载
    if (window.location.pathname.includes('/pages/review/') && window.ReviewData) {
      window.ReviewData.loadSavedItems();
    }
    
    // 显示切换提示
    showNavToast(`已切换到${selectedLanguage === 'japanese' ? '日语' : '英语'}模式`);
  });
}

// 显示提示消息
function showNavToast(message) {
  // 创建或获取toast元素
  let toast = document.getElementById('nav-toast-message');
  if (!toast) {
    toast = document.createElement('div');
    toast.id = 'nav-toast-message';
    toast.className = 'fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-6 py-3 rounded-lg shadow-lg opacity-0 transition-opacity duration-300 z-50';
    document.body.appendChild(toast);
  }
  
  // 设置消息并显示
  toast.textContent = message;
  toast.classList.remove('opacity-0');
  toast.classList.add('opacity-100');
  
  // 3秒后隐藏
  setTimeout(() => {
    toast.classList.remove('opacity-100');
    toast.classList.add('opacity-0');
  }, 3000);
}