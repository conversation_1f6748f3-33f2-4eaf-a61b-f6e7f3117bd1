// 解析面板共享组件
document.addEventListener('DOMContentLoaded', function() {
    initAnalysisPanel();
});

// 初始化解析面板
function initAnalysisPanel() {
    // 确保DOM中存在指定的容器元素
    const analysisContainer = document.getElementById('analysis-panel-container');
    if (!analysisContainer) {
        console.error('未找到解析面板容器元素');
        return;
    }

    // 插入解析面板HTML
    analysisContainer.innerHTML = `
        <div class="fixed top-0 right-0 h-full w-1/4 lg:w-1/4 bg-white shadow-lg transform translate-x-full transition-transform duration-300 z-50" id="analysis-panel">
            <div class="glass-container p-4 rounded-xl shadow-lg h-full flex flex-col">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                    <span id="analysis-title">AI解析结果</span>
                </h2>
                <div id="analysis-container" class="mt-2 text-gray-700 overflow-y-auto flex-1" style="max-height: calc(100% - 100px);">
                    <p class="analysis-item p-4 rounded-lg bg-gray-50">等待分析...</p>
                </div>
                <div class="flex justify-center mt-4">
                    <button onclick="saveSelectedContent()" class="save-btn flex items-center justify-center px-6 py-2 rounded-lg">
                        <i class="fas fa-bookmark mr-2"></i>一键收藏
                    </button>
                </div>
            </div>
            <!-- 推拉手柄 -->
            <button class="absolute top-1/2 -left-8 transform -translate-y-1/2 bg-gray-600 text-white px-2 py-4 rounded-l-lg hover:bg-gray-700 transition flex items-center justify-center" onclick="toggleAnalysisPanel()" id="panel-toggle">
                <i class="fas fa-chevron-left text-lg"></i>
            </button>
        </div>
    `;
}

// 切换解析面板显示/隐藏
function toggleAnalysisPanel() {
    const mainContainer = document.getElementById('main-container');
    const analysisPanel = document.getElementById('analysis-panel');
    const toggleIcon = document.querySelector('#panel-toggle i');
    
    mainContainer.classList.toggle('is-analysis-open');
    
    if (mainContainer.classList.contains('is-analysis-open')) {
        analysisPanel.style.transform = 'translateX(0)';
        toggleIcon.classList.replace('fa-chevron-left', 'fa-chevron-right');
    } else {
        analysisPanel.style.transform = 'translateX(100%)';
        toggleIcon.classList.replace('fa-chevron-right', 'fa-chevron-left');
    }
}

// 获取当前选择的语言
function getCurrentLanguage() {
    // 从localStorage获取语言设置
    return localStorage.getItem('selectedLanguage') || 'japanese';
}

// 保存选中内容的通用方法
async function saveSelectedContent() {
    const checkboxes = document.querySelectorAll('#analysis-container .custom-checkbox:checked');
    if (checkboxes.length === 0) {
        Toastify({
            text: '请先选择要收藏的内容',
            duration: 3000,
            gravity: 'top',
            position: 'center',
            backgroundColor: '#ef4444'
        }).showToast();
        return;
    }

    const currentLanguage = getCurrentLanguage();
    const contentsToSave = [];

    checkboxes.forEach(checkbox => {
        const content = extractContentFromInput(checkbox);
        if (content) {
            // 英语模式下，对于固定搭配，只收藏冒号前的内容
            if (currentLanguage === 'english' && checkbox.classList.contains('phrase-content')) {
                // 如果内容包含冒号，只取冒号前的部分
                if (content.includes(':')) {
                    const phraseOnly = content.split(':')[0].trim();
                    contentsToSave.push(phraseOnly);
                } else {
                    // 如果没有冒号，移除▲后保存
                    contentsToSave.push(content.replace('▲','').trim());
                }
            } else {
                // 日语模式或非固定搭配，移除'▲'后收藏
                contentsToSave.push(content.replace('▲','').trim());
            }
        }
    });

    if (contentsToSave.length === 0) {
        Toastify({
            text: '未能提取有效内容进行收藏',
            duration: 3000,
            gravity: 'top',
            position: 'center',
            backgroundColor: '#f59e0b'
        }).showToast();
        return;
    }

    console.log('将保存的内容:', contentsToSave, '语言:', currentLanguage);

    fetch('/api/subtitle/save', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
            contents: contentsToSave,
            language: currentLanguage
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            Toastify({
                text: data.message || '收藏成功！',
                duration: 3000,
                gravity: 'top',
                position: 'center',
                backgroundColor: '#22c55e'
            }).showToast();
            checkboxes.forEach(checkbox => checkbox.checked = false);
        } else {
            throw new Error(data.message || '保存失败');
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        Toastify({
            text: error.message || '收藏失败，请重试',
            duration: 3000,
            gravity: 'top',
            position: 'center',
            backgroundColor: '#ef4444'
        }).showToast();
    });
}

// 从复选框元素中提取内容 - 与插件逻辑完全一致
function extractContentFromInput(checkbox) {
    // 对于英语固定搭配（有phrase-content类的复选框）
    if (checkbox.classList.contains('phrase-content')) {
        // 获取复选框后面的文本内容
        let nextNode = checkbox.nextSibling;
        let textContent = '';

        while (nextNode) {
            if (nextNode.nodeType === 1 && nextNode.tagName === 'BR') {
                break; // 遇到<br>标签结束
            }
            if (nextNode.nodeType === 1 && nextNode.tagName === 'INPUT') {
                break; // 遇到下一个input标签结束
            }

            if (nextNode.nodeType === 3) { // 文本节点
                textContent += nextNode.textContent;
            } else if (nextNode.nodeType === 1) { // 元素节点
                textContent += nextNode.textContent;
            }
            
            nextNode = nextNode.nextSibling;
        }

        return textContent.trim();
    }
    
    // 对于日语（有label的复选框）
    const label = document.querySelector(`label[for="${checkbox.id}"]`);
    if (label) {
        let nextNode = label.nextSibling;
        let textContent = '';
        
        // 收集label后面的所有文本节点，直到遇到下一个input元素
        while (nextNode && 
              (!nextNode.tagName || 
               (nextNode.tagName !== 'INPUT' && 
                (!nextNode.classList || 
                 !nextNode.classList.contains('custom-checkbox'))))) {
            
            if (nextNode.nodeType === 3) { // 文本节点
                textContent += nextNode.textContent;
            } else if (nextNode.tagName === 'BR') {
                textContent += '\n';
            } else if (nextNode.tagName) { // 其他标签如<strong>
                textContent += nextNode.textContent;
            }
            
            nextNode = nextNode.nextSibling;
        }
        
        return textContent.trim();
    }
    
    // Fallback
    let parentElement = checkbox.parentElement;
    if (parentElement) {
        return parentElement.textContent.trim();
    }
    return '';
} 