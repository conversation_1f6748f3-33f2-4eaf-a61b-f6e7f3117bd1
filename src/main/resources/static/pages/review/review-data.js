/**
 * 数据管理模块
 * 负责管理应用的全局状态和数据加载
 */

// 全局状态变量
const itemsPerPage = 5;
let currentPage = 1;
let allItems = [];
let currentLearnedState = 0; // 0表示正在学习，1表示已学会

/**
 * 获取当前选择的语言
 */
function getCurrentLanguage() {
    return localStorage.getItem('selectedLanguage') || 'japanese';
}

/**
 * 根据语言过滤数据
 */
function filterItemsByLanguage(items) {
    const currentLanguage = getCurrentLanguage();
    return items.filter(item => {
        // 如果item没有lang字段，默认为japanese（向后兼容）
        const itemLang = item.lang || 'japanese';
        return itemLang === currentLanguage;
    });
}

/**
 * 加载保存的条目数据
 */
function loadSavedItems() {
    fetch(`/api/subtitle/list?learned=${currentLearnedState}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应异常');
            }
            return response.json();
        })
        .then(data => {
            const rawItems = Array.isArray(data) ? data : [];
            // 根据当前选择的语言过滤数据
            allItems = filterItemsByLanguage(rawItems);
            
            if (allItems.length === 0) {
                console.warn('当前语言下没有数据');
            }
            // 重置到第一页
            currentPage = 1;
            // 通知其他模块数据已更新
            if (window.ReviewRenderer) {
                window.ReviewRenderer.renderItems();
                window.ReviewRenderer.renderPagination();
            }
            if (window.ReviewActions) {
                window.ReviewActions.updateMarkLearnedButton();
            }
        })
        .catch(error => {
            console.error('加载收藏内容失败:', error);
            allItems = [];
            if (window.ReviewRenderer) {
                window.ReviewRenderer.renderItems();
                window.ReviewRenderer.renderPagination();
            }
            if (window.ReviewActions) {
                window.ReviewActions.updateMarkLearnedButton();
            }
            Toastify({
                text: '加载收藏内容失败，请检查网络或联系管理员',
                duration: 3000,
                gravity: 'top',
                position: 'center',
                backgroundColor: '#ef4444'
            }).showToast();
        });
}

/**
 * 获取当前页的数据
 */
function getCurrentPageItems() {
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    return allItems.slice(start, end);
}

/**
 * 获取总页数
 */
function getTotalPages() {
    return Math.ceil(allItems.length / itemsPerPage);
}

/**
 * 切换标签页
 */
function switchTab(tabId) {
    const tabs = document.querySelectorAll('.tab-btn');
    tabs.forEach(tab => tab.classList.remove('active-tab'));
    
    const activeTab = document.getElementById(tabId);
    activeTab.classList.add('active-tab');
    
    currentLearnedState = tabId === 'tab-learning' ? 0 : 1;
    currentPage = 1;
    loadSavedItems();
}

/**
 * 从本地数组中移除指定的条目
 */
function removeItemsFromLocal(itemsToRemove) {
    // 按索引从大到小排序，这样删除时不会影响前面元素的索引
    itemsToRemove.sort((a, b) => b.index - a.index);
    
    itemsToRemove.forEach(item => {
        allItems.splice(item.index, 1);
    });

    // 调整当前页码
    const totalPages = getTotalPages();
    if (currentPage > totalPages && totalPages > 0) {
        currentPage = totalPages;
    } else if (totalPages === 0) {
        currentPage = 1;
    }
}

// 导出模块接口
window.ReviewData = {
    // 状态变量
    get itemsPerPage() { return itemsPerPage; },
    get currentPage() { return currentPage; },
    set currentPage(value) { currentPage = value; },
    get allItems() { return allItems; },
    get currentLearnedState() { return currentLearnedState; },
    
    // 函数
    loadSavedItems,
    getCurrentPageItems,
    getTotalPages,
    switchTab,
    removeItemsFromLocal,
    getCurrentLanguage,
    filterItemsByLanguage
}; 