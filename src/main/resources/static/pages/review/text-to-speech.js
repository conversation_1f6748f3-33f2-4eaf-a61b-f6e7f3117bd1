document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const textInput = document.getElementById('textInput');
    const voiceSelect = document.getElementById('voiceSelect');
    const speakButton = document.getElementById('speakButton');
    const speakButtonLarge = document.getElementById('speakButtonLarge');
    const clearButton = document.getElementById('clearButton');
    const statusMessage = document.getElementById('statusMessage');
    
    // 检查是否有语音正在播放
    let isPlaying = false;
    let audioElement = null;
    
    // 朗读文本函数
    function speakText() {
        const text = textInput.value.trim();
        if (!text) {
            showStatus('请输入要朗读的文本', 'warning');
            return;
        }
        
        if (isPlaying) {
            stopSpeaking();
            return;
        }
        
        const voice = voiceSelect.value;
        
        showStatus('正在生成语音...', 'info');
        
        // 发送请求到后端
        fetch('/api/tts/speak', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: text,
                voice: voice
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('语音生成失败');
            }
            return response.blob();
        })
        .then(audioBlob => {
            const audioUrl = URL.createObjectURL(audioBlob);
            playAudio(audioUrl);
            showStatus('正在朗读...', 'success');
        })
        .catch(error => {
            console.error('错误:', error);
            showStatus('语音生成失败: ' + error.message, 'danger');
        });
    }
    
    // 播放音频
    function playAudio(audioUrl) {
        if (audioElement) {
            audioElement.pause();
            URL.revokeObjectURL(audioElement.src);
        }
        
        audioElement = new Audio(audioUrl);
        audioElement.onended = function() {
            isPlaying = false;
            updatePlayButton(false);
            showStatus('朗读完成', 'success');
        };
        
        audioElement.onerror = function() {
            isPlaying = false;
            updatePlayButton(false);
            showStatus('音频播放失败', 'danger');
        };
        
        audioElement.play();
        isPlaying = true;
        updatePlayButton(true);
    }
    
    // 停止朗读
    function stopSpeaking() {
        if (audioElement) {
            audioElement.pause();
            URL.revokeObjectURL(audioElement.src);
            audioElement = null;
        }
        isPlaying = false;
        updatePlayButton(false);
        showStatus('朗读已停止', 'warning');
    }
    
    // 更新播放按钮状态
    function updatePlayButton(playing) {
        if (playing) {
            speakButton.classList.remove('bi-megaphone-fill');
            speakButton.classList.add('bi-stop-fill');
            speakButton.title = '停止朗读';
            
            speakButtonLarge.innerHTML = '<i class="bi bi-stop-fill me-2"></i>停止朗读';
            speakButtonLarge.classList.remove('btn-primary');
            speakButtonLarge.classList.add('btn-danger');
        } else {
            speakButton.classList.remove('bi-stop-fill');
            speakButton.classList.add('bi-megaphone-fill');
            speakButton.title = '朗读文本';
            
            speakButtonLarge.innerHTML = '<i class="bi bi-megaphone-fill me-2"></i>朗读文本';
            speakButtonLarge.classList.remove('btn-danger');
            speakButtonLarge.classList.add('btn-primary');
        }
    }
    
    // 显示状态消息
    function showStatus(message, type) {
        statusMessage.textContent = message;
        statusMessage.className = '';
        statusMessage.classList.add('alert', `alert-${type}`);
        
        // 如果是成功或错误消息，3秒后自动消失
        if (type === 'success' || type === 'danger') {
            setTimeout(() => {
                statusMessage.classList.add('fade');
                setTimeout(() => {
                    statusMessage.textContent = '';
                    statusMessage.className = '';
                }, 300);
            }, 3000);
        }
    }
    
    // 清空文本
    function clearText() {
        textInput.value = '';
        statusMessage.textContent = '';
        statusMessage.className = '';
    }
    
    // 事件监听
    speakButton.addEventListener('click', speakText);
    speakButtonLarge.addEventListener('click', speakText);
    clearButton.addEventListener('click', clearText);
    
    // 初始隐藏状态消息
    statusMessage.style.display = 'none';
}); 