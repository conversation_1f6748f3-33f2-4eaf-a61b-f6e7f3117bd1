<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复习</title>
    <!-- 立即加载暗色模式脚本，防止页面闪烁 -->
    <script src="/shared/utils/dark-mode.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.0.0/marked.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="review.css">
    <link rel="icon" type="image/png" href="/favicon.png">
    <link rel="stylesheet" href="/assets/css/dark-mode.css">
</head>
<body class="min-h-screen flex flex-col bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50">

<!-- Navigation Bar -->
<div id="navbar-container"></div>
<script src="/components/navbar/navbar.js"></script>

<div class="flex-1 container mx-auto p-6 mt-6">
    <div class="max-w-full mx-auto px-4">
        <div class="glass-container p-6 rounded-xl shadow-lg">
            <!-- 添加Tab切换 -->
            <div class="tabs-container mb-6">
                <div class="tabs flex border-b">
                    <button id="tab-learning" class="tab-btn active-tab px-6 py-2 font-medium rounded-t-lg">
                        <i class="fas fa-graduation-cap mr-2"></i>正在学习
                    </button>
                    <button id="tab-learned" class="tab-btn px-6 py-2 font-medium rounded-t-lg">
                        <i class="fas fa-award mr-2"></i>已学会
                    </button>
                </div>
            </div>
            
            <div class="review-header-grid gap-4 mb-4 text-gray-600 font-medium text-center">
                <div></div>
                <div>单词和语法</div>
                <div>解析</div>
                <div>朗读</div>
                <div>视频片段</div>
                <div>联想</div>
            </div>
            <div id="saved-items-container" class="flex flex-col gap-6">
                <!-- 条目将通过 JS 动态生成 -->
            </div>
            <!-- 已学会按钮 -->
            <button id="mark-learned-btn" onclick="markAsLearned()" class="learned-btn">
                <i class="fas fa-check-circle mr-2"></i>已学会
            </button>
            <!-- 分页导航 -->
            <div id="pagination" class="flex justify-center mt-6 gap-4">
                <button id="prev-page" class="pagination-btn bg-indigo-600 text-white hover:bg-indigo-700 disabled:bg-gray-400" disabled>上一页</button>
                <span id="page-info" class="text-gray-600 px-4 py-2">1 / 1</span>
                <button id="next-page" class="pagination-btn bg-indigo-600 text-white hover:bg-indigo-700 disabled:bg-gray-400" disabled>下一页</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Association Content -->
<div id="association-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-xl p-6 w-full max-w-5xl relative glass-container">
        <button id="close-modal" class="absolute top-4 right-4 text-gray-600 hover:text-gray-800">
            <i class="fas fa-times text-2xl"></i>
        </button>
        <h3 class="text-lg font-semibold text-gray-800 mb-4">联想内容</h3>
        <div class="grid grid-cols-10 gap-4 mb-2 text-gray-600 font-medium text-center">
            <div class="col-span-1"></div> <!-- Checkbox column -->
            <div class="col-span-2"></div>
            <div class="col-span-7">解析</div>
        </div>
        <div id="modal-items-container" class="flex flex-col gap-4 max-h-[60vh] overflow-y-auto pb-16">
            <!-- Modal items will be dynamically generated -->
        </div>
        <button id="collect-btn" class="absolute bottom-4 right-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-lg hover:from-green-600 hover:to-emerald-700 transition">
            一键收藏
        </button>
    </div>
</div>

<footer class="w-full text-center py-4 text-gray-700 text-sm mt-auto bg-white shadow-inner">
    <div class="container mx-auto px-4">
        <p>© 2025 lingtube.net</p>
    </div>
</footer>

<!-- 模块化脚本引入 - 按依赖顺序加载 -->
<script src="review-permissions.js"></script>
<script src="review-formatter.js"></script>
<script src="review-data.js"></script>
<script src="review-audio.js"></script>
<script src="review-context.js"></script>
<script src="review-renderer.js"></script>
<script src="review-association.js"></script>
<script src="review-actions.js"></script>
<script src="review-main.js"></script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'93e19d25586d455e',t:'MTc0Njk2NTg1My4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script>
</body>
</html>