/**
 * 真实语境模块
 * 负责处理YouGlish真实语境功能
 */

/**
 * 获取YouGlish语言映射
 */
function getYouGlishLanguage(language) {
    const languageMap = {
        'japanese': 'japanese',
        'english': 'english'
    };
    return languageMap[language] || 'english';
}

/**
 * 打开YouGlish真实语境模态框
 */
async function openContextModal(event, contextElement) {
    // 阻止事件冒泡
    event.stopPropagation();
    
    // 检查Plus或Premium权限
    const permissionResult = await window.ReviewPermissions.checkPlusOrPremiumPermission();
    
    if (!permissionResult.success || !permissionResult.isPlusOrPremium) {
        // 显示升级提示 - Plus会员或Premium会员都可以使用
        window.ReviewPermissions.showUpgradeDialog('真实语境功能', 'Plus');
        return;
    }
    
    const itemDiv = contextElement.closest('.saved-item');
    const wordElement = itemDiv.querySelector('.saved-item-column-word');
    const keyword = wordElement.textContent.trim();
    
    // 获取当前项的语言
    const itemLang = wordElement.getAttribute('data-lang') || 'japanese';
    const youglishLang = getYouGlishLanguage(itemLang);
    
    // 构建YouGlish URL
    const youglishUrl = `https://youglish.com/pronounce/${encodeURIComponent(keyword)}/${youglishLang}`;
    
    console.log(`真实语境 - 关键词: ${keyword}, 语言: ${itemLang}, YouGlish URL: ${youglishUrl}`);
    
    // 在新窗口中打开YouGlish
    window.open(youglishUrl, '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');
}

// 导出模块接口
window.ReviewContext = {
    openContextModal
}; 