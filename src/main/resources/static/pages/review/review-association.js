/**
 * 联想功能模块
 * 负责处理联想内容的生成和模态框管理
 */

/**
 * 从英语单词格式中提取纯单词部分
 * 例如: "write /raɪt/" -> "write"
 */
function extractEnglishWord(wordWithPhonetic) {
    // 匹配单词部分，去掉音标
    const match = wordWithPhonetic.match(/^([a-zA-Z\s]+)/);
    return match ? match[1].trim() : wordWithPhonetic;
}

/**
 * 打开联想模态框
 */
async function openAssociationModal(event, associationElement) {
    // 阻止事件冒泡
    event.stopPropagation();
    
    // 检查Premium权限
    const permissionResult = await window.ReviewPermissions.checkPremiumPermission();
    
    if (!permissionResult.success || !permissionResult.isPremium) {
        // 显示升级提示 - 仅限Premium会员
        window.ReviewPermissions.showUpgradeDialog('联想功能', 'Premium');
        return;
    }
    
    const modal = document.getElementById('association-modal');
    const modalContainer = document.getElementById('modal-items-container');
    
    // 显示加载中效果
    modalContainer.innerHTML = `
        <div class="loading-container">
            <!-- 仅保留外圈蓝色加载动画，去掉内侧紫色 SVG -->
            <div class="loading-spinner"></div>
            <p class="mt-4 text-gray-700">正在生成联想内容，请稍候...</p>
        </div>
    `;
    
    // 显示模态框
    modal.classList.remove('hidden');

    // 获取当前项的单词
    const itemDiv = associationElement.closest('.saved-item');
    const wordElement = itemDiv.querySelector('.saved-item-column-word');
    const keyword = wordElement.textContent.trim();
    
    // 获取当前项的类型和语言
    const type = wordElement.getAttribute('data-type') || 'word';
    const lang = wordElement.getAttribute('data-lang') || 'japanese';
    console.log(`联想内容 - 关键词: ${keyword}, 类型: ${type}, 语言: ${lang}`);
    
    // 调用联想API，传递语言参数
    fetch('/api/subtitle/associate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: `keyword=${encodeURIComponent(keyword)}&type=${type}&lang=${lang}`
    })
        .then(response => {
            if (!response.ok) {
            throw new Error('网络响应异常: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
        if (data.success && data.result) {
            // 将AI生成的Markdown转换为HTML并显示
            // 这里需要处理▲符号以生成复选框
            const markdownContent = data.result;
            
            // 定义随机颜色数组
            const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899'];
            
            // 将markdown中的▲替换为复选框
            let checkboxCounter = 0;
            const processedMarkdown = markdownContent.replace(/▲/g, () => {
                const randomColor = colors[Math.floor(Math.random() * colors.length)];
                checkboxCounter++;
                return `<input type="checkbox" class="custom-checkbox" id="modal-checkbox-${checkboxCounter}" style="color: ${randomColor};" data-id="new-${checkboxCounter}"><label for="modal-checkbox-${checkboxCounter}" class="inline-block align-middle">▲</label>`;
            });
            
            // 清空容器
            modalContainer.innerHTML = '';

            // 使用marked库将Markdown转换为HTML
            const htmlContent = marked.parse(processedMarkdown);
            
            // 创建一个临时容器来解析HTML
            const tempContainer = document.createElement('div');
            tempContainer.innerHTML = htmlContent;
            
            // 遍历所有段落，找出包含▲的部分，创建条目
            const paragraphs = tempContainer.querySelectorAll('p');
            
            if (paragraphs.length === 0) {
                modalContainer.innerHTML = '<p class="text-gray-500 text-center py-4">无法解析联想内容</p>';
                return;
            }

            // 遍历段落，创建条目
            paragraphs.forEach((paragraph, index) => {
                // 只处理包含复选框的段落
                if (paragraph.querySelector('.custom-checkbox')) {
                    const itemContent = paragraph.innerHTML;
                    
                    // 提取单词和详情
                    // 我们假设格式是:checkbox + "单词本身（假名注音）:详情"
                    const colonIndex = itemContent.indexOf(':');
                    
                    if (colonIndex > 0) {
                        const rawText = paragraph.textContent;
                        // 去除▲和前后空格
                        const cleanText = rawText.replace(/▲/, '').trim();
                        const wordEndIndex = cleanText.indexOf(':');
                        
                        if (wordEndIndex > 0) {
                            const wordPart = cleanText.substring(0, wordEndIndex).trim();
                            const details = cleanText.substring(wordEndIndex + 1).trim();

                            // 根据语言处理单词显示
                            let displayWord = wordPart;
                            let fullDetails = details;
                            
                            if (lang === 'english' && type === 'word') {
                                // 对于英语单词，显示部分是纯单词，详情部分则需要重新组合以包含音标
                                displayWord = extractEnglishWord(wordPart);
                                fullDetails = wordPart + ':' + details;
                            }

                            // 创建条目
                            const itemDiv = document.createElement('div');
                            itemDiv.className = 'saved-item modal-item';
                            
                            // 根据语言选择格式化方式
                            const formattedDetails = window.ReviewFormatter.formatAnalysisTextByLanguage(fullDetails, lang, type);
                            
                            itemDiv.innerHTML = `
                                <div class="saved-item-checkbox">
                                    ${paragraph.querySelector('.custom-checkbox').outerHTML}
                                </div>
                                <div class="saved-item-column-word" data-type="${type}" data-lang="${lang}">${displayWord}</div>
                                <div class="saved-item-column-details">${formattedDetails}</div>
                            `;
                            
                            modalContainer.appendChild(itemDiv);
                        } else {
                            // 如果无法正确解析，直接显示原内容
                            const itemDiv = document.createElement('div');
                            itemDiv.className = 'saved-item modal-item';
                            
                            // 根据语言选择格式化方式
                            const formattedDetails = window.ReviewFormatter.formatAnalysisTextByLanguage(cleanText, lang, type);
                            
                            itemDiv.innerHTML = `
                                <div class="saved-item-checkbox">
                                    ${paragraph.querySelector('.custom-checkbox').outerHTML}
                                </div>
                                <div class="saved-item-column-word" data-type="${type}" data-lang="${lang}">未知</div>
                                <div class="saved-item-column-details">${formattedDetails}</div>
                            `;
                            
                            modalContainer.appendChild(itemDiv);
                        }
                    } else {
                        // 如果没有找到冒号，将整个内容放在details中
                        const itemDiv = document.createElement('div');
                        itemDiv.className = 'saved-item modal-item';
                        
                        const cleanContent = paragraph.textContent.replace(/▲/, '').trim();
                        // 根据语言选择格式化方式
                        const formattedDetails = window.ReviewFormatter.formatAnalysisTextByLanguage(cleanContent, lang, type);
                        
                        itemDiv.innerHTML = `
                            <div class="saved-item-checkbox">
                                ${paragraph.querySelector('.custom-checkbox').outerHTML}
                            </div>
                            <div class="saved-item-column-word" data-type="${type}" data-lang="${lang}">未知</div>
                            <div class="saved-item-column-details">${formattedDetails}</div>
                        `;
                        
                        modalContainer.appendChild(itemDiv);
                    }
                }
            });
            
            if (modalContainer.children.length === 0) {
                modalContainer.innerHTML = '<p class="text-gray-500 text-center py-4">未找到可用的联想内容</p>';
            }
        } else {
            modalContainer.innerHTML = `<p class="text-gray-500 text-center py-4">联想内容生成失败: ${data.error || '未知错误'}</p>`;
        }
        })
        .catch(error => {
        console.error('联想内容请求失败:', error);
        modalContainer.innerHTML = `<p class="text-gray-500 text-center py-4">联想内容请求失败: ${error.message}</p>`;
        
            Toastify({
            text: '联想内容请求失败，请重试',
                duration: 3000,
                gravity: 'top',
                position: 'center',
                backgroundColor: '#ef4444'
            }).showToast();
        });
}

/**
 * 关闭联想模态框
 */
function closeAssociationModal() {
    const modal = document.getElementById('association-modal');
    modal.classList.add('hidden');
}

/**
 * 收集选中的联想条目
 */
function collectSelectedItems() {
    const checkboxes = document.querySelectorAll('#modal-items-container input[type="checkbox"]:checked');
    if (checkboxes.length === 0) {
        Toastify({
            text: '请至少选择一条内容！',
            duration: 3000,
            gravity: 'top',
            position: 'center',
            backgroundColor: '#ef4444'
        }).showToast();
        return;
    }

    // 收集要保存的内容
    const contentsToSave = [];
    const types = [];
    let language = 'japanese'; // 默认语言
    
    Array.from(checkboxes).forEach(checkbox => {
        // 找到包含此复选框的条目
        const itemDiv = checkbox.closest('.saved-item');
        if (itemDiv) {
            const wordElement = itemDiv.querySelector('.saved-item-column-word');
            const detailsElement = itemDiv.querySelector('.saved-item-column-details');
            
            if (wordElement && detailsElement) {
                const displayWord = wordElement.textContent.trim();
                const details = detailsElement.textContent.trim();
                const type = wordElement.getAttribute('data-type') || 'word';
                const lang = wordElement.getAttribute('data-lang') || 'japanese';
                
                // 使用第一个条目的语言作为整体语言
                if (contentsToSave.length === 0) {
                    language = lang;
                }
                
                // 统一使用"单词:详情"格式，将预先生成的分析结果直接传递给后端
                // 这里的 detailsElement.textContent 包含了未被HTML格式化的原始分析文本
                contentsToSave.push(`${displayWord}:${details}`);
                types.push(type);
            }
        }
    });
    
    if (contentsToSave.length === 0) {
        Toastify({
            text: '无法获取选中内容的详情',
            duration: 3000,
            gravity: 'top',
            position: 'center',
            backgroundColor: '#f59e0b'
        }).showToast();
        return;
    }
    
    // 显示保存中的提示
    const collectBtn = document.getElementById('collect-btn');
    const originalBtnText = collectBtn.innerHTML;
    collectBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>保存中...';
    collectBtn.disabled = true;
    
    // 发送到后端保存
    fetch('/api/subtitle/save', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
            contents: contentsToSave,
            types: types, // 添加types数组，如果后端API支持的话
            language: language // 添加语言参数
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络响应异常: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success') {
            // 刷新列表
            window.ReviewData.loadSavedItems();

    Toastify({
        text: '已收藏选中内容！',
        duration: 3000,
        gravity: 'top',
        position: 'center',
        backgroundColor: '#22c55e'
    }).showToast();

            // 关闭模态框
    closeAssociationModal();
        } else {
            throw new Error(data.message || '保存失败');
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        Toastify({
            text: error.message || '保存失败，请重试',
            duration: 3000,
            gravity: 'top',
            position: 'center',
            backgroundColor: '#ef4444'
        }).showToast();
    })
    .finally(() => {
        // 恢复按钮状态
        collectBtn.innerHTML = originalBtnText;
        collectBtn.disabled = false;
    });
}

// 导出模块接口
window.ReviewAssociation = {
    openAssociationModal,
    closeAssociationModal,
    collectSelectedItems
}; 