/**
 * 音频播放模块
 * 负责处理TTS语音播放功能
 */

/**
 * 获取语言特定的TTS声音配置
 */
function getVoiceConfig(language) {
    const configs = {
        'japanese': {
            voice: 'ja-JP-NanamiNeural',
            lang: 'japanese'
        },
        'english': {
            voice: 'en-US-JennyNeural', // 英语默认女声
            lang: 'english'
        }
    };
    
    return configs[language] || configs['japanese']; // 默认使用日语
}

/**
 * 提取例文内容用于朗读
 */
function extractExampleText(detailsElement) {
    let speechText = '';
    const exampleElement = detailsElement.querySelector('.example-text');
    
    if (exampleElement) {
        // 找到了例句元素，直接使用其文本
        speechText = exampleElement.textContent.trim();
    } else {
        // 如果没有找到例句元素，尝试查找可能包含例文内容的其他元素
        const markerElements = detailsElement.querySelectorAll('.marker-word');
        let exampleMarker = null;
        
        // 查找"例文:"标记
        for (const marker of markerElements) {
            if (marker.textContent.includes('例文:') || marker.textContent.includes('例文：')) {
                exampleMarker = marker;
                break;
            }
        }
        
        if (exampleMarker) {
            // 找到了例文标记，获取它后面的文本内容直到下一个标记
            let nextElement = exampleMarker.nextSibling;
            while (nextElement && 
                  (!nextElement.classList || !nextElement.classList.contains('marker-word'))) {
                if (nextElement.nodeType === Node.TEXT_NODE && nextElement.textContent.trim()) {
                    speechText += nextElement.textContent;
                } else if (nextElement.nodeType === Node.ELEMENT_NODE && 
                          !nextElement.classList.contains('details-cover')) {
                    // 排除details-cover元素
                    speechText += nextElement.textContent;
                }
                nextElement = nextElement.nextSibling;
            }
        }
    }
    
    // 清理文本，去除多余空格和换行
    speechText = speechText.replace(/\s+/g, ' ').trim();
    speechText = speechText.replace(/^\s*[,，:：]\s*/, ''); // 移除开头的逗号和冒号
    
    return speechText;
}

/**
 * 朗读文本内容
 */
async function speakText(event, audioElement) {
    event.stopPropagation();
    
    // 检查Plus/Premium权限
    const permissionResult = await window.ReviewPermissions.checkPlusOrPremiumPermission();
    
    if (!permissionResult.success || !permissionResult.isPlusOrPremium) {
        // 显示升级提示 - Plus会员或Premium会员都可以使用
        window.ReviewPermissions.showUpgradeDialog('朗读功能', 'Plus');
        return;
    }
    
    const itemDiv = audioElement.closest('.saved-item');
    const detailsElement = itemDiv.querySelector('.saved-item-column-details');
    const wordElement = itemDiv.querySelector('.saved-item-column-word');
    
    // 获取条目的语言信息
    const itemLang = wordElement.getAttribute('data-lang') || 'japanese';
    
    // 提取例文内容
    let speechText = extractExampleText(detailsElement);
    
    console.log('要朗读的文本:', speechText, '语言:', itemLang);
    
    if (!speechText) {
        console.warn('未找到可朗读的文本');
        return;
    }

    // 获取语言特定的声音配置
    const voiceConfig = getVoiceConfig(itemLang);
    
    // 先重置之前所有语音元素的状态
    document.querySelectorAll('.audio-icon').forEach(icon => {
        icon.style.pointerEvents = 'auto';
        icon.style.opacity = '1';
    });

    // 设置当前点击的元素状态
    audioElement.style.pointerEvents = 'none';
    audioElement.style.opacity = '0.5';

    // 调用后端TTS接口，传递语言参数
    fetch('/api/tts/speak', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            text: speechText,
            voice: voiceConfig.voice,
            lang: voiceConfig.lang, // 添加语言参数
            rate: 1.0 // 正常语速
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('TTS请求失败: ' + response.status);
        }
        return response.arrayBuffer();
    })
    .then(arrayBuffer => {
        // 将音频数据转换为Blob
        const audioBlob = new Blob([arrayBuffer], { type: 'audio/mpeg' });
        const audioUrl = URL.createObjectURL(audioBlob);
        
        // 播放音频
        const audio = new Audio(audioUrl);
        
        audio.onended = () => {
            // 播放结束后恢复按钮状态
            audioElement.style.pointerEvents = 'auto';
            audioElement.style.opacity = '1';
            // 释放URL对象
            URL.revokeObjectURL(audioUrl);
        };
        
        audio.onerror = (error) => {
            console.error('音频播放错误:', error);
            audioElement.style.pointerEvents = 'auto';
            audioElement.style.opacity = '1';
            URL.revokeObjectURL(audioUrl);
        };
        
        // 播放音频
        audio.play().catch(error => {
            console.error('音频播放失败:', error);
            audioElement.style.pointerEvents = 'auto';
            audioElement.style.opacity = '1';
            URL.revokeObjectURL(audioUrl);
        });
    })
    .catch(error => {
        console.error('TTS生成失败:', error);
        audioElement.style.pointerEvents = 'auto';
        audioElement.style.opacity = '1';
        
        Toastify({
            text: 'TTS生成失败，请重试',
            duration: 3000,
            gravity: 'top',
            position: 'center',
            backgroundColor: '#ef4444'
        }).showToast();
    });
}

// 导出模块接口
window.ReviewAudio = {
    speakText
}; 