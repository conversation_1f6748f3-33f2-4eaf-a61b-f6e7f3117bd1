/**
 * 用户操作模块
 * 负责处理用户的各种操作行为
 */

/**
 * 更新"已学会"按钮的状态
 */
function updateMarkLearnedButton() {
    const btnElem = document.getElementById('mark-learned-btn');
    if (!btnElem) return;
    
    if (window.ReviewData.currentLearnedState === 0) {
        // 正在学习状态
        btnElem.innerHTML = '<i class="fas fa-check-circle mr-2"></i>标记为已学会';
        btnElem.classList.remove('delete-mode');
        btnElem.onclick = markAsLearned;
    } else {
        // 已学会状态
        btnElem.innerHTML = '<i class="fas fa-trash-alt mr-2"></i>删除';
        btnElem.classList.add('delete-mode');
        btnElem.onclick = deleteItem;
    }
}

/**
 * 标记已学会
 */
function markAsLearned() {
    const checkboxes = document.querySelectorAll('#saved-items-container input[type="checkbox"]:checked');
    if (checkboxes.length === 0) {
        Toastify({
            text: '请至少选择一条内容！',
            duration: 3000,
            gravity: 'top',
            position: 'center',
            backgroundColor: '#ef4444'
        }).showToast();
        return;
    }

    // 获取选中项的ID和索引
    const itemsToUpdate = Array.from(checkboxes).map(checkbox => {
        return {
            id: checkbox.getAttribute('data-id'),
            index: parseInt(checkbox.getAttribute('data-index'))
        };
    });

    // 收集要更新的项的ID
    const itemIds = itemsToUpdate.map(item => item.id);

    // 向后端发送更新请求
    fetch('/api/subtitle/mark-learned', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ itemIds: itemIds })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success' || data.status === 'warning') {
            // 从本地数组中移除（因为已经被标记为学会，不应该在"正在学习"标签里显示）
            window.ReviewData.removeItemsFromLocal(itemsToUpdate);

            window.ReviewRenderer.renderItems();
            window.ReviewRenderer.renderPagination();

            Toastify({
                text: data.message || '已标记为学会！',
                duration: 3000,
                gravity: 'top',
                position: 'center',
                backgroundColor: '#22c55e'
            }).showToast();
        } else {
            Toastify({
                text: data.message || '操作失败，请重试',
                duration: 3000,
                gravity: 'top',
                position: 'center',
                backgroundColor: '#ef4444'
            }).showToast();
        }
    })
    .catch(error => {
        console.error('标记失败:', error);
        Toastify({
            text: '网络请求失败，请重试',
            duration: 3000,
            gravity: 'top',
            position: 'center',
            backgroundColor: '#ef4444'
        }).showToast();
    });
}

/**
 * 删除条目
 */
function deleteItem() {
    const checkboxes = document.querySelectorAll('#saved-items-container input[type="checkbox"]:checked');
    if (checkboxes.length === 0) {
        Toastify({
            text: '请至少选择一条内容！',
            duration: 3000,
            gravity: 'top',
            position: 'center',
            backgroundColor: '#ef4444'
        }).showToast();
        return;
    }

    // 获取选中项的ID和索引
    const itemsToDelete = Array.from(checkboxes).map(checkbox => {
        return {
            id: checkbox.getAttribute('data-id'),
            index: parseInt(checkbox.getAttribute('data-index'))
        };
    });

    // 收集要删除的项的ID
    const itemIds = itemsToDelete.map(item => item.id);

    // 确认对话框
    if (confirm('确定要删除选中的条目吗？此操作不可恢复。')) {
        // 向后端发送删除请求
        fetch('/api/subtitle/delete', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ itemIds: itemIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success' || data.status === 'warning') {
                // 从本地数组中删除
                window.ReviewData.removeItemsFromLocal(itemsToDelete);

                window.ReviewRenderer.renderItems();
                window.ReviewRenderer.renderPagination();

                Toastify({
                    text: data.message || '删除成功！',
                    duration: 3000,
                    gravity: 'top',
                    position: 'center',
                    backgroundColor: '#22c55e'
                }).showToast();
            } else {
                Toastify({
                    text: data.message || '操作失败，请重试',
                    duration: 3000,
                    gravity: 'top',
                    position: 'center',
                    backgroundColor: '#ef4444'
                }).showToast();
            }
        })
        .catch(error => {
            console.error('删除失败:', error);
            Toastify({
                text: '网络请求失败，请重试',
                duration: 3000,
                gravity: 'top',
                position: 'center',
                backgroundColor: '#ef4444'
            }).showToast();
        });
    }
}

// 导出模块接口
window.ReviewActions = {
    updateMarkLearnedButton,
    markAsLearned,
    deleteItem
}; 