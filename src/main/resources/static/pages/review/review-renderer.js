/**
 * 渲染模块
 * 负责处理页面元素的渲染显示
 */

/**
 * 渲染条目列表
 */
function renderItems() {
    const container = document.getElementById('saved-items-container');
    if (!container) {
        console.error('saved-items-container 元素未找到');
        return;
    }
    container.innerHTML = '';

    const itemsToShow = window.ReviewData.getCurrentPageItems();

    if (itemsToShow.length === 0) {
        container.innerHTML = `<p class="text-gray-500 text-center py-4">暂无${window.ReviewData.currentLearnedState === 0 ? '学习中' : '已学会'}的条目</p>`;
        return;
    }

    itemsToShow.forEach((item, index) => {
        const globalIndex = (window.ReviewData.currentPage - 1) * window.ReviewData.itemsPerPage + index;

        const wordText = item.word;
        const detailsText = item.analysis;
        const itemId = item.id;
        const itemType = item.type || 'word'; // 获取type字段，默认为'word'
        const itemLang = item.lang || 'japanese'; // 获取lang字段，默认为'japanese'

        const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899'];
        const randomColor = colors[Math.floor(Math.random() * colors.length)];

        const itemDiv = document.createElement('div');
        itemDiv.className = 'saved-item';

        // 1. Checkbox column
        const checkboxDiv = document.createElement('div');
        checkboxDiv.className = 'saved-item-checkbox';
        checkboxDiv.innerHTML = `
            <input type="checkbox" class="custom-checkbox" id="checkbox-${globalIndex}" style="color: ${randomColor};" data-index="${globalIndex}" data-id="${itemId}">
                <label for="checkbox-${globalIndex}" class="inline-block align-middle"></label>
        `;
        itemDiv.appendChild(checkboxDiv);

        // 2. Word column
        const wordDiv = document.createElement('div');
        wordDiv.className = 'saved-item-column-word';
        wordDiv.textContent = wordText;
        wordDiv.setAttribute('data-type', itemType); // 保存type属性到DOM元素
        wordDiv.setAttribute('data-lang', itemLang); // 保存lang属性到DOM元素
        itemDiv.appendChild(wordDiv);

        // 3. Details column (with cover)
        const detailsCell = document.createElement('div');
        detailsCell.className = 'saved-item-column-details';
        
        // 根据语言和类型选择格式化函数
        const formattedText = window.ReviewFormatter.formatAnalysisTextByLanguage(detailsText, itemLang, itemType);
        detailsCell.innerHTML = formattedText; // 使用innerHTML而不是textContent来渲染HTML

        const coverElement = document.createElement('div');
        coverElement.className = 'details-cover';
        coverElement.textContent = '点击显示';
        coverElement.onclick = function() { 
            this.classList.add('hidden'); 
        }; // 'this' refers to the coverElement
        detailsCell.appendChild(coverElement);
        itemDiv.appendChild(detailsCell);

        // 4. Audio column
        const audioDiv = document.createElement('div');
        audioDiv.className = 'saved-item-column-audio';
        audioDiv.innerHTML = `
                <svg class="audio-icon" onclick="window.ReviewAudio.speakText(event, this)" viewBox="0 0 24 24">
                    <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                </svg>
        `;
        itemDiv.appendChild(audioDiv);

        // 5. Real Context column
        const contextDiv = document.createElement('div');
        contextDiv.className = 'saved-item-column-context';
        contextDiv.innerHTML = `
            <svg class="context-icon" onclick="window.ReviewContext.openContextModal(event, this)" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
            </svg>
        `;
        itemDiv.appendChild(contextDiv);

        // 6. Association column
        const associationDiv = document.createElement('div');
        associationDiv.className = 'saved-item-column-association';
        associationDiv.innerHTML = `
            <svg class="association-icon" onclick="window.ReviewAssociation.openAssociationModal(event, this)" viewBox="0 0 24 24" style="fill: ${randomColor};">
                    <path d="M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7z"/>
                </svg>
        `;
        itemDiv.appendChild(associationDiv);

        container.appendChild(itemDiv);
    });
}

/**
 * 渲染分页控件
 */
function renderPagination() {
    const totalPages = window.ReviewData.getTotalPages();
    const paginationContainer = document.getElementById('pagination');
    if (!paginationContainer) {
        console.error('pagination 元素未找到');
        return;
    }
    paginationContainer.innerHTML = '';

    if (totalPages === 0) {
        return;
    }

    const prevButton = document.createElement('button');
    prevButton.id = 'prev-page';
    prevButton.className = 'pagination-btn bg-indigo-600 text-white hover:bg-indigo-700 disabled:bg-gray-400';
    prevButton.textContent = '上一页';
    prevButton.onclick = () => {
        if (window.ReviewData.currentPage > 1) {
            window.ReviewData.currentPage--;
            renderItems();
            renderPagination();
        }
    };
    prevButton.disabled = window.ReviewData.currentPage === 1;
    paginationContainer.appendChild(prevButton);

    const pageInfo = document.createElement('span');
    pageInfo.id = 'page-info';
    pageInfo.className = 'text-gray-600 px-4 py-2';
    pageInfo.textContent = `${window.ReviewData.currentPage} / ${totalPages}`;
    paginationContainer.appendChild(pageInfo);

    const nextButton = document.createElement('button');
    nextButton.id = 'next-page';
    nextButton.className = 'pagination-btn bg-indigo-600 text-white hover:bg-indigo-700 disabled:bg-gray-400';
    nextButton.textContent = '下一页';
    nextButton.onclick = () => {
        if (window.ReviewData.currentPage < totalPages) {
            window.ReviewData.currentPage++;
            renderItems();
            renderPagination();
        }
    };
    nextButton.disabled = window.ReviewData.currentPage === totalPages;
    paginationContainer.appendChild(nextButton);
}

// 导出模块接口
window.ReviewRenderer = {
    renderItems,
    renderPagination
}; 