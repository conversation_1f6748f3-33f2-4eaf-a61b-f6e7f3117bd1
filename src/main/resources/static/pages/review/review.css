body {
    background: linear-gradient(135deg, #f0f4ff, #e0e7ff, #ede9fe);
    animation: gradient 15s ease infinite;
    background-size: 400% 400%;
    font-family: 'Inter', sans-serif;
    min-height: 100vh;
}

@keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.glass-container {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 1.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
}

.glass-container:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* 自定义Grid布局 */
.review-header-grid {
    display: grid;
    grid-template-columns: 0.5fr 1.5fr 6.5fr 0.6fr 0.8fr 0.6fr;
    align-items: center;
    gap: 1rem; /* Add gap for consistency */
    padding: 0 1rem; /* Add horizontal padding */
}

.saved-item {
    display: grid;
    grid-template-columns: 0.5fr 1.5fr 6.5fr 0.6fr 0.8fr 0.6fr;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.saved-item-checkbox {
    display: flex;
    justify-content: center;
    align-items: center;
}

.saved-item-column-word {
    font-weight: 500;
    color: #374151;
    font-size: 1.1rem;
    text-align: center;
}

.saved-item-column-details {
    color: #4b5563;
    position: relative;
    font-size: 0.95rem;
    text-align: left;
    padding: 0.75rem;
    overflow: hidden;
    line-height: 1.6;
}

/* 改进的格式化文本样式 */
.saved-item-column-details {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
    border-radius: 0.75rem;
    border: 1px solid rgba(226, 232, 240, 0.6);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
}

.saved-item-column-details:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: rgba(79, 70, 229, 0.2);
}

.saved-item-column-details br {
    margin-top: 0.75rem;
    display: block;
    content: "";
}

/* 标记词样式 - 更美观的设计 */
.marker-word {
    display: inline-block;
    font-weight: 700;
    color: #4f46e5;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(99, 102, 241, 0.08));
    padding: 0.25rem 0.6rem;
    border-radius: 0.5rem;
    margin: 0.25rem 0.5rem 0.25rem 0;
    border: 1px solid rgba(79, 70, 229, 0.2);
    font-size: 0.9rem;
    letter-spacing: 0.025em;
    box-shadow: 0 1px 3px rgba(79, 70, 229, 0.1);
    transition: all 0.2s ease;
}

.marker-word:hover {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.15), rgba(99, 102, 241, 0.12));
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(79, 70, 229, 0.15);
}

/* 词性标记样式 - 更突出的设计 */
.part-of-speech {
    display: inline-block;
    color: #8b5cf6;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.12), rgba(168, 85, 247, 0.08));
    font-weight: 700;
    padding: 0.2rem 0.5rem;
    border-radius: 0.4rem;
    margin: 0.2rem 0.4rem 0.2rem 0;
    border: 1px solid rgba(139, 92, 246, 0.25);
    font-size: 0.85rem;
    box-shadow: 0 1px 2px rgba(139, 92, 246, 0.1);
}

/* 例文样式 - 改进的卡片设计 */
.example-text {
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(99, 102, 241, 0.03));
    border: 1px solid rgba(79, 70, 229, 0.15);
    border-left: 4px solid #4f46e5;
    padding: 0.75rem 1rem;
    margin: 0.75rem 0;
    border-radius: 0.5rem;
    font-style: italic;
    font-size: 0.95rem;
    line-height: 1.6;
    position: relative;
    box-shadow: 0 2px 4px rgba(79, 70, 229, 0.05);
}



/* 翻译样式 - 改进的卡片设计 */
.translation-text {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(168, 85, 247, 0.03));
    border: 1px solid rgba(139, 92, 246, 0.15);
    border-left: 4px solid #8b5cf6;
    padding: 0.75rem 1rem;
    margin: 0.75rem 0;
    border-radius: 0.5rem;
    font-size: 0.95rem;
    line-height: 1.6;
    position: relative;
    box-shadow: 0 2px 4px rgba(139, 92, 246, 0.05);
    color: #374151;
}



/* 英语单词头部样式 - 支持音标和词性换行 */
.word-header {
    margin-bottom: 0.75rem;
    padding: 0.5rem 0;
}

.word-main {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
}

.word-header .part-of-speech {
    margin-top: 0.25rem;
    display: inline-block;
}

/* 确保标记词前有适当间距 */
.saved-item-column-details br + .marker-word,
.saved-item-column-details br + .part-of-speech {
    margin-top: 0.5rem;
}

/* 改进文本可读性 */
.saved-item-column-details {
    font-family: 'Inter', 'Hiragino Sans', 'Yu Gothic', 'Meiryo', sans-serif;
    line-height: 1.7;
    letter-spacing: 0.01em;
}

.saved-item-column-audio {
    display: flex;
    justify-content: center;
    align-items: center;
}

.saved-item-column-context {
    display: flex;
    justify-content: center;
    align-items: center;
}

.saved-item-column-association {
    display: flex;
    justify-content: center;
    align-items: center;
}

#saved-items-container {
    /* This targets the main list container, not the modal's */
    padding-bottom: 4rem; /* Ensure content is not hidden by the 'mark learned' button */
}

.audio-icon {
    width: 1.5rem;
    height: 1.5rem;
    cursor: pointer;
    fill: #6b7280; /* 更新默认颜色为深灰色 */
    transition: fill 0.3s ease;
}

.context-icon {
    width: 1.5rem;
    height: 1.5rem;
    cursor: pointer;
    fill: #6b7280; /* 更新默认颜色为深灰色 */
    transition: fill 0.3s ease;
}

.association-icon {
    width: 1.5rem;
    height: 1.5rem;
    cursor: pointer;
    fill: #6b7280; /* 更新默认颜色为深灰色 */
    transition: fill 0.3s ease;
}

.audio-icon:hover, .context-icon:hover, .association-icon:hover {
    fill: #2563eb; /* 更新悬停颜色为更深的蓝色 */
}

.blur-effect {
    position: relative;
    background-color: rgba(255, 255, 255, 0.35);
    backdrop-filter: blur(4px);
    border-radius: inherit;
    user-select: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.7);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    padding: 0.75rem;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;
}

.blur-effect::before {
    content: "点击显示";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #333333;
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0.9;
}

.blur-effect:hover {
    background-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.blur-effect.show {
    backdrop-filter: none;
    background: transparent;
    color: #4b5563;
    border: 1px solid transparent;
    box-shadow: none;
}

.blur-effect.show::before {
    display: none;
}

.custom-checkbox {
    appearance: none;
    width: 1.2rem;
    height: 1.2rem;
    border: 2px solid currentColor;
    border-radius: 4px;
    margin-right: 0.5rem;
    vertical-align: middle;
    cursor: pointer;
    transition: all 0.2s ease;
}

.custom-checkbox:checked {
    background-color: currentColor;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3E%3Cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3E%3C/svg%3E");
    background-size: 0.8rem;
    background-position: center;
    background-repeat: no-repeat;
}

#mark-learned-btn {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    background: linear-gradient(to right, #10b981, #059669);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(16, 185, 129, 0.2);
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
}

#mark-learned-btn:hover {
    background: linear-gradient(to right, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(16, 185, 129, 0.3);
}

.pagination-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.7);
    color: #4b5563;
    transition: all 0.3s ease;
    border: 1px solid rgba(226, 232, 240, 0.7);
    backdrop-filter: blur(4px);
}

.pagination-btn:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(79, 70, 229, 0.15);
    color: #4f46e5;
}

.pagination-btn.active {
    background: linear-gradient(to right, #4f46e5, #6366f1);
    color: white;
    border-color: transparent;
    box-shadow: 0 2px 6px rgba(79, 70, 229, 0.3);
}

#association-modal {
    transition: opacity 0.3s ease;
}

#association-modal .glass-container {
    max-height: 80vh;
    overflow: hidden;
}

#close-modal:hover {
    color: #ef4444;
}

#modal-items-container .saved-item {
    display: grid; /* Changed from flex to grid */
    grid-template-columns: repeat(10, minmax(0, 1fr)); /* Adjusted for 10 columns: 1 (checkbox) + 2 (word) + 7 (details) */
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Ensure modal items also have a checkbox column style if not already covered by general .saved-item-checkbox */
#modal-items-container .saved-item .saved-item-checkbox {
    grid-column: span 1 / span 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

#modal-items-container .saved-item-column-word {
    grid-column: span 2 / span 2; /* Adjusted to span 2 columns */
    font-weight: 500;
    color: #374151;
    font-size: 1.1rem; /* Match main list style */
    text-align: center; /* Match main list style */
}

#modal-items-container .saved-item-column-details {
    grid-column: span 7 / span 7; /* Adjusted to span 7 columns */
    color: #4b5563;
    font-size: 0.95rem; /* Match main list style */
    text-align: left; /* Match main list style */
}

/* 加载动画相关样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;
}

.loading-spinner {
    width: 48px;
    height: 48px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner svg {
    width: 100%;
    height: 100%;
    animation: spin 2s linear infinite;
}

.loading-spinner circle {
    fill: none;
    stroke: url(#spinner-gradient);
    stroke-width: 4;
    stroke-linecap: round;
    stroke-dasharray: 150, 200;
    stroke-dashoffset: -10;
}

@keyframes spin {
    100% { transform: rotate(360deg); }
}

/* New .details-cover style, adapted from dialog.css .chat-cover */
.details-cover {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.35);
    backdrop-filter: blur(4px);
    /* border-radius: inherit; */ /* Inherit won't work well here as parent cell has no radius. Use a fixed one or none. */
    border-radius: 0.5rem; /* A small radius for the cover itself */
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333; /* Color for '点击显示' text */
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 10; /* Ensure it's on top of the text */
    border: 1px solid rgba(255, 255, 255, 0.7);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.details-cover:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

/* Style to hide the cover, applied by JS */
.details-cover.hidden {
    display: none;
}

/* 添加Tab相关样式 */
.tabs-container {
    margin-bottom: 1.5rem;
    position: relative;
}

.tabs {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 1rem;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    color: #64748b;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    margin-right: 0.5rem;
    position: relative;
    z-index: 1;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
    background-color: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(4px);
}

.tab-btn:hover {
    background-color: rgba(255, 255, 255, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.15);
}

.active-tab {
    font-weight: 600;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
}

.tab-btn i {
    margin-right: 0.5rem;
    transition: all 0.3s ease;
}

/* Tab按钮基本样式 */
#tab-learning, #tab-learned {
    color: #64748b; /* 灰色 */
    border-bottom: 2px solid transparent;
}

/* 未激活状态下的图标颜色 */
#tab-learning i {
    color: #64748b; /* 灰色 */
}

#tab-learned i {
    color: #64748b; /* 灰色 */
}

/* 鼠标悬停效果 */
#tab-learning:hover {
    color: #3b82f6; /* 蓝色 */
}

#tab-learning:hover i {
    color: #3b82f6; /* 蓝色 */
}

#tab-learned:hover {
    color: #10b981; /* 绿色 */
}

#tab-learned:hover i {
    color: #10b981; /* 绿色 */
}

/* 激活状态下的图标颜色加深 */
.active-tab i {
    transform: scale(1.1);
}

#tab-learning.active-tab {
    color: #2563eb; /* 深蓝色 */
    border-bottom: 2px solid #2563eb;
}

#tab-learning.active-tab i {
    color: #2563eb; /* 深蓝色 */
}

#tab-learned.active-tab {
    color: #059669; /* 深绿色 */
    border-bottom: 2px solid #059669;
}

#tab-learned.active-tab i {
    color: #059669; /* 深绿色 */
}

#mark-learned-btn.delete-mode {
    background: linear-gradient(to right, #f43f5e, #e11d48);
    box-shadow: 0 4px 6px rgba(244, 63, 94, 0.2);
}

#mark-learned-btn.delete-mode:hover {
    background: linear-gradient(to right, #e11d48, #be123c);
    box-shadow: 0 6px 12px rgba(244, 63, 94, 0.3);
}
