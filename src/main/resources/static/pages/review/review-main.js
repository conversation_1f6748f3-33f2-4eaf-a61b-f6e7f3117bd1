/**
 * 复习页面主控制器
 * 负责应用的初始化和事件绑定
 */

/**
 * 初始化应用
 */
function initializeApp() {
    // 初始渲染
    window.ReviewRenderer.renderItems();
    
    // 绑定模态框事件
    const closeModalBtn = document.getElementById('close-modal');
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', window.ReviewAssociation.closeAssociationModal);
    }

    const collectBtn = document.getElementById('collect-btn');
    if (collectBtn) {
        collectBtn.addEventListener('click', window.ReviewAssociation.collectSelectedItems);
    }
    
    // 绑定Tab切换事件
    const tabLearning = document.getElementById('tab-learning');
    const tabLearned = document.getElementById('tab-learned');
    
    if (tabLearning) {
        tabLearning.addEventListener('click', () => window.ReviewData.switchTab('tab-learning'));
    }
    
    if (tabLearned) {
        tabLearned.addEventListener('click', () => window.ReviewData.switchTab('tab-learned'));
    }
}

// 页面加载完成后初始化
window.onload = initializeApp;

// DOM内容加载完成后加载数据
document.addEventListener('DOMContentLoaded', window.ReviewData.loadSavedItems); 