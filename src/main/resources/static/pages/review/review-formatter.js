/**
 * 文本格式化模块
 * 负责处理分析文本的格式化显示
 */

// 修复后的格式化解析文本函数
function formatAnalysisText(text) {
    // 寻找常见的段落标记
    const markers = [
        '语法分析:', '含义:', '例文:', '中文翻译:', '注意:'
    ];
    
    let formattedText = text;
    
    // 为每个标记添加换行和样式，使用更精确的匹配
    markers.forEach(marker => {
        // 转义特殊字符并匹配完整的标记词
        const escapedMarker = marker.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`(${escapedMarker})`, 'gi');
        formattedText = formattedText.replace(regex, '<br><span class="marker-word">$1</span>');
    });
    
    // 移除第一个<br>（如果存在）
    if (formattedText.startsWith('<br>')) {
        formattedText = formattedText.substring(4);
    }
    
    // 处理【名词】、【动词】等词性标记，让它们更显眼
    formattedText = formattedText.replace(/【(.+?)】/g, '<strong class="part-of-speech">【$1】</strong>');
    
    // 给例文和中文翻译添加特殊样式
    // 先找到例文标记之后的内容
    const exampleMatch = formattedText.match(/<span class="marker-word">例文:.*?<\/span>(.*?)(?:<br>|$)/);
    if (exampleMatch && exampleMatch[1]) {
        const exampleText = exampleMatch[1].trim();
        formattedText = formattedText.replace(exampleMatch[1], `<div class="example-text">${exampleText}</div>`);
    }
    
    // 再找到中文翻译标记之后的内容
    const translationMatch = formattedText.match(/<span class="marker-word">中文翻译:.*?<\/span>(.*?)(?:<br>|$)/);
    if (translationMatch && translationMatch[1]) {
        const translationText = translationMatch[1].trim();
        formattedText = formattedText.replace(translationMatch[1], `<div class="translation-text">${translationText}</div>`);
    }
    
    // 处理翻译标记（不带"中文"二字的情况）
    const simpleTranslationMatch = formattedText.match(/<span class="marker-word">翻译:.*?<\/span>(.*?)(?:<br>|$)/);
    if (simpleTranslationMatch && simpleTranslationMatch[1]) {
        const translationText = simpleTranslationMatch[1].trim();
        formattedText = formattedText.replace(simpleTranslationMatch[1], `<div class="translation-text">${translationText}</div>`);
    }
    
    // 返回格式化后的HTML
    return formattedText;
}

/**
 * 格式化英语单词分析文本
 * 针对英语单词的特殊格式要求
 */
function formatEnglishWordText(text) {
    // 英语单词的标记词
    const markers = [
        '含义:', '例文:', '中文翻译:', '词根词缀:', '同义词:', '反义词:'
    ];
    
    let formattedText = text;
    
    // 处理单词和音标以及词性 - 保持在同一行，词性用纯文本
    // 匹配格式：write /raɪt/:【动词】
    formattedText = formattedText.replace(/([a-zA-Z\s]+)\s*\/([^\/]+)\/\s*:\s*【(.+?)】/g, 
        '$1 /$2/:【$3】');
    
    // 为每个标记添加换行和样式
    markers.forEach(marker => {
        const escapedMarker = marker.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`(${escapedMarker})`, 'gi');
        formattedText = formattedText.replace(regex, '<br><span class="marker-word">$1</span>');
    });
    
    // 移除第一个<br>（如果存在）
    if (formattedText.startsWith('<br>')) {
        formattedText = formattedText.substring(4);
    }
    
    // 给例文添加特殊样式
    const exampleMatch = formattedText.match(/<span class="marker-word">例文:.*?<\/span>(.*?)(?:<br>|$)/);
    if (exampleMatch && exampleMatch[1]) {
        const exampleText = exampleMatch[1].trim();
        formattedText = formattedText.replace(exampleMatch[1], `<div class="example-text">${exampleText}</div>`);
    }
    
    // 给中文翻译添加特殊样式
    const translationMatch = formattedText.match(/<span class="marker-word">中文翻译:.*?<\/span>(.*?)(?:<br>|$)/);
    if (translationMatch && translationMatch[1]) {
        const translationText = translationMatch[1].trim();
        formattedText = formattedText.replace(translationMatch[1], `<div class="translation-text">${translationText}</div>`);
    }
    
    return formattedText;
}

/**
 * 格式化英语短语分析文本
 * 针对英语短语的特殊格式要求
 */
function formatEnglishPhraseText(text) {
    // 英语短语的标记词
    const markers = [
        '含义:', '例文:', '中文翻译:'
    ];
    
    let formattedText = text;
    
    // 为每个标记添加换行和样式
    markers.forEach(marker => {
        const escapedMarker = marker.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');  
        const regex = new RegExp(`(${escapedMarker})`, 'gi');
        formattedText = formattedText.replace(regex, '<br><span class="marker-word">$1</span>');
    });
    
    // 移除第一个<br>（如果存在）
    if (formattedText.startsWith('<br>')) {
        formattedText = formattedText.substring(4);
    }
    
    // 给例句添加特殊样式
    const exampleMatch = formattedText.match(/<span class="marker-word">例文:.*?<\/span>(.*?)(?:<br>|$)/);
    if (exampleMatch && exampleMatch[1]) {
        const exampleText = exampleMatch[1].trim();
        formattedText = formattedText.replace(exampleMatch[1], `<div class="example-text">${exampleText}</div>`);
    }
    
    // 给中文翻译添加特殊样式
    const translationMatch = formattedText.match(/<span class="marker-word">中文翻译:.*?<\/span>(.*?)(?:<br>|$)/);
    if (translationMatch && translationMatch[1]) {
        const translationText = translationMatch[1].trim();
        formattedText = formattedText.replace(translationMatch[1], `<div class="translation-text">${translationText}</div>`);
    }
    
    return formattedText;
}

/**
 * 根据语言和类型选择合适的格式化函数
 */
function formatAnalysisTextByLanguage(text, language, type) {
    if (language === 'english') {
        if (type === 'phrase') {
            return formatEnglishPhraseText(text);
        } else {
            return formatEnglishWordText(text);
        }
    } else {
        // 日语使用原有的格式化方法
        return formatAnalysisText(text);
    }
}

// 导出函数供其他模块使用
window.ReviewFormatter = {
    formatAnalysisText,
    formatEnglishWordText,
    formatEnglishPhraseText,
    formatAnalysisTextByLanguage
}; 