/**
 * 权限检查模块 (带缓存优化)
 * 负责处理用户权限校验和升级提示
 */

// 权限缓存，用于存储订阅信息，减少网络请求
const permissionCache = {
    subscriptionInfo: null,
    lastCheckTime: 0,
    cacheValidDuration: 5 * 60 * 1000 // 缓存有效期5分钟
};

/**
 * 获取用户订阅信息（带缓存）
 * @returns {Promise<object|null>} 用户订阅信息或在失败时返回null
 */
async function getSubscriptionInfoWithCache() {
    const now = Date.now();
    
    // 如果缓存有效，直接返回缓存数据
    if (permissionCache.lastCheckTime && (now - permissionCache.lastCheckTime) < permissionCache.cacheValidDuration) {
        console.log('使用缓存的订阅信息');
        return permissionCache.subscriptionInfo;
    }

    console.log('权限缓存已过期或不存在，重新获取订阅信息');
    
    try {
        // 复用现有的订阅状态接口
        const response = await fetch('/api/subscription/active', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        });

        if (!response.ok) {
            throw new Error(`网络响应异常: ${response.status}`);
        }
        
        const data = await response.json();
        
        // 更新缓存
        permissionCache.subscriptionInfo = data;
        permissionCache.lastCheckTime = now;
        
        return data;
    } catch (error) {
        console.error('获取订阅信息失败:', error);
        // 请求失败时，不清空旧缓存，但也不更新时间戳，以便下次能快速重试
        return null; // 返回null表示获取失败
    }
}

/**
 * 检查Premium会员权限的通用函数
 */
async function checkPremiumPermission() {
    const data = await getSubscriptionInfoWithCache();
    
    // 如果获取订阅信息失败
    if (!data) {
        return { success: false, isPremium: false };
    }
    
    // 检查是否有活跃订阅且为Premium会员
    const hasActiveSubscription = data.hasActiveSubscription || false;
    let isPremium = false;
    
    if (hasActiveSubscription && data.subscription) {
        const subscription = data.subscription;
        const planName = subscription.planName || '';
        const planId = subscription.planId || '';
        
        // 检查是否为Premium会员（包括试用版）
        isPremium = (planName.toLowerCase().includes('premium')) ||
                   (planId.toLowerCase().includes('premium')) ||
                   (planId.toLowerCase().includes('trial_premium'));
    }
    
    return { success: true, isPremium };
}

/**
 * 检查Plus或Premium会员权限的通用函数
 */
async function checkPlusOrPremiumPermission() {
    const data = await getSubscriptionInfoWithCache();
    
    // 如果获取订阅信息失败
    if (!data) {
        return { success: false, isPlusOrPremium: false };
    }
    
    // 检查是否有活跃订阅且为Plus或Premium会员
    const hasActiveSubscription = data.hasActiveSubscription || false;
    let isPlusOrPremium = false;
    
    if (hasActiveSubscription && data.subscription) {
        const subscription = data.subscription;
        const planName = subscription.planName || '';
        const planId = subscription.planId || '';
        
        // 检查是否为Plus会员或Premium会员（包括试用版）
        isPlusOrPremium = (planName.toLowerCase().includes('plus')) ||
                         (planName.toLowerCase().includes('premium')) ||
                         (planId.toLowerCase().includes('plus')) ||
                         (planId.toLowerCase().includes('premium')) ||
                         (planId.toLowerCase().includes('trial_premium'));
    }
    
    return { success: true, isPlusOrPremium };
}

/**
 * 显示升级提示对话框
 */
function showUpgradeDialog(featureName, requiredMembership = 'Premium') {
  // 根据所需会员等级确定标题和文案
  const isPlus = requiredMembership === 'Plus';
  const title = isPlus ? '升级以解锁 Plus 功能' : '升级以解锁 Premium 功能';
  const description = isPlus ? 
    `<strong>${featureName}</strong> 需要 Plus 或 Premium 会员资格` :
    `<strong>${featureName}</strong> 是 Premium 会员专属功能`;
  
  // 创建升级提示对话框
  const dialog = document.createElement('div');
  dialog.className = 'premium-upgrade-dialog';
  dialog.innerHTML = `
    <div class="dialog-overlay"></div>
    <div class="dialog-content">
      <div class="dialog-header">
        <h3>${title}</h3>
        <button class="dialog-close">&times;</button>
      </div>
      <div class="dialog-body">
        <div class="premium-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="#FFD700" stroke="#FFB000" stroke-width="2"/>
          </svg>
        </div>
        <p class="feature-text">
          ${description}
        </p>
      </div>
      <div class="dialog-footer">
        <button class="btn-upgrade">立即升级</button>
      </div>
    </div>
  `;
  
  // 添加样式
  if (!document.getElementById('premium-dialog-styles')) {
    const styles = document.createElement('style');
    styles.id = 'premium-dialog-styles';
    styles.textContent = `
      .premium-upgrade-dialog {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2147483647;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .dialog-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(4px);
      }
      
      .dialog-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        max-width: 480px;
        width: 90%;
        max-height: 80vh;
        overflow: hidden;
        animation: dialogAppear 0.3s ease-out;
      }
      
      @keyframes dialogAppear {
        from {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.9);
        }
        to {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
      }
      
      .dialog-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 24px 16px;
        border-bottom: 1px solid #e5e7eb;
      }
      
      .dialog-header h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #111827;
      }
      
      .dialog-close {
        background: none;
        border: none;
        font-size: 24px;
        color: #6b7280;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s;
      }
      
      .dialog-close:hover {
        background: #f3f4f6;
        color: #374151;
      }
      
      .dialog-body {
        padding: 24px;
        text-align: center;
      }
      
      .premium-icon {
        margin-bottom: 16px;
      }
      
      .feature-text {
        font-size: 16px;
        color: #374151;
        margin-bottom: 24px;
      }
      
      .dialog-footer {
        display: flex;
        gap: 12px;
        padding: 16px 24px 24px;
        justify-content: center;
      }
      
      .btn-upgrade {
        padding: 10px 20px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        border: none;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }
      
      .btn-upgrade:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
        transform: translateY(-1px);
      }
    `;
    document.head.appendChild(styles);
  }
  
  // 添加到页面
  document.body.appendChild(dialog);
  
  // 绑定事件
  const closeBtn = dialog.querySelector('.dialog-close');
  const upgradeBtn = dialog.querySelector('.btn-upgrade');
  const overlay = dialog.querySelector('.dialog-overlay');
  
  function closeDialog() {
    dialog.remove();
  }
  
  closeBtn.addEventListener('click', closeDialog);
  overlay.addEventListener('click', closeDialog);
  
  upgradeBtn.addEventListener('click', function() {
    // 打开升级页面
    window.open('http://localhost:8080/index.html#pricing', '_blank');
    closeDialog();
  });
  
  // ESC键关闭
  const handleKeydown = (e) => {
    if (e.key === 'Escape') {
      closeDialog();
      document.removeEventListener('keydown', handleKeydown);
    }
  };
  document.addEventListener('keydown', handleKeydown);
}

// 导出模块接口
window.ReviewPermissions = {
  checkPremiumPermission,
  checkPlusOrPremiumPermission,
  showUpgradeDialog
}; 