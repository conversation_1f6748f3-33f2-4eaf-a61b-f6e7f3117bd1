<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>lingtube.net</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/odometer@0.4.8/themes/odometer-theme-default.css">
    <style>
        body {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        #particles-js {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        .card {
            background: linear-gradient(135deg, rgba(45, 45, 45, 0.7), rgba(26, 26, 26, 0.7));
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3), 0 0 20px rgba(59, 130, 246, 0.3);
            padding: 20px;
            margin: 20px;
            width: 500px;
            text-align: center;
            animation: fadeIn 1.5s ease-in-out;
        }
        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        .label {
            font-size: 2.5rem;
            font-weight: bold;
            color: #ffffff;
            text-shadow: 0 0 10px rgba(139, 92, 246, 0.8);
            margin-bottom: 15px;
        }
        .odometer {
            font-family: 'Roboto Mono', monospace;
            font-size: 3.5rem;
            color: #FFD700;
            text-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
            line-height: 1;
            animation: glow 2s ease-in-out infinite alternate;
        }
        @keyframes glow {
            from {
                text-shadow: 0 0 10px rgba(255, 215, 0, 0.7), 0 0 20px rgba(255, 215, 0, 0.5);
            }
            to {
                text-shadow: 0 0 20px rgba(255, 215, 0, 0.9), 0 0 30px rgba(255, 215, 0, 0.7);
            }
        }
    </style>
</head>
<body>
<!-- 粒子动画背景 -->
<div id="particles-js"></div>

<!-- 标题 -->
<div class="text-center mb-10">
    <div class="text-white text-5xl font-bold">
        <i class="fas fa-language mr-3"></i>lingtube.net
    </div>
    <p class="text-white text-xl mt-4">用 AI 赋能语言学习</p>
</div>

<!-- 数据卡片 -->
<div class="flex justify-center gap-10">
    <!-- 用户注册总数卡片 -->
    <div class="card">
        <div class="label">用户注册总数</div>
        <div id="register-odometer" class="odometer">0</div>
    </div>

    <!-- 当前用户登录数卡片 -->
    <div class="card">
        <div class="label">当前用户登录数</div>
        <div id="login-odometer" class="odometer">0</div>
    </div>
</div>

<!-- 底部宣传 -->
<footer class="text-center text-white text-lg mt-10">
    © 2025 lingtube.net
</footer>

<script src="https://cdn.jsdelivr.net/npm/odometer@0.4.8/odometer.min.js"></script>
<script>
    // 初始化粒子动画
    particlesJS('particles-js', {
        particles: {
            number: { value: 100, density: { enable: true, value_area: 800 } },
            color: { value: '#ffffff' },
            shape: { type: 'circle' },
            opacity: { value: 0.5, random: true },
            size: { value: 3, random: true },
            line_linked: { enable: true, distance: 150, color: '#ffffff', opacity: 0.4, width: 1 },
            move: { enable: true, speed: 2, direction: 'none', random: false }
        },
        interactivity: {
            detect_on: 'canvas',
            events: { onhover: { enable: false }, onclick: { enable: false } },
            modes: { repulse: { distance: 100, duration: 0.4 }, push: { particles_nb: 4 } }
        },
        retina_detect: true
    });

    // 初始化 Odometer
    let registerCount = 12500;
    let loginCount = 8500;

    const registerOdometer = new Odometer({
        el: document.getElementById('register-odometer'),
        value: registerCount,
        format: '(,ddd)',
        theme: 'default'
    });

    const loginOdometer = new Odometer({
        el: document.getElementById('login-odometer'),
        value: loginCount,
        format: '(,ddd)',
        theme: 'default'
    });

    // 模拟数据更新
    function updateNumbers() {
        // 模拟数据增长
        registerCount += Math.floor(Math.random() * 50);
        loginCount += Math.floor(Math.random() * 30);

        // 更新 Odometer
        registerOdometer.update(registerCount);
        loginOdometer.update(loginCount);
    }

    // 每 5 秒更新一次
    setInterval(updateNumbers, 5000);
</script>
</body>
</html>