<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文本转语音</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        .container {
            max-width: 800px;
            margin-top: 50px;
        }
        .tts-card {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            padding: 20px;
        }
        .speak-btn {
            cursor: pointer;
            font-size: 1.5rem;
            color: #0d6efd;
            transition: color 0.3s;
        }
        .speak-btn:hover {
            color: #0a58ca;
        }
        .input-group-text {
            background-color: transparent;
            border-left: none;
        }
        textarea {
            resize: none;
            height: 150px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="tts-card">
            <h2 class="mb-4 text-center">文本转语音</h2>
            
            <div class="mb-3">
                <label for="textInput" class="form-label">输入文本</label>
                <div class="input-group">
                    <textarea class="form-control" id="textInput" rows="4" placeholder="请输入要转换为语音的文本..."></textarea>
                    <span class="input-group-text border-start-0">
                        <i class="bi bi-megaphone-fill speak-btn" id="speakButton" title="朗读文本"></i>
                    </span>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="voiceSelect" class="form-label">选择语音</label>
                <select class="form-select" id="voiceSelect">
                    <option value="zh-CN-XiaoxiaoNeural">小筱 (女声)</option>
                    <option value="zh-CN-XiaomoNeural">小墨 (女声)</option>
                    <option value="zh-CN-XiaoxuanNeural">小璇 (女声)</option>
                    <option value="zh-CN-YunjianNeural">云健 (男声)</option>
                    <option value="zh-CN-YunxiNeural">云希 (男声)</option>
                    <option value="zh-CN-YunyangNeural">云扬 (男声)</option>
                </select>
            </div>
            
            <div class="d-flex justify-content-between mt-4">
                <button class="btn btn-secondary" id="clearButton">清空文本</button>
                <div>
                    <button class="btn btn-primary" id="speakButtonLarge">
                        <i class="bi bi-megaphone-fill me-2"></i>朗读文本
                    </button>
                </div>
            </div>
            
            <div class="mt-4" id="statusMessage"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/pages/review/text-to-speech.js"></script>
</body>
</html> 