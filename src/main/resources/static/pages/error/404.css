body {
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
    min-height: 100vh;
}

@keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

#particles-js {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.glass-container {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 1rem;
    box-shadow: 0 15px 25px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    max-width: 400px;
    margin: 0 auto;
    padding: 2rem;
}