// 支付成功页面脚本
document.addEventListener('DOMContentLoaded', function() {
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    
    // 获取支付详情参数
    const planName = urlParams.get('plan') || 'Plus';
    const period = urlParams.get('period') || '月付';
    const amount = urlParams.get('amount') || '¥48';
    
    // 更新页面显示
    document.getElementById('plan-name').textContent = planName;
    document.getElementById('plan-period').textContent = period;
    document.getElementById('plan-amount').textContent = amount;
    
    // 添加页面加载动画（可选）
    setTimeout(function() {
        const confetti = document.createElement('script');
        confetti.src = 'https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js';
        confetti.onload = function() {
            window.confetti({
                particleCount: 100,
                spread: 70,
                origin: { y: 0.6 }
            });
        };
        document.head.appendChild(confetti);
    }, 500);
}); 