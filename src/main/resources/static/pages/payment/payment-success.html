<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付成功</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="plan.css">
    <link rel="icon" type="image/png" href="/favicon.png">
    <!-- Canvas Confetti库用于彩带效果 -->
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>
</head>
<body class="min-h-screen flex flex-col bg-[#f3f3ff]">
<!-- Navigation Bar -->
<div id="navbar-container"></div>
<script src="/components/navbar/navbar.js"></script>

<main class="flex-1 container mx-auto p-6 flex flex-col items-center justify-center">
    <div class="bg-white rounded-2xl p-12 shadow-md max-w-lg w-full text-center">
        <div class="w-20 h-20 bg-green-100 rounded-full mx-auto flex items-center justify-center mb-6">
            <i class="fas fa-check text-green-500 text-4xl"></i>
        </div>
        
        <h1 class="text-3xl font-bold text-gray-800 mb-4">支付成功</h1>
        <p class="text-gray-600 mb-8">感谢您的订阅！您的账户已成功升级，请刷新 YouTube 和网站页面以同步更新。</p>
        
        <div class="flex flex-col space-y-4">
            <a href="/pages/profile/profile.html" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-full font-medium shadow-md hover:shadow-lg transition duration-300">
                进入个人主页
            </a>
            <a href="/" class="text-blue-600 hover:text-blue-700 font-medium transition duration-200">
                返回首页
            </a>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 立即显示第一次彩带效果 - 更大、更多彩带
    confetti({
        particleCount: 150,
        spread: 100,
        origin: { y: 0.6 },
        scalar: 1.5 // 使彩带更大
    });
    
    // 1秒后显示第二次彩带效果 - 从两侧发射，形成交叉效果
    setTimeout(function() {
        // 从左侧发射
        confetti({
            particleCount: 250,
            angle: 60,
            spread: 70,
            origin: { x: 0 },
            scalar: 1.5
        });
        
        // 立即从右侧发射，不再有额外延迟
        confetti({
            particleCount: 250,
            angle: 120,
            spread: 70,
            origin: { x: 1 },
            scalar: 1.5
        });
    }, 1000); // 缩短为1秒
});
</script>
</body>
</html> 