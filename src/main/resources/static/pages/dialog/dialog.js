/**
 * 对话页面主文件 - 负责初始化和协调各个功能模块
 */

// 全局管理器实例
let storageManager;
let favoriteManager;
let tooltipManager;
let ttsManager;
let analysisManager;
let dialogManager;

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // 初始化各个管理器
        await initializeManagers();
        
        // 初始化页面功能
        initializePageFeatures();
        
        // 设置解析面板标题
        const titleElement = document.getElementById('analysis-title');
        if (titleElement) {
            titleElement.textContent = 'AI语法解析结果';
        }
        
        console.log('对话页面初始化完成');
    } catch (error) {
        console.error('页面初始化失败:', error);
        UIUtils.showToast('页面初始化失败，请刷新重试', 'error');
    }
});

/**
 * 初始化各个管理器
 */
async function initializeManagers() {
    // 按依赖顺序初始化管理器
    storageManager = new StorageManager();
    favoriteManager = new FavoriteManager(storageManager);
    tooltipManager = new TooltipManager(favoriteManager);
    ttsManager = new TTSManager();
    analysisManager = new AnalysisManager();
    dialogManager = new DialogManager(tooltipManager, ttsManager, analysisManager);
    
    // 初始化需要异步操作的管理器
    await favoriteManager.init();
    tooltipManager.init();
}

/**
 * 初始化页面功能
 */
function initializePageFeatures() {
    // 初始化推荐场景卡片
    UIUtils.initScenarioCards();
    
    // 初始化搜索功能
    dialogManager.initSearchFunction();
}

/**
 * 向后兼容的全局函数 - 保持原有API不变
 */

// 生成对话 - 向后兼容
function generateDialog(scenario) {
    if (dialogManager) {
        dialogManager.generateDialog(scenario);
    } else {
        console.error('DialogManager未初始化');
    }
}

// 分析对话 - 向后兼容
function analyzeDialog(text) {
    if (analysisManager) {
        analysisManager.analyzeDialog(text);
    } else {
        console.error('AnalysisManager未初始化');
    }
}

// 切换分析面板 - 向后兼容
function toggleAnalysisPanel() {
    UIUtils.toggleAnalysisPanel();
}

// 文本朗读 - 向后兼容
function speakText(text, event, audioElement) {
    if (ttsManager) {
        ttsManager.speakText(text, event, audioElement);
    } else {
        console.error('TTSManager未初始化');
    }
}

// 显示toast - 向后兼容
function showToast(message, type = 'info') {
    UIUtils.showToast(message, type);
}

// 保存到本地存储 - 向后兼容
function saveToLocalStorage() {
    if (favoriteManager && storageManager) {
        storageManager.saveFavorites(favoriteManager.favoritedWords);
    } else {
        console.error('StorageManager或FavoriteManager未初始化');
    }
}

// 从本地存储加载 - 向后兼容
function loadFromLocalStorage() {
    if (storageManager) {
        return storageManager.loadFavorites();
    } else {
        console.error('StorageManager未初始化');
        return new Set();
    }
}

// 从服务器加载收藏 - 向后兼容
function loadFavoritesFromServer() {
    if (favoriteManager) {
        // 这个函数在init中已经调用，这里只是为了向后兼容
        console.log('收藏已在初始化时从服务器加载');
    } else {
        console.error('FavoriteManager未初始化');
    }
}

// 导出全局变量以便其他脚本访问（如果需要）
window.dialogApp = {
    storageManager,
    favoriteManager,
    tooltipManager,
    ttsManager,
    analysisManager,
    dialogManager,
    
    // 向后兼容的函数
    generateDialog,
    analyzeDialog,
    toggleAnalysisPanel,
    speakText,
    showToast,
    saveToLocalStorage,
    loadFromLocalStorage,
    loadFavoritesFromServer
};

// analyzeText函数现在委托给AnalysisManager处理
function analyzeText(row) {
    const text = row.querySelector('.subtitle-text').textContent;
    if (window.dialogApp && window.dialogApp.analysisManager) {
        window.dialogApp.analysisManager.analyzeDialog(text);
    } else {
        console.error('AnalysisManager未初始化或无法访问');
        // 可以提供一个备用方案或显示错误信息
        UIUtils.showToast('分析功能不可用', 'error');
    }
}

/**
 * 检查分析面板是否打开
 * @returns {boolean}
 */
function isAnalysisPanelOpen() {
    const analysisPanel = document.getElementById('analysis-panel');
    return analysisPanel && analysisPanel.classList.contains('open');
} 