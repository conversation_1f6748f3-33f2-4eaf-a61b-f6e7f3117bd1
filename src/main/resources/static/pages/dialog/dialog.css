body {
    font-family: 'Inter', sans-serif;
}

.glass-container {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 1.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

/* 推荐场景卡片样式 */
.scenario-card {
    display: flex;
    padding: 1.25rem;
    border-radius: 1rem;
    background: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(229, 231, 235, 0.7);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.scenario-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
}

.scenario-card:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.scenario-card:hover:after {
    opacity: 1;
}

.scenario-card .icon-container {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.scenario-card .icon-container i {
    font-size: 1.5rem;
}

.scenario-card .text-container {
    flex: 1;
}

.scenario-card .text-container h3 {
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.scenario-card .text-container p {
    line-height: 1.4;
}

#recommended-scenarios h2 {
    margin-bottom: 1rem;
}

#analysis-container {
    overflow-y: auto;
    scrollbar-width: thin;
    flex: 1;
}

#analysis-container::-webkit-scrollbar {
    width: 6px;
}

#analysis-container::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 3px;
}

#analysis-container::-webkit-scrollbar-thumb:hover {
    background: #5a626e;
}

.analysis-item {
    opacity: 0;
    animation: fadeIn 0.5s forwards;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0.75rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

@keyframes fadeIn {
    to { opacity: 1; }
}

.markdown-content {
    @apply prose prose-sm text-gray-700;
}

.markdown-content h1, .markdown-content h2, .markdown-content h3 {
    @apply text-gray-800 font-semibold;
}

.markdown-content p {
    @apply mt-1;
}

.markdown-content ul, .markdown-content ol {
    @apply ml-4;
}

.markdown-content ruby {
    ruby-align: center;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    vertical-align: middle;
}

.markdown-content rt {
    font-size: 0.6em;
    line-height: 1.2;
    color: #4b5563;
    text-align: center;
}

@media (max-width: 640px) {
    .markdown-content rt {
        font-size: 0.5em;
    }
}

/* 词性标注颜色 */
.pos-noun {
    background-image: linear-gradient(to top, #fefcbf 50%, transparent 50%);
    padding: 0.1rem 0.3rem;
}

.pos-verb {
    background-image: linear-gradient(to top, #c6f6d5 50%, transparent 50%);
    padding: 0.1rem 0.3rem;
}

.pos-adjective {
    background-image: linear-gradient(to top, #bee3f8 50%, transparent 50%);
    padding: 0.1rem 0.3rem;
}

.pos-adverb {
    background-image: linear-gradient(to top, #fed7e2 50%, transparent 50%);
    padding: 0.1rem 0.3rem;
}

.pos-particle {
    background-image: linear-gradient(to top, #e9d8fd 50%, transparent 50%);
    padding: 0.1rem 0.3rem;
}

.pos-pron {
    background-image: linear-gradient(to top, #ffcccb 50%, transparent 50%);
    padding: 0.1rem 0.3rem;
}

.pos-aux {
    background-image: linear-gradient(to top, #d1fae5 50%, transparent 50%);
    padding: 0.1rem 0.3rem;
}

.pos-conj {
    background-image: linear-gradient(to top, #fef3c7 50%, transparent 50%);
    padding: 0.1rem 0.3rem;
}

.pos-other {
    background-image: linear-gradient(to top, #d3d3d3 50%, transparent 50%);
    padding: 0.1rem 0.3rem;
}

.pos-interjection {
    background-image: linear-gradient(to top, #ffaa66 50%, transparent 50%);
    padding: 0.1rem 0.3rem;
}

.pos-punctuation {
    padding: 0.1rem 0.3rem;
}

/* 词组悬停便签样式 */
#word-tooltip {
    min-width: 120px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border-radius: 8px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

#favorite-icon {
    transition: color 0.2s ease;
}

#favorite-icon.fas.text-red-500 {
    animation: heartBeat 0.3s ease-in-out;
}

@keyframes heartBeat {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

/* 加载动画 */
.loading-spinner {
    width: 48px;
    height: 48px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner svg {
    width: 100%;
    height: 100%;
    animation: spin 2s linear infinite;
}

.loading-spinner circle {
    fill: none;
    stroke: url(#spinner-gradient);
    stroke-width: 4;
    stroke-linecap: round;
    stroke-dasharray: 150, 200;
    stroke-dashoffset: -10;
}

@keyframes spin {
    100% { transform: rotate(360deg); }
}

/* 对话气泡样式 */
.chat-bubble {
    position: relative;
    margin-bottom: 20px;
    max-width: 80%;
    clear: both;
}

.chat-bubble.left {
    float: left;
    background-color: #f0f0f0;
    border-radius: 16px 16px 16px 0;
    padding: 12px 16px;
}

.chat-bubble.right {
    float: right;
    background-color: #ecfdf5;
    border-radius: 16px 16px 0 16px;
    padding: 12px 16px;
}

.chat-bubble:after {
    content: '';
    clear: both;
    display: table;
}

.chat-wrapper {
    position: relative;
    margin-bottom: 20px;
    width: 100%;
    clear: both;
    overflow: hidden;
}

.chat-wrapper.left-wrapper {
    display: flex;
    justify-content: flex-start;
}

.chat-wrapper.right-wrapper {
    display: flex;
    justify-content: flex-end;
}

.chat-content {
    display: flex;
    flex-direction: column;
    margin: 5px 0;
}

.chat-japanese {
    font-size: 1.1rem;
    font-weight: 500;
    color: #1f2937;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.chat-english {
    font-size: 1.1rem;
    font-weight: 500;
    color: #1f2937;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.chat-kana {
    font-size: 0.8rem;
    color: #6b7280;
    margin-bottom: 4px;
}

.chat-chinese {
    font-size: 0.95rem;
    color: #374151;
}

.analysis-btn {
    position: absolute;
    bottom: 5px;
    right: 5px;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f3f4f6;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.analysis-btn:hover {
    background-color: #e5e7eb;
}

.analysis-btn.active {
    background-color: #10b981;
    color: white;
}

/* 修改收藏按钮颜色 */
.save-btn {
    background: linear-gradient(to right, #2563eb, #3b82f6);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.save-btn:hover {
    background: linear-gradient(to right, #1d4ed8, #2563eb);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

/* 动态布局样式 */
#main-container.is-analysis-open {
    @apply w-3/4 mx-auto; /* 左侧内容占3/4 */
}

#main-container.is-analysis-open #analysis-panel {
    transform: translateX(0) !important;
}

#main-container.is-analysis-open #panel-toggle i {
    @apply fas fa-chevron-right;
}

.custom-checkbox {
    appearance: none;
    width: 1.2rem;
    height: 1.2rem;
    border: 2px solid currentColor;
    border-radius: 4px;
    margin-right: 0.5rem;
    vertical-align: middle;
    cursor: pointer;
    transition: all 0.2s ease;
}

.custom-checkbox:checked {
    background-color: currentColor;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3E%3Cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3E%3C/svg%3E");
    background-size: 0.8rem;
    background-position: center;
    background-repeat: no-repeat;
}

/* 朗读按钮样式 */
.speak-btn {
    position: absolute;
    bottom: 5px;
    right: 35px; /* 位于分析按钮左侧 */
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f3f4f6;
    border-radius: 50%;
    transition: all 0.2s ease;
    color: #4b5563;
}

.speak-btn:hover {
    background-color: #3b82f6;
    color: white;
}

.speak-btn.speaking {
    animation: pulse 1.5s infinite;
    background-color: #3b82f6;
    color: white;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 修改蒙版样式 */
.chat-cover {
    position: absolute;
    top: 0;
    bottom: auto;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.35);
    backdrop-filter: blur(4px);
    border-radius: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    height: auto;
    border: 1px solid rgba(255, 255, 255, 0.7);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.chat-cover:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

/* 删除原有的显示/隐藏逻辑 */
.chat-bubble.right:hover .chat-cover {
    opacity: 1;
}

.reveal-btn {
    display: none; /* 隐藏解锁按钮 */
}

ruby > span {
    background: inherit;
}

/* 波纹效果 */
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.7);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
    width: 120px;
    height: 120px;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
} 