<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>场景AI对话</title>
  <!-- 立即加载暗色模式脚本，防止页面闪烁 -->
  <script src="/shared/utils/dark-mode.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  <script src="https://cdn.jsdelivr.net/npm/marked@4.0.0/marked.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/dompurify@2.3.10/dist/purify.min.js"></script>
  <link href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="dialog.css">
  <link rel="icon" type="image/png" href="/favicon.png">
  <link rel="stylesheet" href="/assets/css/dark-mode.css">
</head>
<body class="min-h-screen flex flex-col bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50">
<!-- Navigation Bar -->
<div id="navbar-container"></div>
<script src="/components/navbar/navbar.js"></script>

<div class="flex-1 container mx-auto p-6 mt-6">
  <!-- 主内容容器 -->
  <div class="max-w-4xl mx-auto flex flex-col gap-6 transition-all duration-300" id="main-container">
    <!-- 搜索栏 -->
    <div class="glass-container p-6 rounded-xl shadow-lg bg-white bg-opacity-80 backdrop-blur-lg">
      <div class="flex items-center">
        <div class="relative flex-1">
          <input type="text" id="scenario-input" placeholder="请输入对话场景，例如:咖啡厅点餐、机场登机..." 
                 class="w-full py-3 px-5 pr-12 rounded-lg border border-gray-300 focus:border-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-200">
          <span class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500">
            <i class="fas fa-keyboard"></i>
          </span>
        </div>
        <button id="search-btn" class="ml-4 px-6 py-3 bg-gradient-to-r from-indigo-600 to-blue-500 hover:from-indigo-700 hover:to-blue-600 text-white rounded-lg shadow-md transition flex items-center">
          <i class="fas fa-search mr-2"></i>
          生成对话
        </button>
      </div>
    </div>
    
    <!-- 推荐场景区域 -->
    <div class="glass-container p-6 rounded-xl shadow-lg bg-white bg-opacity-80 backdrop-blur-lg" id="recommended-scenarios">
      <div class="mb-4">
        <h2 class="text-xl font-semibold text-gray-800 flex items-center">
          <i class="fas fa-lightbulb text-amber-500 mr-3"></i>推荐场景
          <span class="ml-2 text-sm font-normal text-gray-500">点击卡片快速生成对话</span>
        </h2>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- 每日生活场景 - 第一行 -->
        <div class="scenario-card" data-scenario="问路寻求帮助">
          <div class="icon-container bg-gradient-to-br from-amber-100 to-amber-200">
            <i class="fas fa-map-marker-alt text-amber-600"></i>
          </div>
          <div class="text-container">
            <h3 class="text-gray-800 font-medium">问路寻求帮助</h3>
            <p class="text-gray-600 text-sm">向路人询问位置和方向的对话</p>
          </div>
        </div>
        
        <!-- 旅行场景 -->
        <div class="scenario-card" data-scenario="酒店前台办理入住">
          <div class="icon-container bg-gradient-to-br from-blue-100 to-blue-200">
            <i class="fas fa-hotel text-blue-600"></i>
          </div>
          <div class="text-container">
            <h3 class="text-gray-800 font-medium">酒店入住</h3>
            <p class="text-gray-600 text-sm">在酒店前台办理入住的完整对话</p>
          </div>
        </div>
        
        <!-- 购物场景 -->
        <div class="scenario-card" data-scenario="商场购买衣服">
          <div class="icon-container bg-gradient-to-br from-green-100 to-green-200">
            <i class="fas fa-shopping-bag text-green-600"></i>
          </div>
          <div class="text-container">
            <h3 class="text-gray-800 font-medium">商场购物</h3>
            <p class="text-gray-600 text-sm">学习选购衣物和讨价还价的表达</p>
          </div>
        </div>
        
        <!-- 第二行 -->
        <!-- 餐厅场景 -->
        <div class="scenario-card" data-scenario="餐厅点餐">
          <div class="icon-container bg-gradient-to-br from-red-100 to-red-200">
            <i class="fas fa-utensils text-red-600"></i>
          </div>
          <div class="text-container">
            <h3 class="text-gray-800 font-medium">餐厅点餐</h3>
            <p class="text-gray-600 text-sm">在餐厅选择食物和饮料的对话</p>
          </div>
        </div>
        
        <!-- 交通场景 -->
        <div class="scenario-card" data-scenario="搭乘地铁问路">
          <div class="icon-container bg-gradient-to-br from-purple-100 to-purple-200">
            <i class="fas fa-subway text-purple-600"></i>
          </div>
          <div class="text-container">
            <h3 class="text-gray-800 font-medium">乘坐地铁</h3>
            <p class="text-gray-600 text-sm">询问路线和购买地铁票的对话</p>
          </div>
        </div>
        
        <!-- 医疗场景 -->
        <div class="scenario-card" data-scenario="诊所看病描述症状">
          <div class="icon-container bg-gradient-to-br from-teal-100 to-teal-200">
            <i class="fas fa-stethoscope text-teal-600"></i>
          </div>
          <div class="text-container">
            <h3 class="text-gray-800 font-medium">诊所看病</h3>
            <p class="text-gray-600 text-sm">向医生描述症状和获取建议</p>
          </div>
        </div>
        
        <!-- 第三行 - 新增场景 -->
        <!-- 电话通讯场景 -->
        <div class="scenario-card" data-scenario="电话沟通">
          <div class="icon-container bg-gradient-to-br from-indigo-100 to-indigo-200">
            <i class="fas fa-phone-alt text-indigo-600"></i>
          </div>
          <div class="text-container">
            <h3 class="text-gray-800 font-medium">电话沟通</h3>
            <p class="text-gray-600 text-sm">打电话联系服务和解决问题</p>
          </div>
        </div>
        
        <!-- 社交场景 -->
        <div class="scenario-card" data-scenario="便利店购物">
          <div class="icon-container bg-gradient-to-br from-pink-100 to-pink-200">
            <i class="fas fa-store text-pink-600"></i>
          </div>
          <div class="text-container">
            <h3 class="text-gray-800 font-medium">便利店购物</h3>
            <p class="text-gray-600 text-sm">在便利店购买日常用品的对话</p>
          </div>
        </div>
        
        <!-- 职场场景 -->
        <div class="scenario-card" data-scenario="工作面试对话">
          <div class="icon-container bg-gradient-to-br from-yellow-100 to-yellow-200">
            <i class="fas fa-briefcase text-yellow-600"></i>
          </div>
          <div class="text-container">
            <h3 class="text-gray-800 font-medium">工作面试</h3>
            <p class="text-gray-600 text-sm">面试中的问答和自我介绍</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 对话加载动画 -->
    <div id="loading-container" class="glass-container p-8 rounded-xl shadow-lg bg-white bg-opacity-80 backdrop-blur-lg hidden">
      <div class="flex flex-col items-center justify-center">
        <div class="loading-spinner"><!-- 外层蓝色加载动画由 border 实现，移除内层紫色 SVG --></div>
        <p class="mt-4 text-gray-700">正在生成对话内容，请稍候...</p>
      </div>
    </div>

    <!-- 对话容器 -->
    <div id="dialog-container" class="hidden">
      <!-- 对话信息 -->
      <div class="glass-container p-6 rounded-xl shadow-lg bg-white bg-opacity-80 backdrop-blur-lg mb-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-2" id="scenario-title">场景:</h2>
        <p class="text-gray-600" id="dialog-info">生成的对话包含了日常用语和实用表达，鼠标悬停在日语词组上可以查看详细信息，点击问号图标可以查看语法解析。</p>
      </div>
      
      <!-- 对话内容 -->
      <div class="glass-container p-6 rounded-xl shadow-lg bg-white bg-opacity-80 backdrop-blur-lg">
        <div id="chat-container" class="flex flex-col gap-6">
          <!-- 对话内容将在这里动态生成 -->
        </div>
      </div>
    </div>
  </div>

  <!-- 词组悬浮提示 -->
  <div id="word-tooltip" class="hidden fixed bg-white p-2 rounded-md shadow-lg z-50 border">
    <div class="flex items-center justify-between">
      <span id="tooltip-text" class="mr-2"></span>
      <i id="favorite-icon" class="far fa-heart cursor-pointer text-gray-400 hover:text-red-500"></i>
    </div>
  </div>
  
  <!-- 右侧:分析结果面板容器 -->
  <div id="analysis-panel-container"></div>
</div>

<footer class="w-full text-center py-4 text-gray-700 text-sm mt-auto bg-white shadow-inner">
  <div class="container mx-auto px-4">
    <p>© 2025 lingtube.net</p>
  </div>
</footer>

<!-- 引入重构后的模块文件 -->
<script src="dialog-permissions.js"></script>
<script src="/shared/utils/ui-utils.js"></script>
<script src="/shared/managers/storage-manager.js"></script>
<script src="/shared/managers/favorite-manager.js"></script>
<script src="/shared/managers/tooltip-manager.js"></script>
<script src="/shared/managers/tts-manager.js"></script>
<script src="/shared/managers/analysis-manager.js"></script>
<script src="/shared/managers/dialog-manager.js"></script>

<script src="/components/analysis-panel/analysis-panel.js"></script>
<script src="dialog.js"></script>
</body>
</html> 