body {
    background: linear-gradient(135deg, #a1c4fd, #c2e9fb);
    animation: gradient 15s ease infinite;
    background-size: 400% 400%;
    min-height: 100vh;
}

@keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

#particles-js {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.glass-container {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 1.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(12px);
    border: 1px solid transparent;
    background-clip: padding-box;
    border-image: linear-gradient(to right, #4f46e5, #a855f7) 1;
    position: relative;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.glass-container:hover {
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.85);
    border-radius: 0.75rem;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.suggestion-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.like-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #e5e7eb;
    border-radius: 0.5rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.3s ease;
}

.like-btn:hover {
    background: #d1d5db;
}

.like-btn.liked .like-icon {
    fill: #ef4444; /* 红色心形 */
}

.like-icon {
    width: 1.2rem;
    height: 1.2rem;
    fill: #4b5563; /* 默认灰色 */
}

.suggestion-content {
    flex: 1;
    color: #4b5563;
    font-size: 0.95rem;
}

.suggestion-content h3 {
    font-size: 1.1rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.pagination-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    background: #e5e7eb;
    color: #374151;
    transition: all 0.3s ease;
}

.pagination-btn:hover {
    background: #d1d5db;
    transform: translateY(-1px);
}

.pagination-btn.active {
    background: linear-gradient(to right, #4f46e5, #a855f7);
    color: white;
}

.pagination-btn:disabled {
    background: #d1d5db;
    cursor: not-allowed;
}