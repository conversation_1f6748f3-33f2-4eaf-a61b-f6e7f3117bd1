particlesJS('particles-js', {
    particles: {
        number: { value: 60, density: { enable: true, value_area: 800 } },
        color: { value: '#ffffff' },
        shape: { type: 'circle' },
        opacity: { value: 0.5, random: true },
        size: { value: 3, random: true },
        line_linked: { enable: true, distance: 150, color: '#ffffff', opacity: 0.4, width: 1 },
        move: { enable: true, speed: 1.5, direction: 'none', random: false }
    },
    interactivity: {
        detect_on: 'canvas',
        events: { onhover: { enable: true, mode: 'repulse' }, onclick: { enable: true, mode: 'push' } },
        modes: { repulse: { distance: 100, duration: 0.4 }, push: { particles_nb: 4 } }
    },
    retina_detect: true
});

// 随机生成建议数据
const suggestionData = [
    { id: 1, title: "添加夜间模式", description: "支持夜间模式以保护用户视力。", likes: 45, status: "pending", liked: false },
    { id: 2, title: "支持多语言字幕", description: "允许用户选择不同语言的字幕。", likes: 32, status: "pending", liked: false },
    { id: 3, title: "语音转文字功能", description: "将视频语音自动转为文字字幕。", likes: 28, status: "pending", liked: false },
    { id: 4, title: "单词收藏导出", description: "允许用户将收藏的单词导出为文件。", likes: 15, status: "pending", liked: false },
    { id: 5, title: "学习进度统计", description: "显示用户的学习进度和统计数据。", likes: 10, status: "pending", liked: false },
    { id: 6, title: "个性化学习计划", description: "根据用户水平生成学习计划。", likes: 8, status: "pending", liked: false },
    { id: 7, title: "离线模式", description: "支持离线下载字幕和视频学习。", likes: 25, status: "in-progress", liked: false },
    { id: 8, title: "实时翻译", description: "提供实时字幕翻译功能。", likes: 20, status: "in-progress", liked: false },
    { id: 9, title: "自定义播放速度", description: "允许用户调整视频播放速度。", likes: 18, status: "in-progress", liked: false },
    { id: 10, title: "字幕字体调整", description: "支持调整字幕字体大小和颜色。", likes: 12, status: "in-progress", liked: false },
    { id: 11, title: "学习提醒", description: "设置每日学习提醒功能。", likes: 8, status: "in-progress", liked: false },
    { id: 12, title: "多设备同步", description: "支持跨设备同步学习进度。", likes: 5, status: "in-progress", liked: false },
    { id: 13, title: "单词发音练习", description: "为每个单词提供发音练习功能。", likes: 50, status: "completed", liked: false },
    { id: 14, title: "视频书签功能", description: "用户可以为视频添加书签。", likes: 40, status: "completed", liked: false },
    { id: 15, title: "字幕同步调整", description: "手动调整字幕与视频的同步。", likes: 35, status: "completed", liked: false },
    { id: 16, title: "用户反馈系统", description: "添加用户反馈入口。", likes: 30, status: "completed", liked: false },
    { id: 17, title: "学习笔记功能", description: "用户可以为视频添加学习笔记。", likes: 25, status: "completed", liked: false },
    { id: 18, title: "社区讨论区", description: "添加用户交流的社区功能。", likes: 20, status: "completed", liked: false }
];

// 分页相关变量
const itemsPerPage = 5;
let currentPages = {
    pending: 1,
    "in-progress": 1,
    completed: 1
};
let suggestions = {
    pending: [],
    "in-progress": [],
    completed: []
};

// 按状态分组建议
function groupSuggestions() {
    suggestions.pending = suggestionData.filter(item => item.status === "pending").sort((a, b) => b.likes - a.likes);
    suggestions["in-progress"] = suggestionData.filter(item => item.status === "in-progress").sort((a, b) => b.likes - a.likes);
    suggestions.completed = suggestionData.filter(item => item.status === "completed").sort((a, b) => b.likes - a.likes);
}

// 渲染建议
function renderSuggestions(status) {
    const container = document.getElementById(`${status}-suggestions`);
    const paginationContainer = document.getElementById(`${status}-pagination`);
    if (!container || !paginationContainer) {
        console.error(`${status} 容器未找到`);
        return;
    }
    container.innerHTML = '';

    const start = (currentPages[status] - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    const itemsToShow = suggestions[status].slice(start, end);

    if (itemsToShow.length === 0) {
        container.innerHTML = '<p class="text-gray-500 text-center py-4">暂无建议</p>';
        paginationContainer.innerHTML = '';
        return;
    }

    itemsToShow.forEach(item => {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'suggestion-item';
        // 仅待开发允许点赞
        const isLikeable = status === 'pending';
        itemDiv.innerHTML = `
            <div class="${isLikeable ? 'like-btn' : 'like-btn opacity-50 cursor-not-allowed'}" ${!isLikeable ? 'onclick="return false;"' : `onclick="likeSuggestion('${status}', ${item.id})"`}>
                <svg class="like-icon" viewBox="0 0 24 24">
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                </svg>
                <span>${item.likes}</span>
            </div>
            <div class="suggestion-content">
                <h3>${item.title}</h3>
                <p>${item.description}</p>
            </div>
        `;
        if (item.liked) itemDiv.querySelector('.like-btn').classList.add('liked');
        container.appendChild(itemDiv);
    });

    renderPagination(status);
}

// 渲染分页
function renderPagination(status) {
    const totalPages = Math.ceil(suggestions[status].length / itemsPerPage);
    const paginationContainer = document.getElementById(`${status}-pagination`);
    paginationContainer.innerHTML = '';

    if (totalPages <= 1) return;

    const prevButton = document.createElement('button');
    prevButton.className = 'pagination-btn';
    prevButton.textContent = '上一页';
    prevButton.onclick = () => {
        if (currentPages[status] > 1) {
            currentPages[status]--;
            renderSuggestions(status);
        }
    };
    prevButton.disabled = currentPages[status] === 1;
    paginationContainer.appendChild(prevButton);

    const pageInfo = document.createElement('span');
    pageInfo.className = 'text-gray-600 px-4 py-2';
    pageInfo.textContent = `${currentPages[status]} / ${totalPages}`;
    paginationContainer.appendChild(pageInfo);

    const nextButton = document.createElement('button');
    nextButton.className = 'pagination-btn';
    nextButton.textContent = '下一页';
    nextButton.onclick = () => {
        if (currentPages[status] < totalPages) {
            currentPages[status]++;
            renderSuggestions(status);
        }
    };
    nextButton.disabled = currentPages[status] === totalPages;
    paginationContainer.appendChild(nextButton);
}

// 点赞功能（支持取消）
function likeSuggestion(status, id) {
    if (status !== 'pending') return; // 仅待开发允许点赞
    const suggestion = suggestions[status].find(item => item.id === id);
    if (suggestion) {
        if (suggestion.liked) {
            suggestion.likes--;
            suggestion.liked = false;
            Toastify({
                text: '已取消点赞！',
                duration: 2000,
                gravity: 'top',
                position: 'center',
                backgroundColor: '#ef4444'
            }).showToast();
        } else {
            suggestion.likes++;
            suggestion.liked = true;
            Toastify({
                text: '感谢你的支持！',
                duration: 2000,
                gravity: 'top',
                position: 'center',
                backgroundColor: '#22c55e'
            }).showToast();
        }
        suggestions[status].sort((a, b) => b.likes - a.likes);
        renderSuggestions(status);
    }
}

// 提交建议
function submitSuggestion() {
    const suggestionInput = document.getElementById('suggestion-input');
    const emailInput = document.getElementById('email-input');
    const suggestionText = suggestionInput.value.trim();
    const email = emailInput.value.trim();

    if (!suggestionText) {
        Toastify({
            text: '请填写建议内容！',
            duration: 3000,
            gravity: 'top',
            position: 'center',
            backgroundColor: '#ef4444'
        }).showToast();
        return;
    }

    const newSuggestion = {
        id: suggestionData.length + 1,
        title: suggestionText.substring(0, 20) + (suggestionText.length > 20 ? '...' : ''),
        description: suggestionText,
        likes: 0,
        status: 'pending',
        liked: false
    };
    suggestionData.push(newSuggestion);
    groupSuggestions();
    renderSuggestions('pending');

    suggestionInput.value = '';
    emailInput.value = '';

    Toastify({
        text: '建议提交成功！',
        duration: 3000,
        gravity: 'top',
        position: 'center',
        backgroundColor: '#22c55e'
    }).showToast();
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    groupSuggestions();
    renderSuggestions('pending');
    renderSuggestions('in-progress');
    renderSuggestions('completed');
});