<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>功能建议 - YouTube 字幕学习</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
  <link href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css" rel="stylesheet">
  <link rel="stylesheet" href="suggestions.css">
  <link rel="icon" type="image/png" href="/favicon.png">
</head>
<body class="min-h-screen flex flex-col">
<div id="particles-js"></div>
<div class="absolute top-4 right-4 z-10">
  <button onclick="window.location.href='/index.html'" class="bg-indigo-600 text-white px-4 py-2 rounded-lg shadow-md hover:bg-indigo-700 transition">
    用户123
  </button>
</div>
<div class="flex-1 container mx-auto p-6 mt-20 flex flex-col">
  <div class="text-center mb-12">
    <svg class="mx-auto h-16 w-16 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h10a2 2 0 012 2v12a2 2 0 01-2 2z"/>
    </svg>
    <h1 class="text-4xl font-bold text-gray-900 mt-4">功能建议</h1>
    <p class="text-gray-600 mt-2">你的意见是我们进步的动力</p>
  </div>
  <div class="flex flex-row gap-6">
    <!-- 待开发 -->
    <div class="w-1/4 glass-container p-6 rounded-xl shadow-lg">
      <h2 class="text-xl font-semibold text-gray-800 mb-6 flex items-center gap-2">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37a1.724 1.724 0 002.572-1.065z"/>
        </svg>
        待开发
      </h2>
      <div id="pending-suggestions" class="flex flex-col gap-4"></div>
      <div id="pending-pagination" class="flex justify-center mt-6 gap-4"></div>
    </div>
    <!-- 开发中 -->
    <div class="w-1/4 glass-container p-6 rounded-xl shadow-lg">
      <h2 class="text-xl font-semibold text-gray-800 mb-6 flex items-center gap-2">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"/>
        </svg>
        开发中
      </h2>
      <div id="in-progress-suggestions" class="flex flex-col gap-4"></div>
      <div id="in-progress-pagination" class="flex justify-center mt-6 gap-4"></div>
    </div>
    <!-- 已完成 -->
    <div class="w-1/4 glass-container p-6 rounded-xl shadow-lg">
      <h2 class="text-xl font-semibold text-gray-800 mb-6 flex items-center gap-2">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
          <path d="M9 16.2l-3.5-3.5a1 1 0 00-1.4 1.4l4.2 4.2a1 1 0 001.4 0l10-10a1 1 0 10-1.4-1.4L9 16.2z"/>
        </svg>
        已完成
      </h2>
      <div id="completed-suggestions" class="flex flex-col gap-4"></div>
      <div id="completed-pagination" class="flex justify-center mt-6 gap-4"></div>
    </div>
    <!-- 提交建议 -->
    <div class="w-1/4 glass-container p-6 rounded-xl shadow-lg">
      <h2 class="text-xl font-semibold text-gray-800 mb-6 flex items-center gap-2">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
        </svg>
        提交你的建议
      </h2>
      <div class="flex flex-col gap-4">
        <textarea id="suggestion-input" class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500" rows="5" placeholder="请输入你的建议..."></textarea>
        <input type="email" id="email-input" class="w-full p-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500" placeholder="请输入你的邮箱（可选）">
        <button id="submit-suggestion-btn" onclick="submitSuggestion()" class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-2 rounded-lg shadow-md hover:from-indigo-700 hover:to-purple-700 transition transform hover:-translate-y-1">
          提交建议
        </button>
      </div>
    </div>
  </div>
</div>
<footer class="w-full text-center py-4 text-gray-700 text-sm mt-auto">
  © 2025 YouTube 字幕学习工具
</footer>
<script src="suggestions.js"></script>
</body>
</html>