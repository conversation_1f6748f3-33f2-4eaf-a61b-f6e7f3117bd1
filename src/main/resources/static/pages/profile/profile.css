body {
    background: linear-gradient(135deg, #f0f4ff, #e0e7ff, #ede9fe);
    animation: gradient 15s ease infinite;
    background-size: 400% 400%;
    font-family: 'Inter', sans-serif;
    min-height: 100vh;
}

@keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.glass-container {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 1.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
}

.glass-container:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.pagination-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    background: #e5e7eb;
    color: #374151;
    transition: all 0.3s ease;
}

.pagination-btn:hover {
    background: #d1d5db;
}

.pagination-btn:disabled {
    background: #d1d5db;
    cursor: not-allowed;
}

/* 会员徽章样式 */
.subscription-loading {
    display: flex;
    align-items: center;
}

/* Premium会员徽章 */
.bg-amber-100 {
    background-color: #fef3c7;
}

.text-amber-800 {
    color: #92400e;
}

/* Plus会员徽章 */
.bg-green-100 {
    background-color: #d1fae5;
}

.text-green-800 {
    color: #065f46;
}

/* 普通会员徽章 */
.bg-indigo-100 {
    background-color: #e0e7ff;
}

.text-indigo-800 {
    color: #3730a3;
}