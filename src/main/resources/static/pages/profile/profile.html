<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人主页</title>
    <!-- 立即加载暗色模式脚本，防止页面闪烁 -->
    <script src="/shared/utils/dark-mode.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="profile.css">
    <link rel="icon" type="image/png" href="/favicon.png">
    <link rel="stylesheet" href="/assets/css/dark-mode.css">
</head>
<body class="min-h-screen flex flex-col bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50">
<!-- Navigation Bar -->
<div id="navbar-container"></div>
<script src="/components/navbar/navbar.js"></script>

<div class="flex-1 container mx-auto p-6 mt-6">
    <div class="max-w-5xl mx-auto flex flex-col lg:flex-row gap-6">
        <!-- 左侧:用户信息和历史记录 -->
        <div class="lg:w-2/3 flex flex-col gap-6">
            <!-- 用户信息 -->
            <div class="glass-container p-6 rounded-xl shadow-lg">
                <div class="flex items-center gap-4">
                    <img id="user-avatar" src="https://via.placeholder.com/100" alt="用户头像" class="w-20 h-20 rounded-full border-2 border-gray-200">
                    <div>
                        <h2 id="user-name" class="text-2xl font-semibold text-gray-800">加载中...</h2>
                        <p id="user-email" class="text-gray-600 mt-1"></p>
                        <div id="subscription-container" class="mt-2">
                            <div class="flex items-center">
                                <div class="subscription-loading">
                                    <div class="animate-pulse flex space-x-2 items-center">
                                        <div class="rounded-full bg-gray-200 h-5 w-5"></div>
                                        <div class="h-4 bg-gray-200 rounded w-24"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 历史记录 -->
            <div class="glass-container p-6 rounded-xl shadow-lg">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">历史记录</h2>
                <div id="history-container" class="flex flex-col gap-4">
                    <!-- 历史记录将通过 JS 动态生成 -->
                </div>
                <!-- 分页导航 -->
                <div id="pagination" class="flex justify-center mt-4 gap-2">
                    <button id="prev-page" onclick="changePage(-1)" class="pagination-btn bg-indigo-600 text-white hover:bg-indigo-700 disabled:bg-gray-400">上一页</button>
                    <span id="page-info" class="text-gray-600 px-3 py-1">1 / 2</span>
                    <button id="next-page" onclick="changePage(1)" class="pagination-btn bg-indigo-600 text-white hover:bg-indigo-700 disabled:bg-gray-400">下一页</button>
                </div>
            </div>
        </div>
        <!-- 右侧:统计图表 -->
        <div class="lg:w-1/3">
            <div class="glass-container p-6 rounded-xl shadow-lg">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-semibold text-gray-800">学习统计</h2>
                    <div class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full flex items-center">
                        <i class="fas fa-chart-line mr-1"></i>数据分析
                    </div>
                </div>
                
                <!-- 进度环形图 -->
                <div class="flex justify-center mb-8">
                    <div class="relative">
                        <canvas id="progress-chart" width="160" height="160"></canvas>
                        <div class="absolute inset-0 flex flex-col items-center justify-center">
                            <span id="progress-percentage" class="text-2xl font-bold text-indigo-700">0%</span>
                            <span class="text-xs text-gray-500">学习进度</span>
                        </div>
                    </div>
                </div>
                
                <!-- 学习卡片 -->
                <div class="space-y-4">
                    <!-- 收藏总数卡片 -->
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-bookmark text-blue-500"></i>
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-800">收藏总数</h3>
                                    <p class="text-xs text-gray-500">累计收藏</p>
                                </div>
                            </div>
                            <span class="text-lg font-bold text-gray-800" id="words-count">0</span>
                        </div>
                        <div class="mt-2 bg-white rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 100%"></div>
                        </div>
                    </div>
                    
                    <!-- 已学会总数卡片 -->
                    <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-graduation-cap text-green-500"></i>
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-800">已学会</h3>
                                    <p class="text-xs text-gray-500">掌握单词和语法</p>
                                </div>
                            </div>
                            <span class="text-lg font-bold text-gray-800" id="total-learned">0</span>
                        </div>
                        <div class="mt-2 bg-white rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 70%"></div>
                        </div>
                    </div>
                    
                    <!-- 本月观看视频卡片 -->
                    <div class="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-play-circle text-purple-500"></i>
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-800">本月视频</h3>
                                    <p class="text-xs text-gray-500">观看数量</p>
                                </div>
                            </div>
                            <span class="text-lg font-bold text-gray-800" id="videos-count">0</span>
                        </div>
                        <div class="mt-2 bg-white rounded-full h-2">
                            <div class="bg-purple-500 h-2 rounded-full" style="width: 60%"></div>
                        </div>
                    </div>
                    
                    <!-- 本周登录天数卡片 -->
                    <div class="bg-gradient-to-r from-amber-50 to-yellow-50 p-4 rounded-lg hover:shadow-md transition-all duration-300 transform hover:-translate-y-1">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-fire text-amber-500"></i>
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-800">本周登录</h3>
                                    <p class="text-xs text-gray-500">天数</p>
                                </div>
                            </div>
                            <span class="text-lg font-bold text-gray-800" id="streak-count">0</span>
                        </div>
                        <div class="flex justify-center mt-2">
                            <div class="grid grid-cols-7 gap-1">
                                <div class="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-white text-xs">一</div>
                                <div class="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-white text-xs">二</div>
                                <div class="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-white text-xs">三</div>
                                <div class="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-white text-xs">四</div>
                                <div class="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-white text-xs">五</div>
                                <div class="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-white text-xs">六</div>
                                <div class="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-white text-xs">日</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<footer class="w-full text-center py-4 text-gray-700 text-sm mt-auto bg-white shadow-inner">
    <div class="container mx-auto px-4">
        <p>© 2025 lingtube.net</p>
    </div>
</footer>
<script src="profile.js"></script>
</body>
</html>