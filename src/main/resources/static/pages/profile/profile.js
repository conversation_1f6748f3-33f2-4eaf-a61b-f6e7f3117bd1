// 获取用户信息
function fetchUserProfile() {
    fetch('/api/user/me')
        .then(response => {
            if (!response.ok) {
                throw new Error('获取用户信息失败');
            }
            return response.json();
        })
        .then(user => {
            if (user.authenticated) {
                // 更新用户信息
                document.getElementById('user-avatar').src = user.picture || 'https://via.placeholder.com/100';
                document.getElementById('user-name').textContent = user.name || '未知用户';
                document.getElementById('user-email').textContent = user.email || '';
                
                // 登录时记录登录日志
                logUserLogin();
            } else {
                console.warn('用户未登录');
                // 可以在这里添加重定向到登录页的逻辑
            }
        })
        .catch(error => {
            console.error('获取用户信息出错:', error);
            Toastify({
                text: '获取用户信息失败，请重试',
                duration: 3000,
                gravity: 'top',
                position: 'center',
                backgroundColor: '#ef4444'
            }).showToast();
        });
}

// 记录用户登录
function logUserLogin() {
    fetch('/api/login/log', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) {
            console.warn('记录登录日志失败');
        } else {
            console.log('登录日志记录成功，重新获取学习统计数据');
            fetchLearningStats(); // 重新获取学习统计数据
        }
    })
    .catch(error => {
        console.error('记录登录日志出错:', error);
    });
}

// 获取订阅信息
function fetchSubscriptionInfo() {
    const subscriptionContainer = document.getElementById('subscription-container');
    
    fetch('/api/subscription/active')
        .then(response => {
            if (!response.ok) {
                throw new Error('获取订阅信息失败');
            }
            return response.json();
        })
        .then(data => {
            if (!data.authenticated) {
                console.warn('用户未登录');
                return;
            }
            
            if (data.hasActiveSubscription) {
                const subscription = data.subscription;
                const planName = subscription.planName;
                const endDate = new Date(subscription.endDate);
                const formattedEndDate = endDate.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                
                let badgeClass = 'bg-indigo-100 text-indigo-800';
                let icon = 'fa-crown';
                
                if (planName.toLowerCase().includes('premium')) {
                    badgeClass = 'bg-amber-100 text-amber-800';
                    icon = 'fa-crown';
                } else if (planName.toLowerCase().includes('plus')) {
                    badgeClass = 'bg-green-100 text-green-800';
                    icon = 'fa-star';
                }
                
                subscriptionContainer.innerHTML = `
                    <div class="flex items-center">
                        <span class="${badgeClass} flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                            <i class="fas ${icon} mr-1"></i>
                            ${planName}
                        </span>
                        <span class="ml-2 text-xs text-gray-500">有效期至:${formattedEndDate}</span>
                    </div>
                `;
            } else {
                subscriptionContainer.innerHTML = `
                    <span class="text-gray-500 text-xs">普通会员</span>
                `;
            }
        })
        .catch(error => {
            console.error('获取订阅信息出错:', error);
            subscriptionContainer.innerHTML = `
                <span class="text-gray-500 text-xs">普通会员</span>
            `;
        });
}

// 获取学习统计数据
function fetchLearningStats() {
    fetch('/api/stats/learning')
        .then(response => {
            if (!response.ok) {
                throw new Error('获取学习统计失败');
            }
            return response.json();
        })
        .then(stats => {
            // 更新学习统计面板
            updateLearningStatsUI(stats);
        })
        .catch(error => {
            console.error('获取学习统计出错:', error);
            Toastify({
                text: '获取学习统计失败，请重试',
                duration: 3000,
                gravity: 'top',
                position: 'center',
                backgroundColor: '#ef4444'
            }).showToast();
            
            // 使用默认值更新 UI
            updateLearningStatsUI({
                totalSaved: 0,
                totalLearned: 0,
                monthlyWatchedVideos: 0,
                weeklyLoginDays: 0
            });
        });
}

// 更新学习统计面板 UI
function updateLearningStatsUI(stats) {
    // 环形进度图 - 假设学习进度为已学单词 / 总收藏单词
    const progressCtx = document.getElementById('progress-chart').getContext('2d');
    let progressValue = 0;
    
    if (stats.totalSaved > 0) {
        progressValue = Math.round((stats.totalLearned / stats.totalSaved) * 100);
    }
    
    // 更新进度百分比
    document.getElementById('progress-percentage').textContent = `${progressValue}%`;
    
    // 清除旧的图表实例 (如果存在)
    if (window.progressChartInstance) {
        window.progressChartInstance.destroy();
    }
    
    // 绘制环形图
    window.progressChartInstance = new Chart(progressCtx, {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [progressValue, 100 - progressValue],
                backgroundColor: [
                    'rgba(79, 70, 229, 0.8)', // 紫色填充
                    'rgba(226, 232, 240, 0.6)' // 灰色背景
                ],
                borderWidth: 0,
                cutout: '80%',
                borderRadius: 10,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    enabled: false
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1500,
                easing: 'easeOutCirc'
            }
        }
    });
    
    // 更新统计卡片数据
    document.getElementById('words-count').textContent = stats.totalSaved;
    document.getElementById('total-learned').textContent = stats.totalLearned;
    document.getElementById('videos-count').textContent = stats.monthlyWatchedVideos;
    // 使用 weeklyLoginDaysCount 更新总天数显示
    document.getElementById('streak-count').textContent = stats.weeklyLoginDaysCount !== undefined ? stats.weeklyLoginDaysCount : 0;
    
    // 更新本周登录天数圆点显示，使用 weeklyActiveDays
    updateWeekdayCircles(stats.weeklyActiveDays || []);
}

// 更新本周登录天数显示
function updateWeekdayCircles(activeDaysOfWeek) { // activeDaysOfWeek 是一个数组，例如 [1, 3, 7] (周一, 周三, 周日)
    const weekdayCircles = document.querySelectorAll('.grid-cols-7 > div');
    const daysLabels = ['一', '二', '三', '四', '五', '六', '日'];

    weekdayCircles.forEach((circle, index) => {
        const dayOfWeek = index + 1; // 1 for Monday, 2 for Tuesday, ..., 7 for Sunday
        
        // 重置样式
        circle.classList.remove('bg-amber-500', 'text-white');
        circle.classList.add('bg-gray-200'); // 默认背景
        circle.textContent = daysLabels[index]; // 设置文本

        // 根据是否活跃日来设置不同文本颜色
        if (activeDaysOfWeek.includes(dayOfWeek)) {
            circle.classList.remove('bg-gray-200');
            circle.classList.add('bg-amber-500', 'text-white'); // 活跃日样式
            
            // 添加动画效果 (可选)
            // setTimeout(() => { // 暂时注释掉动画，避免重复触发问题
            //     circle.style.transform = 'scale(1.1)';
            //     circle.style.transition = 'transform 0.3s ease';
            //     setTimeout(() => {
            //         circle.style.transform = 'scale(1)';
            //     }, 300);
            // }, index * 50); // 动画延迟
        } else {
             // 非活跃日文本颜色 (如果背景是浅灰，文字可以是深灰)
            circle.classList.add('text-gray-500'); 
        }
    });
}

// 分页参数
const itemsPerPage = 5;
let currentPage = 1;
let totalPages = 1;
let historyData = [];

// 获取播放历史
function fetchPlayHistory() {
    const historyContainer = document.getElementById('history-container');
    historyContainer.innerHTML = `
        <div class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
            <span class="ml-3 text-gray-600">加载历史记录中...</span>
        </div>
    `;
    
    fetch('/api/history/list')
        .then(response => {
            if (!response.ok) {
                throw new Error('获取播放历史失败');
            }
            return response.json();
        })
        .then(data => {
            historyData = data || [];
            totalPages = Math.ceil(historyData.length / itemsPerPage);
            
            if (historyData.length === 0) {
                historyContainer.innerHTML = `
                    <div class="flex flex-col items-center justify-center py-8 text-gray-500">
                        <i class="fas fa-history text-4xl mb-3"></i>
                        <p>暂无观看记录</p>
                    </div>
                `;
                
                // 隐藏分页
                document.getElementById('pagination').classList.add('hidden');
            } else {
                // 显示分页
                document.getElementById('pagination').classList.remove('hidden');
                // 渲染历史记录
                renderHistory();
            }
        })
        .catch(error => {
            console.error('获取播放历史出错:', error);
            historyContainer.innerHTML = `
                <div class="flex flex-col items-center justify-center py-8 text-red-500">
                    <i class="fas fa-exclamation-circle text-4xl mb-3"></i>
                    <p>获取历史记录失败</p>
                    <button onclick="fetchPlayHistory()" class="mt-3 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                        <i class="fas fa-sync-alt mr-2"></i>重试
                    </button>
                </div>
            `;
            
            // 隐藏分页
            document.getElementById('pagination').classList.add('hidden');
        });
}

// 渲染历史记录
function renderHistory() {
    const start = (currentPage - 1) * itemsPerPage;
    const end = Math.min(start + itemsPerPage, historyData.length);
    const currentItems = historyData.slice(start, end);

    const historyContainer = document.getElementById('history-container');
    historyContainer.innerHTML = '';

    if (currentItems.length === 0) {
        historyContainer.innerHTML = `
            <div class="flex flex-col items-center justify-center py-8 text-gray-500">
                <i class="fas fa-history text-4xl mb-3"></i>
                <p>暂无观看记录</p>
            </div>
        `;
        return;
    }

    currentItems.forEach(item => {
        const date = new Date(item.date);
        const formattedDate = date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        const itemElement = document.createElement('div');
        itemElement.className = 'flex items-center gap-4 p-2 hover:bg-indigo-50 rounded-lg transition-colors cursor-pointer';
        itemElement.innerHTML = `
            <img src="${item.thumbnailUrl}" alt="视频缩略图" class="w-24 h-16 rounded-lg object-cover">
            <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-800">${item.title}</h3>
                <p class="text-gray-600 text-sm">观看时间:${formattedDate}</p>
            </div>
            <a href="#" class="text-indigo-600 hover:text-indigo-800 play-btn" data-video-id="${item.videoId}">
                <i class="fas fa-play-circle text-xl"></i>
            </a>
        `;
        itemElement.addEventListener('click', function(e) {
            // 防止点击播放按钮时触发整个卡片的点击事件
            if (e.target.closest('.play-btn')) return;
            
            // 获取YouTube URL
            const videoId = item.videoId;
            const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}`;
            
            // 直接打开YouTube页面
            window.open(youtubeUrl, '_blank');
        });
        
        // 为播放按钮添加单独的点击事件
        const playBtn = itemElement.querySelector('.play-btn');
        playBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const videoId = this.getAttribute('data-video-id');
            const youtubeUrl = `https://www.youtube.com/watch?v=${videoId}`;
            
            // 直接打开YouTube页面
            window.open(youtubeUrl, '_blank');
        });
        
        historyContainer.appendChild(itemElement);
    });

    // 更新分页信息
    document.getElementById('page-info').textContent = `${currentPage} / ${totalPages || 1}`;
    document.getElementById('prev-page').disabled = currentPage === 1 || totalPages === 0;
    document.getElementById('next-page').disabled = currentPage === totalPages || totalPages === 0;
}

// 切换页面
function changePage(delta) {
    currentPage += delta;
    if (currentPage < 1) currentPage = 1;
    if (currentPage > totalPages) currentPage = totalPages;
    renderHistory();
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 获取用户信息
    fetchUserProfile();
    
    // 获取订阅信息
    fetchSubscriptionInfo();
    
    // 获取并渲染播放历史
    fetchPlayHistory();
    
    // 获取学习统计数据
    fetchLearningStats();
});