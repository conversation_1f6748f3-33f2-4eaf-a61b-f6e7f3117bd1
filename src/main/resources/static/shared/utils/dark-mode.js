// 立即执行函数，防止页面闪烁 - 在DOM加载前就检查暗色模式
(function() {
  const isDarkMode = localStorage.getItem('darkMode') === 'true';
  if (isDarkMode) {
    // 立即给html元素添加dark-mode类
    document.documentElement.classList.add('dark-mode');
    
    // 等待body元素可用时立即添加dark-mode类
    const applyDarkModeToBody = () => {
      if (document.body) {
        document.body.classList.add('dark-mode');
      } else {
        // 如果body还没有加载，继续等待
        setTimeout(applyDarkModeToBody, 1);
      }
    };
    
    // 如果body已经存在，立即应用；否则等待
    if (document.body) {
      applyDarkModeToBody();
    } else {
      // 监听DOMContentLoaded事件作为备用方案
      document.addEventListener('DOMContentLoaded', applyDarkModeToBody);
    }
  }
})();

// 黑暗模式处理脚本
document.addEventListener('DOMContentLoaded', function() {
  // 获取黑暗模式开关元素
  const darkModeToggle = document.getElementById('dark-mode-toggle');
  const mobileDarkModeToggle = document.getElementById('mobile-dark-mode-toggle');
  
  // 从localStorage读取黑暗模式状态
  const isDarkMode = localStorage.getItem('darkMode') === 'true';
  
  // 切换黑暗模式的函数
  function toggleDarkMode(enabled) {
    if (enabled) {
      document.documentElement.classList.add('dark-mode');
      document.body.classList.add('dark-mode');
      localStorage.setItem('darkMode', 'true');
    } else {
      document.documentElement.classList.remove('dark-mode');
      document.body.classList.remove('dark-mode');
      localStorage.setItem('darkMode', 'false');
    }
    
    // 同步所有切换开关的状态
    if (darkModeToggle) {
      darkModeToggle.checked = enabled;
    }
    if (mobileDarkModeToggle) {
      mobileDarkModeToggle.checked = enabled;
    }
  }
  
  // 初始化黑暗模式状态（确保开关状态正确）
  if (isDarkMode) {
    // 确保类已经被添加（立即执行函数可能已经添加了）
    if (!document.documentElement.classList.contains('dark-mode')) {
      document.documentElement.classList.add('dark-mode');
    }
    if (!document.body.classList.contains('dark-mode')) {
      document.body.classList.add('dark-mode');
    }
    
    // 同步开关状态
    if (darkModeToggle) {
      darkModeToggle.checked = true;
    }
    if (mobileDarkModeToggle) {
      mobileDarkModeToggle.checked = true;
    }
  }
  
  // 监听桌面端黑暗模式开关变化
  if (darkModeToggle) {
    darkModeToggle.addEventListener('change', function() {
      toggleDarkMode(this.checked);
    });
  }
  
  // 监听移动端黑暗模式开关变化
  if (mobileDarkModeToggle) {
    mobileDarkModeToggle.addEventListener('change', function() {
      toggleDarkMode(this.checked);
    });
  }
  
  // 添加黑暗模式切换快捷键 (Ctrl+Shift+D)
  document.addEventListener('keydown', function(event) {
    if (event.ctrlKey && event.shiftKey && event.key === 'D') {
      const currentDarkMode = localStorage.getItem('darkMode') === 'true';
      const newDarkMode = !currentDarkMode;
      
      // 使用统一的切换函数
      toggleDarkMode(newDarkMode);
      
      // 防止默认行为
      event.preventDefault();
    }
  });
});
