// 国际化系统
class I18n {
  constructor() {
    this.currentLanguage = 'zh';
    this.translations = {
      zh: {
        // 页面基本信息
        page: {
          title: 'lingtube.net - AI驱动的YouTube语言学习平台',
          description: '智能字幕分析 • 实时翻译 • 个性化学习路径 • 多语言支持'
        },
        
        // 导航栏
        nav: {
          features: '功能特点',
          showcase: '功能演示',
          pricing: '定价',
          techSupport: '技术支持',
          faq: '常见问题',
          login: '谷歌登录',
          loginHint: '需要登录',
          loginHintDesc: '请先登录以使用视频分析功能'
        },
        
        // 用户菜单
        user: {
          profile: '个人主页',
          dialog: '场景对话',
          review: '单词复习',
          subscription: '会员订阅',
          logout: '退出登录'
        },
        
        // Hero区域
        hero: {
          title: '利用先进的人工智能技术<br><span class="text-gradient">重新定义</span>YouTube语言学习',
          subtitle: '智能字幕分析 • 实时翻译 • 个性化学习路径 • 多语言支持<br>让每个YouTube视频都成为您的专属语言老师',
          startBtn: '立即开始学习',
          learnMoreBtn: '了解更多功能',
          stats: {
            users: '10,000+ 活跃学习者',
            rating: '4.9/5 用户评分',
            languages: '支持多种语言'
          }
        },
        
        // 功能特点
        features: {
          title: '功能特点',
          sectionTitle: '核心功能',
          ai: {
            title: 'AI智能分析',
            desc: '自动识别关键词汇和语法点，提供个性化学习建议，让学习更加精准高效'
          },
          multilang: {
            title: '多语言支持',
            desc: '支持英语、日语等多种语言的学习和翻译，满足不同语言学习需求'
          },
          subtitle: {
            title: '智能字幕',
            desc: '实时生成和优化字幕，支持多语言同步显示，提升理解效率'
          },
          tracking: {
            title: '学习跟踪',
            desc: '详细的学习进度分析和个性化复习计划，让进步看得见'
          }
        },
        
        // 功能展示
        showcase: {
          title: '功能演示',
          subtitle: '通过直观的界面展示，了解我们如何让语言学习变得更加高效和有趣',
          sectionTitle: '产品展示',
          feature1: {
            category: 'AI智能分析',
            title: '智能字幕分析与词性标注',
            desc: '我们的AI系统能够自动识别视频中的关键词汇，进行精确的词性分析和语法标注。通过不同颜色的高亮显示，让您一眼就能识别名词、动词、形容词等词性，大大提升学习效率。',
            features: [
              '实时词性分析和语法标注',
              '多彩字幕显示，直观易懂',
              '支持英语和日语双语分析'
            ]
          },
          feature2: {
            category: '实时翻译',
            title: '悬浮翻译与深度解析',
            desc: '只需将鼠标悬停在任意单词或短语上，即可获得精准的翻译结果。我们的AI不仅提供基础翻译，还会解析语法结构、惯用搭配和文化背景，让您真正理解语言的精髓。',
            features: [
              '即时悬浮翻译，无需点击',
              '语法结构和惯用搭配解析',
              '文化背景和使用场景说明'
            ]
          },
          feature3: {
            category: '学习跟踪',
            title: '智能学习进度管理',
            desc: '系统会自动记录您的学习历史，分析您的学习偏好和薄弱环节。通过数据可视化图表，清晰展示学习进度，并提供个性化的复习建议和学习计划。',
            features: [
              '详细的学习数据统计分析',
              '个性化复习计划推荐',
              '学习成果可视化展示'
            ]
          },
          feature4: {
            category: 'AI对话',
            title: '智能场景对话生成',
            desc: '基于视频内容和您的学习需求，AI会自动生成相关的生活场景对话。无论是商务会议、日常购物还是旅行交流，都能为您提供贴近实际的对话练习机会。',
            features: [
              '基于视频内容的智能对话生成',
              '多种生活场景模拟练习',
              'Azure神经网络语音合成'
            ]
          },
          feature5: {
            category: '智能摘要',
            title: 'AI视频内容摘要',
            desc: '利用先进的自然语言处理技术，AI会自动分析视频内容并生成精准的摘要。无论是长达几小时的讲座还是短小的教学视频，都能快速提取核心要点，节省您的时间。',
            features: [
              '自动提取视频核心要点',
              '多层级摘要结构化展示',
              '支持长视频内容快速理解'
            ]
          },
          feature6: {
            category: '语音合成',
            title: '高质量语音合成TTS',
            desc: '采用Azure神经网络语音技术，为您提供接近真人的高质量语音合成服务。支持多种语言和音色选择，让您在学习过程中享受自然流畅的听觉体验。',
            features: [
              'Azure神经网络拟人发音',
              '多语言多音色选择',
              '支持语速和音调调节'
            ]
          },
          feature7: {
            category: '专注学习',
            title: '沉浸式学习模式',
            desc: '专为语言学习优化的观看模式，去除干扰元素，突出学习重点。支持字幕遮罩、重复播放、语速调节等功能，创造最佳的学习环境，让您全身心投入语言学习。',
            features: [
              '去除干扰的专注界面',
              '字幕遮罩和重复播放',
              '语速调节和快捷键支持'
            ]
          }
        },
        
        // 技术支持
        techSupport: {
          title: '技术支持',
          subtitle: 'Lingtube由顶尖科技公司提供技术支持',
          sectionTitle: '合作伙伴',
          partners: {
            openai: 'OpenAI - 人工智能技术',
            google: 'Google - 云服务与AI',
            deepseek: 'DeepSeek - 深度学习',
            qwen: 'Qwen - 大语言模型',
            aliyun: '阿里云 - 云计算服务',
            cloudflare: 'Cloudflare - 全球CDN'
          }
        },
        
        // 定价
        pricing: {
          title: '选择适合的套餐',
          subtitle: '升级账户以解锁全部学习功能，加速您的语言学习之旅',
          sectionTitle: '智能学习计划',
          monthly: '月付',
          yearly: '年付',
          yearlyDiscount: '年付可省 15%',
          free: {
            title: '免费版',
            subtitle: '入门级基础功能',
            price: '¥0',
            period: '/月',
            yearlyPeriod: '/年',
            note: '无需信用卡',
            features: [
              '注册即送7天premium会员',
              '沉浸式学习体验',
              '英语/日语 双语视频支持',
              '双语字幕功能',
              'AI多彩字幕功能',
              '基于NLP的AI词性分析功能',
              '弹幕功能',
              '黑暗模式',
              '历史记录功能'
            ],
            button: '开始使用'
          },
          plus: {
            title: 'Plus 专业版',
            subtitle: '最适合语言学习者',
            popular: '最受欢迎',
            price: '¥29',
            period: '/月',
            note: '单独购买',
            yearlyPrice: '¥295',
            yearlyPeriod: '/年',
            yearlySave: '省 ¥',
            features: [
              '所有免费功能',
              '单词悬浮翻译功能（经过AI还原解析）',
              'AI语法、接续、惯用搭配、俚语等的分析功能',
              '单词、固定搭配、语法接续、俚语等的收藏功能',
              '高质量AI单词解析功能',
              '贴近日常生活的AI高质量例句生成功能',
              '外语视频的中文发音功能（Azure神经网络拟人发音）',
              '英语/日语 双语例句、对话朗读功能（Azure神经网络拟人发音）',
              '数据统计、分析功能',
              '解析蒙版功能'
            ],
            button: '立即订阅'
          },
          premium: {
            title: 'Premium 尊享版',
            subtitle: '极致学习体验',
            price: '¥59',
            period: '/月',
            note: '单独购买',
            yearlyPrice: '¥595',
            yearlyPeriod: '/年',
            yearlySave: '省 ¥',
            features: [
              '所有 Plus 功能',
              'AI视频摘要功能',
              'AI聊天助手功能（基于视频上下文回答问题）',
              'AI无字幕视频转录功能（基于大语言模型）',
              'AI词汇、语法、惯用搭配等的联想拓展功能',
              'AI任意生活场景的对话生成和解析功能',
              '优先体验最新功能'
            ],
            button: '立即订阅'
          },
          footer: {
            transparent: '优惠透明价格',
            secure: '安全支付',
            support: '7x24 支持'
          }
        },
        
        // FAQ
        faq: {
          title: '常见问题',
          subtitle: '快速了解平台使用方法',
          sectionTitle: '帮助中心',
          q1: {
            question: '如何开始使用lingtube.net？',
            answer: '只需使用Google账号登录，然后安装我们的浏览器扩展程序，即可在YouTube上直接使用AI分析功能。'
          },
          q2: {
            question: '支持哪些语言的学习？',
            answer: '目前支持英语和日语的学习，我们正在不断添加更多语言支持，包括韩语、法语、德语等。'
          },
          q3: {
            question: '是否需要安装特殊软件？',
            answer: '只需安装我们的Chrome浏览器扩展程序即可，无需安装其他软件。扩展程序轻量级且安全。'
          },
          q4: {
            question: '如何取消订阅？',
            answer: '您可以随时在个人设置页面取消订阅，取消后仍可使用到当前计费周期结束。'
          }
        },
        
        // Footer
        footer: {
          description: '利用AI技术让语言学习更高效、更有趣，成为您专属的语言学习助手',
          product: '产品',
          support: '支持',
          legal: '法律',
          links: {
            features: '功能特点',
            pricing: '定价方案',
            start: '开始使用',
            faq: '常见问题',
            contact: '联系客服',
            help: '使用帮助',
            privacy: '隐私政策',
            terms: '服务条款',
            cookies: 'Cookie政策'
          },
          copyright: '© 2024 lingtube.net. 保留所有权利。'
        },
        
        // 通用
        common: {
          close: '关闭',
          loading: '加载中...',
          error: '出错了',
          success: '成功',
          confirm: '确认',
          cancel: '取消'
        }
      },
      
      en: {
        // 页面基本信息
        page: {
          title: 'lingtube.net - AI-Powered YouTube Language Learning Platform',
          description: 'Smart Subtitle Analysis • Real-time Translation • Personalized Learning Path • Multi-language Support'
        },
        
        // 导航栏
        nav: {
          features: 'Features',
          showcase: 'Showcase',
          pricing: 'Pricing',
          techSupport: 'Tech Support',
          faq: 'FAQ',
          login: 'Google Login',
          loginHint: 'Login Required',
          loginHintDesc: 'Please login first to use video analysis features'
        },
        
        // 用户菜单
        user: {
          profile: 'Profile',
          dialog: 'Dialog Practice',
          review: 'Word Review',
          subscription: 'Subscription',
          logout: 'Logout'
        },
        
        // Hero区域
        hero: {
          title: 'Revolutionize YouTube Language Learning<br>with <span class="text-gradient">Advanced AI Technology</span>',
          subtitle: 'Smart Subtitle Analysis • Real-time Translation • Personalized Learning Path • Multi-language Support<br>Turn every YouTube video into your personal language tutor',
          startBtn: 'Start Learning Now',
          learnMoreBtn: 'Learn More Features',
          stats: {
            users: '10,000+ Active Learners',
            rating: '4.9/5 User Rating',
            languages: 'Multi-language Support'
          }
        },
        
        // 功能特点
        features: {
          title: 'Features',
          sectionTitle: 'Core Features',
          ai: {
            title: 'AI Smart Analysis',
            desc: 'Automatically identify key vocabulary and grammar points, provide personalized learning suggestions for more precise and efficient learning'
          },
          multilang: {
            title: 'Multi-language Support',
            desc: 'Support learning and translation of multiple languages including English and Japanese, meeting different language learning needs'
          },
          subtitle: {
            title: 'Smart Subtitles',
            desc: 'Real-time generation and optimization of subtitles, support multi-language synchronous display, improve comprehension efficiency'
          },
          tracking: {
            title: 'Learning Progress',
            desc: 'Detailed learning progress analysis and personalized review plans, making progress visible'
          }
        },
        
        // 功能展示
        showcase: {
          title: 'Feature Showcase',
          subtitle: 'Through intuitive interface demonstrations, understand how we make language learning more efficient and interesting',
          sectionTitle: 'Product Showcase',
          feature1: {
            category: 'AI Smart Analysis',
            title: 'Smart Subtitle Analysis & POS Tagging',
            desc: 'Our AI system can automatically identify key vocabulary in videos and perform precise part-of-speech analysis and grammar tagging. Through different colored highlights, you can instantly recognize nouns, verbs, adjectives and other parts of speech, greatly improving learning efficiency.',
            features: [
              'Real-time POS analysis and grammar tagging',
              'Colorful subtitle display, intuitive and easy to understand',
              'Support bilingual analysis for English and Japanese'
            ]
          },
          feature2: {
            category: 'Real-time Translation',
            title: 'Hover Translation & Deep Analysis',
            desc: 'Simply hover your mouse over any word or phrase to get accurate translation results. Our AI not only provides basic translation, but also analyzes grammatical structures, idiomatic collocations and cultural backgrounds, allowing you to truly understand the essence of language.',
            features: [
              'Instant hover translation without clicking',
              'Grammar structure and idiomatic collocation analysis',
              'Cultural background and usage scenario explanation'
            ]
          },
          feature3: {
            category: 'Learning Progress',
            title: 'Smart Learning Progress Management',
            desc: 'The system automatically records your learning history and analyzes your learning preferences and weak points. Through data visualization charts, it clearly shows learning progress and provides personalized review suggestions and learning plans.',
            features: [
              'Detailed learning data statistical analysis',
              'Personalized review plan recommendations',
              'Visual display of learning achievements'
            ]
          },
          feature4: {
            category: 'AI Dialogue',
            title: 'Smart Scenario Dialogue Generation',
            desc: 'Based on video content and your learning needs, AI automatically generates relevant life scenario dialogues. Whether it\'s business meetings, daily shopping or travel communication, it provides you with realistic dialogue practice opportunities.',
            features: [
              'Smart dialogue generation based on video content',
              'Multiple life scenario simulation practice',
              'Azure neural network speech synthesis'
            ]
          },
          feature5: {
            category: 'Smart Summary',
            title: 'AI Video Content Summary',
            desc: 'Using advanced natural language processing technology, AI automatically analyzes video content and generates accurate summaries. Whether it\'s hours-long lectures or short educational videos, it can quickly extract key points and save your time.',
            features: [
              'Automatically extract key points from videos',
              'Multi-level summary structured display',
              'Support quick understanding of long video content'
            ]
          },
          feature6: {
            category: 'Speech Synthesis',
            title: 'High-Quality Speech Synthesis TTS',
            desc: 'Using Azure neural network speech technology to provide you with high-quality speech synthesis services close to human voice. Support multiple languages and voice selections, allowing you to enjoy natural and smooth auditory experience during learning.',
            features: [
              'Azure neural network human-like pronunciation',
              'Multiple language and voice options',
              'Support speech rate and tone adjustment'
            ]
          },
          feature7: {
            category: 'Focused Learning',
            title: 'Immersive Learning Mode',
            desc: 'Viewing mode optimized specifically for language learning, removing distracting elements and highlighting learning focus. Support subtitle masking, repeat playback, speed adjustment and other functions to create the best learning environment for full immersion in language learning.',
            features: [
              'Distraction-free focused interface',
              'Subtitle masking and repeat playback',
              'Speed adjustment and shortcut key support'
            ]
          }
        },
        
        // 技术支持
        techSupport: {
          title: 'Tech Support',
          subtitle: 'Lingtube is powered by leading technology companies',
          sectionTitle: 'Partners',
          partners: {
            openai: 'OpenAI - Artificial Intelligence',
            google: 'Google - Cloud Services & AI',
            deepseek: 'DeepSeek - Deep Learning',
            qwen: 'Qwen - Large Language Model',
            aliyun: 'Alibaba Cloud - Cloud Computing',
            cloudflare: 'Cloudflare - Global CDN'
          }
        },
        
        // 定价
        pricing: {
          title: 'Choose the Right Plan',
          subtitle: 'Upgrade your account to unlock all learning features and accelerate your language learning journey',
          sectionTitle: 'Smart Learning Plans',
          monthly: 'Monthly',
          yearly: 'Yearly',
          yearlyDiscount: 'Save 15% with yearly',
          free: {
            title: 'Free',
            subtitle: 'Entry-level basic features',
            price: '$0',
            period: '/month',
            yearlyPeriod: '/year',
            note: 'No credit card required',
            features: [
              '7-day premium membership upon registration',
              'Immersive learning experience',
              'English/Japanese bilingual video support',
              'Bilingual subtitle function',
              'AI colorful subtitle function',
              'NLP-based AI POS analysis function',
              'Danmaku function',
              'Dark mode',
              'History function'
            ],
            button: 'Get Started'
          },
          plus: {
            title: 'Plus Professional',
            subtitle: 'Best for language learners',
            popular: 'Most Popular',
            price: '$4',
            period: '/month',
            note: 'Billed separately',
            yearlyPrice: '$40',
            yearlyPeriod: '/year',
            yearlySave: 'Save $',
            features: [
              'All free features',
              'Word hover translation (AI-powered analysis)',
              'AI grammar, conjunction, idiom, slang analysis',
              'Collection function for words, phrases, grammar, slang',
              'High-quality AI word analysis',
              'AI high-quality example sentence generation',
              'Foreign video Chinese pronunciation (Azure neural network)',
              'English/Japanese bilingual example sentence reading (Azure neural network)',
              'Data statistics and analysis',
              'Analysis mask function'
            ],
            button: 'Subscribe Now'
          },
          premium: {
            title: 'Premium Elite',
            subtitle: 'Ultimate learning experience',
            price: '$8',
            period: '/month',
            note: 'Billed separately',
            yearlyPrice: '$81',
            yearlyPeriod: '/year',
            yearlySave: 'Save $',
            features: [
              'All Plus features',
              'AI video summary function',
              'AI chat assistant (context-based Q&A)',
              'AI subtitle-free video transcription (LLM-based)',
              'AI vocabulary, grammar, idiom association expansion',
              'AI arbitrary life scenario dialogue generation and analysis',
              'Priority access to latest features'
            ],
            button: 'Subscribe Now'
          },
          footer: {
            transparent: 'Transparent Pricing',
            secure: 'Secure Payment',
            support: '24/7 Support'
          }
        },
        
        // FAQ
        faq: {
          title: 'Frequently Asked Questions',
          subtitle: 'Quick guide to using the platform',
          sectionTitle: 'Help Center',
          q1: {
            question: 'How to get started with lingtube.net?',
            answer: 'Simply login with your Google account, then install our browser extension to use AI analysis features directly on YouTube.'
          },
          q2: {
            question: 'Which languages are supported for learning?',
            answer: 'Currently supports English and Japanese learning. We are continuously adding more language support, including Korean, French, German, etc.'
          },
          q3: {
            question: 'Do I need to install special software?',
            answer: 'Only need to install our Chrome browser extension, no other software required. The extension is lightweight and secure.'
          },
          q4: {
            question: 'How to cancel subscription?',
            answer: 'You can cancel your subscription anytime in personal settings page. You can still use the service until the current billing period ends.'
          }
        },
        
        // Footer
        footer: {
          description: 'Use AI technology to make language learning more efficient and interesting, becoming your personal language learning assistant',
          product: 'Product',
          support: 'Support',
          legal: 'Legal',
          links: {
            features: 'Features',
            pricing: 'Pricing',
            start: 'Get Started',
            faq: 'FAQ',
            contact: 'Contact Support',
            help: 'Help',
            privacy: 'Privacy Policy',
            terms: 'Terms of Service',
            cookies: 'Cookie Policy'
          },
          copyright: '© 2024 lingtube.net. All rights reserved.'
        },
        
        // 通用
        common: {
          close: 'Close',
          loading: 'Loading...',
          error: 'Error',
          success: 'Success',
          confirm: 'Confirm',
          cancel: 'Cancel'
        }
      }
    };
    
    // 从localStorage加载语言偏好
    this.loadLanguagePreference();
  }
  
  // 加载语言偏好
  loadLanguagePreference() {
    const savedLang = localStorage.getItem('preferred-language');
    if (savedLang && this.translations[savedLang]) {
      this.currentLanguage = savedLang;
    }
  }
  
  // 保存语言偏好
  saveLanguagePreference(lang) {
    localStorage.setItem('preferred-language', lang);
  }
  
  // 获取翻译文本
  t(key) {
    const keys = key.split('.');
    let value = this.translations[this.currentLanguage];
    
    for (const k of keys) {
      if (value && typeof value === 'object') {
        // 支持数组索引
        if (Array.isArray(value) && !isNaN(k)) {
          value = value[parseInt(k)];
        } else if (k in value) {
          value = value[k];
        } else {
          console.warn(`Translation key not found: ${key}`);
          return key;
        }
      } else {
        console.warn(`Translation key not found: ${key}`);
        return key;
      }
    }
    
    // 确保返回的是字符串，如果是对象则返回原键名
    if (typeof value === 'string') {
      return value;
    } else if (typeof value === 'object') {
      console.warn(`Translation value is an object for key: ${key}`, value);
      return key;
    }
    
    return value || key;
  }
  
  // 切换语言
  switchLanguage(lang) {
    if (this.translations[lang]) {
      this.currentLanguage = lang;
      this.saveLanguagePreference(lang);
      this.updatePageContent();
      this.updatePageTitle();
      
      // 触发语言切换事件
      window.dispatchEvent(new CustomEvent('languageChanged', {
        detail: { language: lang }
      }));
    }
  }
  
  // 更新页面标题
  updatePageTitle() {
    document.title = this.t('page.title');
    
    // 更新meta description
    const metaDesc = document.querySelector('meta[name="description"]');
    if (metaDesc) {
      metaDesc.setAttribute('content', this.t('page.description'));
    }
  }
  
  // 更新页面内容
  updatePageContent() {
    // 更新所有带有data-i18n属性的元素
    const elements = document.querySelectorAll('[data-i18n]');
    elements.forEach(element => {
      const key = element.getAttribute('data-i18n');
      const translation = this.t(key);
      
      if (element.hasAttribute('data-i18n-html')) {
        element.innerHTML = translation;
      } else {
        element.textContent = translation;
      }
    });
    
    // 更新placeholder
    const placeholderElements = document.querySelectorAll('[data-i18n-placeholder]');
    placeholderElements.forEach(element => {
      const key = element.getAttribute('data-i18n-placeholder');
      element.setAttribute('placeholder', this.t(key));
    });
    
    // 更新alt属性
    const altElements = document.querySelectorAll('[data-i18n-alt]');
    altElements.forEach(element => {
      const key = element.getAttribute('data-i18n-alt');
      element.setAttribute('alt', this.t(key));
    });
    
    // 更新aria-label
    const ariaElements = document.querySelectorAll('[data-i18n-aria]');
    ariaElements.forEach(element => {
      const key = element.getAttribute('data-i18n-aria');
      element.setAttribute('aria-label', this.t(key));
    });
  }
  
  // 获取当前语言
  getCurrentLanguage() {
    return this.currentLanguage;
  }
  
  // 获取所有可用语言
  getAvailableLanguages() {
    return Object.keys(this.translations);
  }
}

// 创建全局i18n实例
window.i18n = new I18n();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  window.i18n.updatePageContent();
  window.i18n.updatePageTitle();
}); 