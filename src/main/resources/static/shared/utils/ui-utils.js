/**
 * UI工具类 - 提供通用的UI操作函数
 */
class UIUtils {
    /**
     * 显示toast消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型: success, error, warning, info
     */
    static showToast(message, type = 'info') {
        let backgroundColor;
        switch (type) {
            case 'success':
                backgroundColor = '#22c55e';
                break;
            case 'error':
                backgroundColor = '#ef4444';
                break;
            case 'warning':
                backgroundColor = '#f59e0b';
                break;
            default:
                backgroundColor = '#6b7280';
        }
        
        Toastify({
            text: message,
            duration: 3000,
            gravity: 'top',
            position: 'center',
            backgroundColor
        }).showToast();
    }

    /**
     * 从渐变背景中提取颜色
     * @param {string} gradientString - CSS渐变字符串
     * @returns {string} 提取的颜色值
     */
    static getColorFromGradient(gradientString) {
        // 提取颜色，格式为 linear-gradient(to top, COLOR 50%, transparent 50%)
        const match = gradientString.match(/#[a-fA-F0-9]{6}|#[a-fA-F0-9]{3}|rgba?\([^)]+\)|hsla?\([^)]+\)/);
        const color = match ? match[0] : '#e5e7eb'; // 默认浅灰色
        
        // 调整颜色透明度，使其适合作为背景色
        if (color.startsWith('#')) {
            return color + '80'; // 添加50%透明度
        } else if (color.startsWith('rgb')) {
            return color.replace(')', ', 0.5)').replace('rgb', 'rgba');
        }
        return color;
    }

    /**
     * 切换分析面板显示/隐藏
     */
    static toggleAnalysisPanel() {
        const mainContainer = document.getElementById('main-container');
        const analysisPanel = document.getElementById('analysis-panel');
        const toggleIcon = document.querySelector('#panel-toggle i');
        
        mainContainer.classList.toggle('is-analysis-open');
        
        if (mainContainer.classList.contains('is-analysis-open')) {
            analysisPanel.style.transform = 'translateX(0)';
            toggleIcon.classList.replace('fa-chevron-left', 'fa-chevron-right');
        } else {
            analysisPanel.style.transform = 'translateX(100%)';
            toggleIcon.classList.replace('fa-chevron-right', 'fa-chevron-left');
        }
    }

    /**
     * 检查鼠标是否在tooltip上
     * @returns {boolean} 是否在tooltip上
     */
    static isMouseOverTooltip() {
        const tooltip = document.getElementById('word-tooltip');
        return tooltip === document.activeElement || 
               tooltip.matches(':hover') || 
               tooltip.contains(document.activeElement);
    }

    /**
     * 初始化推荐场景卡片
     */
    static initScenarioCards() {
        const cards = document.querySelectorAll('.scenario-card');
        cards.forEach(card => {
            card.addEventListener('click', async function() {
                const scenario = this.getAttribute('data-scenario');
                if (scenario) {
                    // 检查Premium权限
                    const permissionResult = await window.DialogPermissions.checkPremiumPermission();
                    
                    if (!permissionResult.success || !permissionResult.isPremium) {
                        // 显示升级提示 - 仅限Premium会员
                        window.DialogPermissions.showUpgradeDialog('场景对话生成');
                        return;
                    }
                    
                    // 将场景填入输入框
                    document.getElementById('scenario-input').value = scenario;
                    // 触发自定义事件，让主模块处理对话生成
                    document.dispatchEvent(new CustomEvent('generateDialog', { 
                        detail: { scenario } 
                    }));
                    // 视觉反馈
                    this.style.backgroundColor = 'rgba(236, 252, 203, 0.6)';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 500);
                    // 滚动到顶部
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                }
            });
            
            // 添加波纹效果
            card.addEventListener('mousedown', function(e) {
                const x = e.pageX - this.offsetLeft;
                const y = e.pageY - this.offsetTop;
                
                const ripple = document.createElement('span');
                ripple.className = 'ripple-effect';
                ripple.style.left = `${x}px`;
                ripple.style.top = `${y}px`;
                
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    }
}

// 导出UIUtils类
window.UIUtils = UIUtils; 