/**
 * TTS管理类 - 负责文本朗读功能
 */
class TTSManager {
    constructor() {
        // 语音配置
        this.voices = {
            japanese: {
            male: 'ja-<PERSON>-KeitaNeural',    // 日语男声(小明)
            female: 'ja-<PERSON>-NanamiNeural'  // 日语女声(小花)
            },
            english: {
                male: 'en-US-GuyNeural',      // 英语男声
                female: 'en-US-JennyNeural'   // 英语女声
            }
        };
        this.defaultRate = 1.0; // 正常语速
    }

    /**
     * 获取当前选择的语言
     */
    getCurrentLanguage() {
        return localStorage.getItem('selectedLanguage') || 'japanese';
    }

    /**
     * 文本朗读功能
     * @param {string} text - 要朗读的文本
     * @param {Event} event - 事件对象
     * @param {HTMLElement} audioElement - 朗读按钮元素
     * @param {string} language - 语言类型
     */
    async speakText(text, event, audioElement, language = null) {
        // 如果有事件对象，阻止事件传播
        if (event) {
            event.stopPropagation();
        }
        
        // 如果没有传入语言参数，获取当前语言
        if (!language) {
            language = this.getCurrentLanguage();
        }
        
        console.log('TTS朗读:', text, '语言:', language);
        
        // 重置所有朗读按钮的状态
        if (audioElement) {
            this.resetAllSpeakButtons();
            
            // 设置当前点击的按钮状态
            this.setButtonState(audioElement, false);
        }
        
        // 确定当前对话气泡的位置（左侧/右侧）以选择合适的声音
        const voice = this.getVoiceForElement(audioElement, language);
        
        // 显示朗读状态提示
        UIUtils.showToast('正在朗读...', 'info');
        
        try {
            // 调用后端TTS接口
            const response = await fetch('/api/tts/speak', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text: text,
                    voice: voice,
                    rate: this.defaultRate
                })
            });

            if (!response.ok) {
                throw new Error('TTS请求失败: ' + response.status);
            }

            const arrayBuffer = await response.arrayBuffer();
            
            // 播放音频
            await this.playAudio(arrayBuffer, audioElement);
            
        } catch (error) {
            console.error('TTS生成失败:', error);
            if (audioElement) {
                this.setButtonState(audioElement, true);
            }
            UIUtils.showToast('TTS生成失败，请重试', 'error');
        }
    }

    /**
     * 根据元素位置和语言选择合适的语音
     * @param {HTMLElement} audioElement - 朗读按钮元素
     * @param {string} language - 语言类型
     * @returns {string} 语音类型
     */
    getVoiceForElement(audioElement, language = 'japanese') {
        const languageVoices = this.voices[language] || this.voices.japanese;
        let voice = languageVoices.male; // 默认使用男声
        
        // 检查是否是右侧气泡，如果是则使用女声
        if (audioElement) {
            const bubble = audioElement.closest('.chat-bubble');
            if (bubble && bubble.classList.contains('right')) {
                voice = languageVoices.female; // 右侧使用女声
            }
        }
        
        return voice;
    }

    /**
     * 播放音频
     * @param {ArrayBuffer} arrayBuffer - 音频数据
     * @param {HTMLElement} audioElement - 朗读按钮元素
     * @returns {Promise} 播放Promise
     */
    playAudio(arrayBuffer, audioElement) {
        return new Promise((resolve, reject) => {
            // 将音频数据转换为Blob
            const audioBlob = new Blob([arrayBuffer], { type: 'audio/mpeg' });
            const audioUrl = URL.createObjectURL(audioBlob);
            
            // 播放音频
            const audio = new Audio(audioUrl);
            
            audio.onended = () => {
                // 播放结束后恢复按钮状态
                if (audioElement) {
                    this.setButtonState(audioElement, true);
                }
                UIUtils.showToast('朗读完成', 'success');
                // 释放URL对象
                URL.revokeObjectURL(audioUrl);
                resolve();
            };
            
            audio.onerror = (error) => {
                console.error('音频播放错误:', error);
                if (audioElement) {
                    this.setButtonState(audioElement, true);
                }
                UIUtils.showToast('朗读失败', 'error');
                URL.revokeObjectURL(audioUrl);
                reject(error);
            };
            
            // 播放音频
            audio.play().catch(error => {
                console.error('音频播放失败:', error);
                if (audioElement) {
                    this.setButtonState(audioElement, true);
                }
                UIUtils.showToast('朗读失败', 'error');
                URL.revokeObjectURL(audioUrl);
                reject(error);
            });
        });
    }

    /**
     * 重置所有朗读按钮的状态
     */
    resetAllSpeakButtons() {
        document.querySelectorAll('.speak-btn').forEach(btn => {
            this.setButtonState(btn, true);
        });
    }

    /**
     * 设置按钮状态
     * @param {HTMLElement} button - 按钮元素
     * @param {boolean} enabled - 是否启用
     */
    setButtonState(button, enabled) {
        button.style.pointerEvents = enabled ? 'auto' : 'none';
        button.style.opacity = enabled ? '1' : '0.5';
    }

    /**
     * 为朗读按钮添加事件监听器
     * @param {HTMLElement} speakBtn - 朗读按钮
     * @param {string} text - 要朗读的文本
     * @param {string} language - 语言类型
     */
    addSpeakButtonListener(speakBtn, text, language = null) {
        speakBtn.addEventListener('click', (event) => {
            this.speakText(text, event, speakBtn, language);
        });
    }
}

// 导出TTSManager类
window.TTSManager = TTSManager; 