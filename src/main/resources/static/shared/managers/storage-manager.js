/**
 * 存储管理类 - 负责本地存储的读写操作
 */
class StorageManager {
    constructor() {
        this.FAVORITES_KEY = 'favoritedWords';
    }

    /**
     * 保存收藏词组到本地存储
     * @param {Set} favoritedWords - 收藏词组集合
     */
    saveFavorites(favoritedWords) {
        try {
            localStorage.setItem(this.FAVORITES_KEY, JSON.stringify(Array.from(favoritedWords)));
        } catch (e) {
            console.error('保存收藏词组失败:', e);
        }
    }

    /**
     * 从本地存储加载收藏词组
     * @returns {Set} 收藏词组集合
     */
    loadFavorites() {
        const favoritedWords = new Set();
        try {
            const saved = localStorage.getItem(this.FAVORITES_KEY);
            if (saved) {
                const words = JSON.parse(saved);
                words.forEach(word => favoritedWords.add(word));
            }
        } catch (e) {
            console.error('加载收藏词组失败:', e);
        }
        return favoritedWords;
    }

    /**
     * 清除本地存储的收藏词组
     */
    clearFavorites() {
        try {
            localStorage.removeItem(this.FAVORITES_KEY);
        } catch (e) {
            console.error('清除收藏词组失败:', e);
        }
    }

    /**
     * 从服务器加载收藏词组
     * @returns {Promise<Array>} 服务器收藏数据
     */
    async loadFavoritesFromServer() {
        try {
            const response = await fetch('/api/subtitle/list');
            if (!response.ok) {
                throw new Error('从服务器加载收藏失败');
            }
            const data = await response.json();
            console.log('从服务器加载收藏成功', data.length + '条记录');
            return data;
        } catch (error) {
            console.error('加载服务器收藏失败:', error);
            throw error;
        }
    }
}

// 导出StorageManager类
window.StorageManager = StorageManager; 