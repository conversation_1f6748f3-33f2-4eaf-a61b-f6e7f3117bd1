/**
 * 提示框管理类 - 负责词汇悬停提示框和翻译功能
 */
class TooltipManager {
    constructor(favoriteManager) {
        this.favoriteManager = favoriteManager;
        this.isTooltipVisible = false;
        this.activeSpanElement = null;
        this.tooltipShowTimer = null;
        this.tooltipHideTimer = null;
        this.tooltipShowDelay = 250; // 显示延迟时间（毫秒）
        
        this.tooltip = null;
        this.tooltipText = null;
        this.favoriteIcon = null;
    }

    /**
     * 初始化提示框管理器
     */
    init() {
        this.tooltip = document.getElementById('word-tooltip');
        this.tooltipText = document.getElementById('tooltip-text');
        this.favoriteIcon = document.getElementById('favorite-icon');
        
        this.initTooltipEvents();
    }

    /**
     * 初始化提示框相关事件
     */
    initTooltipEvents() {
        // 收藏图标点击事件
        this.favoriteIcon.addEventListener('click', (e) => {
            e.stopPropagation();
            this.favoriteManager.toggleFavorite(this.favoriteIcon);
        });
        
        // 文档点击事件，在点击其他区域时隐藏tooltip
        document.addEventListener('click', (e) => {
            // 如果点击的不是tooltip、tooltip内部元素、或带有pos-类的span，则隐藏tooltip
            if (!this.tooltip.contains(e.target) && 
                !(e.target.tagName === 'SPAN' && e.target.className.includes('pos-'))) {
                this.hideTooltip();
            }
        });

        // 添加tooltip的鼠标进入事件
        this.tooltip.addEventListener('mouseenter', () => {
            this.isTooltipVisible = true;
            // 清除任何可能的隐藏定时器
            if (this.tooltipHideTimer) {
                clearTimeout(this.tooltipHideTimer);
                this.tooltipHideTimer = null;
            }
        });
        
        // 添加tooltip的鼠标离开事件
        this.tooltip.addEventListener('mouseleave', (e) => {
            // 检查是否移到了span元素上
            if (e.relatedTarget && e.relatedTarget.tagName === 'SPAN' && 
                e.relatedTarget.className.includes('pos-')) {
                return; // 如果移到了span元素上，不隐藏
            }
            
            if (this.tooltipHideTimer) clearTimeout(this.tooltipHideTimer);
            this.tooltipHideTimer = setTimeout(() => {
                if (!UIUtils.isMouseOverTooltip() && 
                    !(this.activeSpanElement && this.activeSpanElement.matches(':hover'))) {
                    this.hideTooltip();
                }
            }, 300);
        });
    }

    /**
     * 为带有pos-类的span添加悬停事件
     * @param {HTMLElement} container - 包含span元素的容器
     */
    addHoverEvents(container) {
        const spans = container.querySelectorAll('span[class*="pos-"]');
        
        spans.forEach(span => {
            span.addEventListener('mouseover', (e) => this.handleSpanMouseOver(e));
            span.addEventListener('mouseleave', (e) => this.handleSpanMouseLeave(e));
        });
    }

    /**
     * 处理span元素鼠标悬停事件
     * @param {Event} e - 鼠标事件
     */
    handleSpanMouseOver(e) {
        // 清除可能存在的隐藏定时器
        if (this.tooltipHideTimer) {
            clearTimeout(this.tooltipHideTimer);
            this.tooltipHideTimer = null;
        }
        
        // 如果已经有显示定时器，但是鼠标移动到了另一个词组，则取消之前的定时器
        if (this.tooltipShowTimer && this.activeSpanElement !== e.target) {
            clearTimeout(this.tooltipShowTimer);
            this.tooltipShowTimer = null;
        }
        
        // 如果不存在显示定时器，创建一个新的
        if (!this.tooltipShowTimer) {
            // 设置当前活动的span
            this.activeSpanElement = e.target;
            
            // 获取当前词组文本（原始日语）
            const currentJapaneseWord = e.target.textContent;
            this.favoriteManager.setCurrentWord(currentJapaneseWord);
            
            // 创建延时显示定时器
            this.tooltipShowTimer = setTimeout(() => {
                // 只有在鼠标仍然在该元素上时才显示tooltip
                if (this.activeSpanElement && this.activeSpanElement.matches(':hover')) {
                    this.showTooltip(this.activeSpanElement, currentJapaneseWord);
                }
                
                // 清除定时器
                this.tooltipShowTimer = null;
            }, this.tooltipShowDelay);
        }
    }

    /**
     * 处理span元素鼠标离开事件
     * @param {Event} e - 鼠标事件
     */
    handleSpanMouseLeave(e) {
        // 如果存在显示定时器，取消它
        if (this.tooltipShowTimer) {
            clearTimeout(this.tooltipShowTimer);
            this.tooltipShowTimer = null;
        }
        
        // 检查是否移出到tooltip上
        if (e.relatedTarget === this.tooltip || this.tooltip.contains(e.relatedTarget)) {
            return; // 如果鼠标移到了tooltip上，不做任何处理
        }
        
        // 延迟隐藏，给用户时间移动到tooltip上
        if (this.tooltipHideTimer) clearTimeout(this.tooltipHideTimer);
        this.tooltipHideTimer = setTimeout(() => {
            // 如果鼠标已经移到tooltip上，则不隐藏
            if (UIUtils.isMouseOverTooltip()) {
                return;
            }
            this.hideTooltip();
        }, 300);
    }

    /**
     * 显示提示框
     * @param {HTMLElement} spanElement - 触发的span元素
     * @param {string} word - 词组文本
     */
    async showTooltip(spanElement, word) {
        // 先设置显示以便获取正确的偏移高度
        this.tooltip.style.visibility = 'hidden'; // 隐藏以便计算尺寸
        this.tooltip.classList.remove('hidden');   // 移除 display:none 样式
        
        // 获取元素位置并计算tooltip位置
        const rect = spanElement.getBoundingClientRect();
        const tooltipHeight = this.tooltip.offsetHeight;
        
        // 计算tooltip的位置（在词组上方）
        this.tooltip.style.left = `${rect.left}px`;
        this.tooltip.style.top = `${rect.top - tooltipHeight - 8}px`;
        
        // 设置便签背景色与词组背景色相同
        const computedStyle = window.getComputedStyle(spanElement);
        const bgImage = computedStyle.backgroundImage;
        if (bgImage && bgImage !== 'none') {
            const color = UIUtils.getColorFromGradient(bgImage);
            this.tooltip.style.backgroundColor = color;
            this.tooltip.style.borderColor = color;
            // 调整文本颜色为深色以确保可读性
            this.tooltip.style.color = '#333';
        }
        
        // 显示加载提示并使tooltip可见
        this.tooltipText.textContent = '翻译中...';
        this.tooltip.style.visibility = 'visible';
        this.isTooltipVisible = true;

        // 更新收藏图标状态
        this.favoriteManager.updateFavoriteIconState(this.favoriteIcon, word, bgImage);
        
        // 进行翻译
        await this.translateWord(word);
    }

    /**
     * 隐藏提示框
     */
    hideTooltip() {
        this.tooltip.classList.add('hidden');
        this.isTooltipVisible = false;
        this.activeSpanElement = null;
    }

    /**
     * 翻译词组
     * @param {string} word - 要翻译的词组
     */
    async translateWord(word) {
        try {
            // 获取当前选择的语言
            const selectedLanguage = await this.getSelectedLanguage();
            
            let wordToTranslate = word;
            
            // 根据语言选择不同的处理策略
            if (selectedLanguage === 'japanese') {
                // 日语：先进行词形还原，再翻译
                const normalizeResponse = await fetch('/api/subtitle/normalize', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ word })
                });
                const normalizeData = await normalizeResponse.json();
                
                // 获取原形结果
                wordToTranslate = normalizeData.success ? normalizeData.originalWord : word;
                
                if (wordToTranslate !== word) {
                    console.log(`翻译前词形还原: ${word} -> ${wordToTranslate}`);
                }
            }
            // 英语：直接翻译，跳过词形还原步骤
            
            // 调用翻译接口，传入语言参数
            const translateResponse = await fetch('/api/translate/word', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `text=${encodeURIComponent(wordToTranslate)}&language=${encodeURIComponent(selectedLanguage)}`
            });
            const data = await translateResponse.json();
            
            // 显示翻译结果
            if (data.success && data.translation) {
                this.tooltipText.textContent = data.translation;
                console.log(`翻译成功: ${word} -> ${data.translation} (语言: ${selectedLanguage}, 源语言: ${data.fromLanguage})`);
            } else {
                console.error('翻译失败:', data.error || '未知错误');
                this.tooltipText.textContent = word; // 回退到原文
            }
        } catch (error) {
            console.error('翻译请求失败:', error);
            this.tooltipText.textContent = word; // 回退到原文
        }
    }

    /**
     * 获取当前选择的语言
     */
    async getSelectedLanguage() {
        return new Promise((resolve) => {
            // 尝试从Chrome扩展存储获取
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.sync.get(['selectedVideoLanguage'], function(result) {
                    const language = result.selectedVideoLanguage || 'japanese'; // 默认为日语
                    resolve(language);
                });
            } else {
                // 如果不在Chrome扩展环境中，从localStorage获取或使用默认值
                const language = localStorage.getItem('selectedLanguage') || 'japanese';
                resolve(language);
            }
        });
    }
}

// 导出TooltipManager类
window.TooltipManager = TooltipManager; 