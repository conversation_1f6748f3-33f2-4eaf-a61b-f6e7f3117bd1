/**
 * 对话管理类 - 负责对话生成和渲染功能
 */
class DialogManager {
    constructor(tooltipManager, ttsManager, analysisManager) {
        this.tooltipManager = tooltipManager;
        this.ttsManager = ttsManager;
        this.analysisManager = analysisManager;
    }

    /**
     * 获取当前选择的语言
     */
    getCurrentLanguage() {
        return localStorage.getItem('selectedLanguage') || 'japanese';
    }

    /**
     * 生成对话
     * @param {string} scenario - 对话场景
     */
    async generateDialog(scenario) {
        // 检查Premium权限
        const permissionResult = await window.DialogPermissions.checkPremiumPermission();
        
        if (!permissionResult.success || !permissionResult.isPremium) {
            // 显示升级提示 - 仅限Premium会员
            window.DialogPermissions.showUpgradeDialog('场景对话生成');
            return;
        }
        
        const loadingContainer = document.getElementById('loading-container');
        const dialogContainer = document.getElementById('dialog-container');
        const scenarioTitle = document.getElementById('scenario-title');
        const recommendedScenarios = document.getElementById('recommended-scenarios');
        
        // 显示加载动画，隐藏对话内容和推荐场景
        loadingContainer.classList.remove('hidden');
        dialogContainer.classList.add('hidden');
        recommendedScenarios.classList.add('hidden');
        
        try {
            // 获取当前选择的语言
            const currentLanguage = this.getCurrentLanguage();
            console.log('当前选择的语言:', currentLanguage);
            
            // 调用后端接口生成对话，传递语言参数
            const response = await fetch(`/api/dialog/generate`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `scenario=${encodeURIComponent(scenario)}&language=${encodeURIComponent(currentLanguage)}`
            });
            
            const data = await response.json();
            
            if (data.success) {
                // 更新场景标题
                scenarioTitle.textContent = `场景:${scenario}`;
                
                // 生成对话内容
                this.renderDialog(data.dialog, currentLanguage);
                
                // 隐藏加载动画和推荐场景，显示对话内容
                loadingContainer.classList.add('hidden');
                dialogContainer.classList.remove('hidden');
                recommendedScenarios.classList.add('hidden');
            } else {
                throw new Error(data.error || '生成对话失败');
            }
        } catch (error) {
            console.error('生成对话失败:', error);
            UIUtils.showToast('生成对话失败，请重试', 'error');
            loadingContainer.classList.add('hidden');
            recommendedScenarios.classList.remove('hidden');
        }
    }

    /**
     * 渲染对话内容
     * @param {Array} dialogData - 对话数据
     * @param {string} language - 当前语言
     */
    renderDialog(dialogData, language = 'japanese') {
        const chatContainer = document.getElementById('chat-container');
        chatContainer.innerHTML = '';
        
        dialogData.forEach((item, index) => {
            const isLeft = item.speaker === 'A';
            const wrapperClass = isLeft ? 'left-wrapper' : 'right-wrapper';
            const bubbleClass = isLeft ? 'left' : 'right';
            
            // 创建对话气泡元素
            const wrapper = document.createElement('div');
            wrapper.className = `chat-wrapper ${wrapperClass}`;
            
            // 对话气泡内容
            const bubble = document.createElement('div');
            bubble.className = `chat-bubble ${bubbleClass}`;
            bubble.setAttribute('data-index', index);
            
            // 对话内容
            const content = document.createElement('div');
            content.className = 'chat-content';
            
            // 主语言文本容器（日语或英语）
            const primaryText = document.createElement('div');
            if (language === 'english') {
                primaryText.className = 'chat-english';
                primaryText.setAttribute('data-text', item.japanese); // 复用字段名
                primaryText.setAttribute('data-phonetic', item.kana); // 复用字段名存储音标
            } else {
                primaryText.className = 'chat-japanese';
                primaryText.setAttribute('data-text', item.japanese);
                primaryText.setAttribute('data-kana', item.kana);
            }
            
            // 将主语言文本发送到后端进行词性标注
            this.tagText(item.japanese, primaryText, language);
            
            // 中文翻译
            const chinese = document.createElement('div');
            chinese.className = 'chat-chinese';
            chinese.textContent = item.chinese;
            
            // 添加解析按钮
            const analysisBtn = document.createElement('div');
            analysisBtn.className = 'analysis-btn';
            analysisBtn.innerHTML = '<i class="fas fa-question"></i>';
            this.analysisManager.addAnalysisButtonListener(analysisBtn, item.japanese, language);
            
            // 添加朗读按钮
            const speakBtn = document.createElement('div');
            speakBtn.className = 'speak-btn';
            speakBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
            this.ttsManager.addSpeakButtonListener(speakBtn, item.japanese, language);
            
            // 组装对话气泡
            content.appendChild(primaryText);
            content.appendChild(chinese);
            
            bubble.appendChild(content);
            bubble.appendChild(analysisBtn);
            bubble.appendChild(speakBtn);
            
            wrapper.appendChild(bubble);
            chatContainer.appendChild(wrapper);
        });
    }

    /**
     * 对文本进行词性标注（支持日语和英语）
     * @param {string} text - 文本
     * @param {HTMLElement} container - 容器元素
     * @param {string} language - 语言类型
     */
    async tagText(text, container, language = 'japanese') {
        try {
            const response = await fetch('/api/subtitle/tag', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `text=${encodeURIComponent(text)}&language=${encodeURIComponent(language)}`
            });
            
            const data = await response.json();
            
            let coloredText;
            if (language === 'english') {
                // 英语：单词间用空格分隔，不显示读音
                coloredText = data.tagged_text.map(word => {
                    return `<span class="pos-${word.pos.toLowerCase()}">${word.text}</span>`;
                }).join(' ');
            } else {
                // 日语：显示读音，不用空格分隔
                coloredText = data.tagged_text.map(word => {
                const span = `<span class="pos-${word.pos.toLowerCase()}">${word.text}</span>`;
                return word.reading ? `<ruby>${span}<rt>${word.reading}</rt></ruby>` : span;
            }).join('');
            }
            
            container.innerHTML = coloredText;
            
            // 为每个带有pos-类的span添加悬停事件
            this.tooltipManager.addHoverEvents(container);
            
            // 如果是右侧对话气泡，添加蒙版
            this.addCoverForRightBubble(container);
            
        } catch (error) {
            console.error('词性标注失败:', error);
            container.textContent = text;
        }
    }

    /**
     * 为右侧对话气泡添加蒙版
     * @param {HTMLElement} container - 主语言文本容器
     */
    addCoverForRightBubble(container) {
        const bubble = container.closest('.chat-bubble');
        if (bubble && bubble.classList.contains('right')) {
            // 获取chat-content元素，它包含了主语言和汉语部分
            const content = container.closest('.chat-content');
            
            const cover = document.createElement('div');
            cover.className = 'chat-cover';
            cover.textContent = '点击显示'; 
            
            // 设置蒙版样式
            cover.style.position = 'absolute';
            cover.style.top = '0';
            cover.style.left = '0';
            cover.style.right = '0';
            cover.style.bottom = 'auto';
            cover.style.zIndex = '10';
            
            // 设置蒙版高度为主语言部分的高度
            cover.style.height = container.offsetHeight + 'px';
            
            // 点击蒙版显示主语言内容
            cover.addEventListener('click', function(e) {
                e.stopPropagation();
                this.style.display = 'none';
            });
            
            // 确保content有相对定位以便蒙版定位正确
            content.style.position = 'relative';
            // 将蒙版插入到content的第一个子元素前面
            content.insertBefore(cover, content.firstChild);
        }
    }

    /**
     * 初始化搜索功能
     */
    initSearchFunction() {
        const searchBtn = document.getElementById('search-btn');
        const scenarioInput = document.getElementById('scenario-input');
        
        // 搜索按钮点击事件
        searchBtn.addEventListener('click', () => {
            const scenario = scenarioInput.value.trim();
            if (scenario) {
                this.generateDialog(scenario);
            } else {
                UIUtils.showToast('请输入对话场景', 'error');
            }
        });
        
        // 回车键搜索
        scenarioInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                searchBtn.click();
            }
        });

        // 监听自定义事件（来自场景卡片点击）
        document.addEventListener('generateDialog', (e) => {
            this.generateDialog(e.detail.scenario);
        });
    }
}

// 导出DialogManager类
window.DialogManager = DialogManager; 