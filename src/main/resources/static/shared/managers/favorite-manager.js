/**
 * 收藏管理类 - 负责词组收藏功能的管理
 */
class FavoriteManager {
    constructor(storageManager) {
        this.storageManager = storageManager;
        this.favoritedWords = new Set();
        this.currentJapaneseWord = '';
    }

    /**
     * 初始化收藏管理器
     */
    async init() {
        // 从本地存储加载收藏
        this.favoritedWords = this.storageManager.loadFavorites();
        
        // 从服务器加载收藏并合并
        try {
            const serverData = await this.storageManager.loadFavoritesFromServer();
            serverData.forEach(item => {
                if (item.word) {
                    this.favoritedWords.add(item.word);
                }
            });
            // 更新本地存储
            this.storageManager.saveFavorites(this.favoritedWords);
        } catch (error) {
            // 出错时仍使用本地存储的数据，不影响用户体验
            console.warn('服务器收藏加载失败，使用本地数据');
        }
    }

    /**
     * 获取当前选择的语言
     */
    async getCurrentLanguage() {
        return new Promise((resolve) => {
            // 尝试从Chrome扩展存储获取
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.sync.get(['selectedVideoLanguage'], function(result) {
                    const language = result.selectedVideoLanguage || 'japanese'; // 默认为日语
                    resolve(language);
                });
            } else {
                // 如果不在Chrome扩展环境中，从localStorage获取或使用默认值
                const language = localStorage.getItem('selectedLanguage') || 'japanese';
                resolve(language);
            }
        });
    }

    /**
     * 设置当前日语词组
     * @param {string} word - 日语词组
     */
    setCurrentWord(word) {
        this.currentJapaneseWord = word;
    }

    /**
     * 检查词组是否已收藏
     * @param {string} word - 词组
     * @returns {boolean} 是否已收藏
     */
    isFavorited(word) {
        return this.favoritedWords.has(word);
    }

    /**
     * 切换收藏状态
     * @param {HTMLElement} favoriteIcon - 收藏图标元素
     */
    async toggleFavorite(favoriteIcon) {
        const wordText = this.currentJapaneseWord;
        
        if (favoriteIcon.classList.contains('far')) {
            // 添加到收藏
            await this.addToFavorites(favoriteIcon, wordText);
        } else {
            // 从收藏中移除
            await this.removeFromFavorites(favoriteIcon, wordText);
        }
    }

    /**
     * 添加到收藏
     * @param {HTMLElement} favoriteIcon - 收藏图标元素
     * @param {string} wordText - 词组文本
     */
    async addToFavorites(favoriteIcon, wordText) {
        // 更新UI状态
        favoriteIcon.classList.remove('far', 'text-gray-400');
        favoriteIcon.classList.add('fas', 'text-red-500');
        favoriteIcon.style.color = '#ef4444';
        
        // 添加到本地收藏
        this.favoritedWords.add(wordText);
        UIUtils.showToast('收藏成功！', 'success');
        
        // 保存到本地存储
        this.storageManager.saveFavorites(this.favoritedWords);
        
        try {
            // 获取当前选择的语言
            const currentLanguage = await this.getCurrentLanguage();
            
            // 先获取词组的原形（辞书形），再发送到后端保存
            const normalizeResponse = await fetch('/api/subtitle/normalize', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ word: wordText })
            });
            const normalizeData = await normalizeResponse.json();
            
            // 获取原形结果
            const wordToStore = normalizeData.success ? normalizeData.originalWord : wordText;
            console.log(`词形还原: ${wordText} -> ${wordToStore}`);
            
            // 现在使用原形发送到后端保存到数据库，并传递语言参数
            const favoriteResponse = await fetch('/api/subtitle/favorite', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    word: wordToStore,
                    lang: currentLanguage
                })
            });
            const data = await favoriteResponse.json();
            
            if (data.success) {
                console.log('后端收藏成功', data);
                if (data.taskId) {
                    // 保存任务ID，以便在用户取消时能够停止任务
                    favoriteIcon.setAttribute('data-task-id', data.taskId);
                }
            } else {
                console.error('后端收藏失败:', data.message);
                // 保留本地收藏状态，不影响用户体验
            }
        } catch (error) {
            console.error('后端收藏请求失败:', error);
            // 保留本地收藏状态，不影响用户体验
        }
    }

    /**
     * 从收藏中移除
     * @param {HTMLElement} favoriteIcon - 收藏图标元素
     * @param {string} wordText - 词组文本
     */
    async removeFromFavorites(favoriteIcon, wordText) {
        // 更新UI状态
        favoriteIcon.classList.remove('fas', 'text-red-500');
        favoriteIcon.classList.add('far', 'text-gray-400');
        favoriteIcon.style.color = '#9ca3af';
        
        // 从本地收藏中移除
        this.favoritedWords.delete(wordText);
        UIUtils.showToast('已取消收藏', 'info');
        
        // 保存到本地存储
        this.storageManager.saveFavorites(this.favoritedWords);
        
        try {
            // 获取当前选择的语言
            const currentLanguage = await this.getCurrentLanguage();
            
            // 先获取词组的原形，再从后端数据库中移除
            const normalizeResponse = await fetch('/api/subtitle/normalize', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ word: wordText })
            });
            const normalizeData = await normalizeResponse.json();
            
            // 获取原形结果
            const wordToRemove = normalizeData.success ? normalizeData.originalWord : wordText;
            console.log(`词形还原: ${wordText} -> ${wordToRemove}`);
            
            const taskId = favoriteIcon.getAttribute('data-task-id');
            
            // 使用原形从后端数据库中移除，并传递语言参数
            const unfavoriteResponse = await fetch('/api/subtitle/unfavorite', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    word: wordToRemove,
                    taskId: taskId, // 可能为null，这样后端只会删除数据库中的记录而不停止任务
                    lang: currentLanguage
                })
            });
            const data = await unfavoriteResponse.json();
            
            if (data.success) {
                console.log('后端取消收藏成功', data);
                // 清除任务ID
                favoriteIcon.removeAttribute('data-task-id');
            } else {
                console.error('后端取消收藏失败:', data.message);
                // 保留本地取消收藏状态，不影响用户体验
            }
        } catch (error) {
            console.error('后端取消收藏请求失败:', error);
            // 保留本地取消收藏状态，不影响用户体验
        }
    }

    /**
     * 更新收藏图标状态
     * @param {HTMLElement} favoriteIcon - 收藏图标元素
     * @param {string} word - 词组
     * @param {string} bgImage - 背景图像样式
     */
    updateFavoriteIconState(favoriteIcon, word, bgImage = null) {
        if (this.isFavorited(word)) {
            favoriteIcon.classList.remove('far', 'text-gray-400');
            favoriteIcon.classList.add('fas', 'text-red-500');
            favoriteIcon.style.color = '#ef4444';
        } else {
            favoriteIcon.classList.remove('fas', 'text-red-500');
            favoriteIcon.classList.add('far', 'text-gray-400');
            favoriteIcon.style.color = (bgImage && bgImage !== 'none') ? '#333' : '#9ca3af';
        }
    }
}

// 导出FavoriteManager类
window.FavoriteManager = FavoriteManager; 