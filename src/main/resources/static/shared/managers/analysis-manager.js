/**
 * 分析管理类 - 负责语法分析功能
 */
class AnalysisManager {
    constructor() {
        this.activeDialogText = '';
    }

    /**
     * 获取当前选择的语言
     */
    getCurrentLanguage() {
        return localStorage.getItem('selectedLanguage') || 'japanese';
    }

    /**
     * 分析对话文本
     * @param {string} text - 要分析的文本
     * @param {string} language - 语言类型
     */
    async analyzeDialog(text, language = null) {
        this.activeDialogText = text;
        
        // 如果没有传入语言参数，获取当前语言
        if (!language) {
            language = this.getCurrentLanguage();
        }
        
        console.log('分析对话文本:', text, '语言:', language);
        
        // 显示分析面板
        const mainContainer = document.getElementById('main-container');
        if (!mainContainer.classList.contains('is-analysis-open')) {
            UIUtils.toggleAnalysisPanel();
        }
        
        const container = document.getElementById('analysis-container');
        this.showLoadingState(container);
        
        try {
            // 调用与插件相同的接口和参数格式
            const response = await fetch('/api/subtitle/analyze', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `ja=${encodeURIComponent(text)}&zh=&language=${encodeURIComponent(language)}`
            });
            
            const data = await response.json();
            this.renderAnalysisResult(container, data.markdown, language);
            UIUtils.showToast('分析完成！', 'success');
            
        } catch (error) {
            console.error('分析失败:', error);
            this.showErrorState(container);
            UIUtils.showToast('分析失败，请重试', 'error');
        }
    }

    /**
     * 显示加载状态
     * @param {HTMLElement} container - 容器元素
     */
    showLoadingState(container) {
        container.innerHTML = `
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <div class="loading-text">
                    AI正在分析中<span class="loading-dots"></span>
                </div>
            </div>
        `;
    }

    /**
     * 显示错误状态
     * @param {HTMLElement} container - 容器元素
     */
    showErrorState(container) {
        container.innerHTML = '<p class="analysis-item p-4 rounded-lg bg-gray-50">等待分析...</p>';
    }

    /**
     * 渲染分析结果
     * @param {HTMLElement} container - 容器元素
     * @param {string} markdownContent - Markdown内容
     * @param {string} language - 语言类型
     */
    renderAnalysisResult(container, markdownContent, language = 'japanese') {
        let processedContent;
        
        if (language === 'english') {
            // 英语：为固定搭配和俚语添加彩色复选框
            processedContent = this.processEnglishAnalysisContent(markdownContent);
        } else {
            // 日语：为语法点添加彩色复选框（保持原有逻辑）
            processedContent = this.processJapaneseAnalysisContent(markdownContent);
        }
        
        const htmlContent = DOMPurify.sanitize(marked.parse(processedContent));
        
        container.innerHTML = '';
        const item = document.createElement('div');
        item.className = 'analysis-item p-4 border-b border-gray-200 markdown-content bg-white rounded-lg shadow-sm fade-in';
        item.innerHTML = htmlContent;
        container.prepend(item);
        container.scrollTop = 0;
    }

    /**
     * 处理日语分析内容（保持原有逻辑）
     */
    processJapaneseAnalysisContent(content) {
        // 定义随机颜色数组
        const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899'];
        // 计数器，用于为每个复选框生成唯一 ID
        let checkboxCounter = 0;
        
        // 在每个 ▲ 前添加复选框
        return content.replace(/▲/g, () => {
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            checkboxCounter++;
            return `<input type="checkbox" class="custom-checkbox" id="checkbox-${checkboxCounter}" style="color: ${randomColor};" data-content-id="${checkboxCounter}"><label for="checkbox-${checkboxCounter}" class="inline-block align-middle">▲</label>`;
        });
    }

    /**
     * 处理英语分析内容 - 与插件完全一致的逻辑
     */
    processEnglishAnalysisContent(content) {
        const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899'];
        let checkboxCounter = 0;
        
        // 第一步：先识别固定搭配和俚语的具体内容行，添加复选框（在添加颜色之前）
        const lines = content.split('\n').map(line => line.replace(/\r/g, ''));
        let inPhraseSection = false;
        let sectionType = '';
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // 检查是否是固定搭配或俚语的标题行
            if (line.includes('▲固定搭配') || line.includes('▲俚语') || line.includes('▲惯用表达')) {
                inPhraseSection = true;
                sectionType = line.includes('固定搭配') ? '固定搭配' : 
                             line.includes('俚语') ? '俚语' : '惯用表达';
                continue;
            }
            
            // 检查是否进入了新的语法部分（以▲开头，但不是固定搭配/俚语/惯用表达）
            if (line.startsWith('▲') && 
                !line.includes('固定搭配') && !line.includes('俚语') && !line.includes('惯用表达')) {
                inPhraseSection = false;
                continue;
            }
            
            // 如果在固定搭配/俚语部分，且这行包含具体内容（有冒号分隔）
            if (inPhraseSection && line && 
                line.includes(':') && 
                !line.startsWith('▲')) {
                
                const randomColor = colors[Math.floor(Math.random() * colors.length)];
                checkboxCounter++;
                
                // 在具体内容前添加复选框（不包含三角符号）
                lines[i] = `<input type="checkbox" class="custom-checkbox phrase-content" id="phrase-${checkboxCounter}" style="color: ${randomColor};" data-content-id="${checkboxCounter}"> ${line}`;
            }
        }
        
        // 第二步：将换行符转换为<br>
        content = lines.join('<br>');
        
        // 第三步：为所有剩余的▲符号添加随机颜色
        content = content.replace(/▲/g, () => {
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            return `<span style="color: ${randomColor}; font-weight: bold;">▲</span>`;
        });
        
        return content;
    }

    /**
     * 为分析按钮添加事件监听器
     * @param {HTMLElement} analysisBtn - 分析按钮
     * @param {string} text - 要分析的文本
     * @param {string} language - 语言类型
     */
    addAnalysisButtonListener(analysisBtn, text, language = null) {
        analysisBtn.addEventListener('click', () => {
            this.analyzeDialog(text, language);
            analysisBtn.innerHTML = '<i class="fas fa-check"></i>';
            analysisBtn.classList.add('active');
        });
    }

    /**
     * 获取当前活动的对话文本
     * @returns {string} 当前对话文本
     */
    getActiveDialogText() {
        return this.activeDialogText;
    }
}

// 导出AnalysisManager类
window.AnalysisManager = AnalysisManager; 