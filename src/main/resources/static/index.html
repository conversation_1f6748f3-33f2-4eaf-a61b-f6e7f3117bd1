<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>lingtube.net - AI驱动的YouTube语言学习平台</title>
  <!-- 立即加载暗色模式脚本，防止页面闪烁 -->
  <script src="/shared/utils/dark-mode.js"></script>
  <!-- 国际化脚本 -->
  <script src="/shared/utils/i18n.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link rel="icon" type="image/png" href="/favicon.png">
  <link rel="stylesheet" href="/assets/css/dark-mode.css">
  <link rel="stylesheet" href="/assets/css/index.css">
  <!-- Paddle JS SDK -->
  <script src="https://cdn.paddle.com/paddle/v2/paddle.js"></script>
</head>
<body>
<!-- 简洁背景 -->

<!-- 固定Header -->
<header class="header fixed top-0 left-0 right-0 z-50">
  <div class="container mx-auto px-6 py-4">
    <div class="header-container">
      <!-- Logo和品牌 -->
      <div class="header-logo flex items-center space-x-3">
        <img src="/favicon.png" alt="lingtube.net" class="w-6 h-6">
        <span class="brand-text text-white text-xl font-bold">lingtube.net</span>
      </div>
      
      <!-- 桌面端导航 -->
      <nav class="header-nav hidden md:flex items-center justify-center space-x-8">
        <a href="#features" class="nav-link text-white hover:text-yellow-300 transition-colors" data-i18n="nav.features">功能特点</a>
        <a href="#feature-showcase" class="nav-link text-white hover:text-yellow-300 transition-colors" data-i18n="nav.showcase">功能演示</a>
        <a href="#pricing" class="nav-link text-white hover:text-yellow-300 transition-colors" data-i18n="nav.pricing">定价</a>
        <a href="#tech-support" class="nav-link text-white hover:text-yellow-300 transition-colors" data-i18n="nav.techSupport">技术支持</a>
        <a href="#faq" class="nav-link text-white hover:text-yellow-300 transition-colors" data-i18n="nav.faq">常见问题</a>
      </nav>
      
      <!-- 登录区域 -->
      <div class="header-actions flex items-center justify-end space-x-4">
        <!-- 语言切换按钮 -->
        <div class="language-toggle-container">
          <button id="language-toggle" class="language-toggle">
            <span class="language-text">中</span>
            <i class="fas fa-globe text-sm ml-1"></i>
          </button>
        </div>
        
        <!-- 黑暗模式切换滑块 -->
        <div class="theme-toggle-container">
          <label class="theme-toggle" for="dark-mode-toggle">
            <input type="checkbox" id="dark-mode-toggle" class="sr-only">
            <div class="toggle-slider">
              <i class="fas fa-sun toggle-icon sun-icon"></i>
              <i class="fas fa-moon toggle-icon moon-icon"></i>
              <div class="toggle-circle"></div>
            </div>
          </label>
        </div>
        
    <!-- 登录按钮 (未登录状态显示) -->
    <div id="login-button" class="relative">
          <a href="/oauth2/authorization/google" class="btn-secondary flex items-center justify-center">
            <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google logo" class="w-4 h-4 mr-2">
        <span data-i18n="nav.login">谷歌登录</span>
      </a>
      <!-- 浮动登录提示 -->
      <div id="login-hint" class="absolute top-full mt-3 left-1/2 -translate-x-1/2 w-72 bg-white rounded-lg shadow-xl p-4 transform transition-all duration-300 scale-0 origin-top z-50">
        <div class="relative">
          <div class="absolute -top-2 left-1/2 -translate-x-1/2 w-0 h-0 border-l-8 border-l-transparent border-r-8 border-r-transparent border-b-8 border-b-white"></div>
          <div class="text-center mb-2">
            <i class="fas fa-lock text-yellow-500 text-xl mb-1"></i>
            <h3 class="text-gray-800 font-medium" data-i18n="nav.loginHint">需要登录</h3>
          </div>
          <p class="text-gray-600 text-sm mb-2 text-center" data-i18n="nav.loginHintDesc">请先登录以使用视频分析功能</p>
        </div>
      </div>
    </div>
    
    <!-- 用户信息 (登录状态显示) -->
    <div id="user-profile" class="hidden profile-wrapper relative">
      <div class="flex items-center cursor-pointer">
            <img id="user-avatar" src="" alt="用户头像" class="w-8 h-8 rounded-full border-2 border-white">
        <span id="user-name" class="ml-2 text-white font-medium"></span>
        <i class="fas fa-chevron-down text-white ml-2"></i>
      </div>
      
      <div class="profile-dropdown py-2">
        <div class="px-4 py-2 border-b border-gray-100">
          <p class="text-sm font-medium text-gray-900" id="dropdown-user-name">用户名</p>
          <p class="text-xs text-gray-500" id="dropdown-user-email">用户邮箱</p>
        </div>
        <a href="/pages/profile/profile.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center group transition-colors">
          <i class="fas fa-user-circle text-cyan-500 mr-3 w-5 text-center group-hover:text-cyan-600"></i>
          <span data-i18n="user.profile">个人主页</span>
        </a>
        <a href="/pages/dialog/dialog.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center group transition-colors">
          <i class="fas fa-comments text-green-500 mr-3 w-5 text-center group-hover:text-green-600"></i>
          <span data-i18n="user.dialog">场景对话</span>
        </a>
        <a href="/pages/review/review.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center group transition-colors">
          <i class="fas fa-book-open text-orange-500 mr-3 w-5 text-center group-hover:text-orange-600"></i>
          <span data-i18n="user.review">单词复习</span>
        </a>
        <div class="border-t border-gray-100 my-1"></div>
        <a href="/api/auth/logout" class="block px-4 py-2 text-red-500 hover:bg-red-50 flex items-center group transition-colors font-medium">
          <i class="fas fa-sign-out-alt mr-3 w-5 text-center group-hover:text-red-600"></i> 
          <span data-i18n="user.logout">退出登录</span>
        </a>
      </div>
    </div>
        
        <!-- 移动端菜单按钮 -->
        <button id="mobile-menu-btn" class="md:hidden text-white text-xl">
          <i class="fas fa-bars"></i>
        </button>
      </div>
    </div>
  </div>
  
  <!-- 移动端菜单 -->
  <div id="mobile-menu" class="mobile-menu md:hidden fixed top-0 right-0 h-full w-64 glass-white z-40">
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <div class="flex items-center space-x-3">
          <!-- 移动端语言切换 -->
          <div class="language-toggle-container">
            <button id="mobile-language-toggle" class="language-toggle">
              <span class="language-text">中</span>
              <i class="fas fa-globe text-sm ml-1"></i>
            </button>
          </div>
          
          <!-- 移动端黑暗模式切换 -->
          <div class="theme-toggle-container">
            <label class="theme-toggle" for="mobile-dark-mode-toggle">
              <input type="checkbox" id="mobile-dark-mode-toggle" class="sr-only mobile-dark-toggle">
              <div class="toggle-slider">
                <i class="fas fa-sun toggle-icon sun-icon"></i>
                <i class="fas fa-moon toggle-icon moon-icon"></i>
                <div class="toggle-circle"></div>
              </div>
            </label>
          </div>
        </div>
        
        <button id="mobile-menu-close" class="text-gray-600 text-xl">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <nav class="space-y-4">
        <a href="#features" class="block text-gray-700 hover:text-blue-600 transition-colors" data-i18n="nav.features">功能特点</a>
        <a href="#feature-showcase" class="block text-gray-700 hover:text-blue-600 transition-colors" data-i18n="nav.showcase">功能演示</a>
        <a href="#pricing" class="block text-gray-700 hover:text-blue-600 transition-colors" data-i18n="nav.pricing">定价</a>
        <a href="#tech-support" class="block text-gray-700 hover:text-blue-600 transition-colors" data-i18n="nav.techSupport">技术支持</a>
        <a href="#faq" class="block text-gray-700 hover:text-blue-600 transition-colors" data-i18n="nav.faq">常见问题</a>
      </nav>
    </div>
  </div>
</header>

<!-- Hero区域 -->
<section class="hero min-h-screen flex items-center justify-center text-center px-6 relative">
  <div class="max-w-5xl mx-auto fade-in">
    <h1 class="hero-title text-5xl md:text-7xl mb-8" data-i18n="hero.title" data-i18n-html="true">
      利用先进的人工智能技术<br>
      <span class="text-gradient">重新定义</span>YouTube语言学习
    </h1>
    <p class="text-gray-600 text-xl md:text-2xl mb-12 leading-relaxed max-w-3xl mx-auto" data-i18n="hero.subtitle" data-i18n-html="true">
      智能字幕分析 • 实时翻译 • 个性化学习路径 • 多语言支持<br>
      让每个YouTube视频都成为您的专属语言老师
    </p>
    <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
      <a href="/pages/profile/profile.html" class="btn-primary">
        <i class="fas fa-rocket mr-2"></i>
        <span data-i18n="hero.startBtn">立即开始学习</span>
      </a>
      <a href="#features" class="btn-secondary">
        <i class="fas fa-play mr-2"></i>
        <span data-i18n="hero.learnMoreBtn">了解更多功能</span>
      </a>
    </div>
    
    <!-- 信任指标 -->
    <div class="mt-16 flex flex-col sm:flex-row items-center justify-center gap-8 text-gray-500 text-sm">
      <div class="flex items-center">
        <i class="fas fa-users text-blue-500 mr-2"></i>
        <span data-i18n="hero.stats.users">10,000+ 活跃学习者</span>
      </div>
      <div class="flex items-center">
        <i class="fas fa-star text-yellow-500 mr-2"></i>
        <span data-i18n="hero.stats.rating">4.9/5 用户评分</span>
      </div>
      <div class="flex items-center">
        <i class="fas fa-globe text-green-500 mr-2"></i>
        <span data-i18n="hero.stats.languages">支持多种语言</span>
      </div>
    </div>
  </div>
  

</section>

<!-- 功能特点 -->
<section id="features" class="py-20 px-6 bg-white">
  <div class="max-w-6xl mx-auto">
    <div class="text-center mb-16 fade-in">
      <span class="text-sm font-semibold text-blue-600 tracking-wide uppercase" data-i18n="features.sectionTitle">核心功能</span>
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4 mt-2" data-i18n="features.title">功能特点</h2>
    </div>
    
    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
      <div class="modern-card p-8 text-center fade-in">
        <div class="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-brain text-2xl icon-gradient"></i>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-4" data-i18n="features.ai.title">AI智能分析</h3>
        <p class="text-gray-600 leading-relaxed" data-i18n="features.ai.desc">自动识别关键词汇和语法点，提供个性化学习建议，让学习更加精准高效</p>
      </div>
      
      <div class="modern-card p-8 text-center fade-in">
        <div class="w-16 h-16 bg-gradient-to-br from-green-100 to-green-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-language text-2xl text-green-600"></i>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-4" data-i18n="features.multilang.title">多语言支持</h3>
        <p class="text-gray-600 leading-relaxed" data-i18n="features.multilang.desc">支持英语、日语等多种语言的学习和翻译，满足不同语言学习需求</p>
      </div>
      
      <div class="modern-card p-8 text-center fade-in">
        <div class="w-16 h-16 bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-closed-captioning text-2xl text-purple-600"></i>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-4" data-i18n="features.subtitle.title">智能字幕</h3>
        <p class="text-gray-600 leading-relaxed" data-i18n="features.subtitle.desc">实时生成和优化字幕，支持多语言同步显示，提升理解效率</p>
      </div>
      
      <div class="modern-card p-8 text-center fade-in">
        <div class="w-16 h-16 bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-chart-line text-2xl text-orange-600"></i>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-4" data-i18n="features.tracking.title">学习跟踪</h3>
        <p class="text-gray-600 leading-relaxed" data-i18n="features.tracking.desc">详细的学习进度分析和个性化复习计划，让进步看得见</p>
      </div>
    </div>
  </div>
</section>

<!-- 功能展示 -->
<section id="feature-showcase" class="py-20 px-6 bg-gray-50">
  <div class="max-w-6xl mx-auto">
    <div class="text-center mb-16 fade-in">
      <span class="text-sm font-semibold text-blue-600 tracking-wide uppercase" data-i18n="showcase.sectionTitle">产品展示</span>
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4 mt-2" data-i18n="showcase.title">功能演示</h2>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto" data-i18n="showcase.subtitle">通过直观的界面展示，了解我们如何让语言学习变得更加高效和有趣</p>
    </div>
    
    <!-- 功能1: AI智能字幕分析 - 左文字右图片 -->
    <div class="grid md:grid-cols-2 gap-12 items-center mb-20 fade-in">
      <div class="order-2 md:order-1">
        <div class="flex items-center mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center mr-4">
            <i class="fas fa-brain text-blue-600 text-xl"></i>
          </div>
          <span class="text-sm font-semibold text-blue-600 tracking-wide uppercase" data-i18n="showcase.feature1.category">AI智能分析</span>
        </div>
        <h3 class="text-3xl font-bold text-gray-900 mb-6" data-i18n="showcase.feature1.title">智能字幕分析与词性标注</h3>
        <p class="text-lg text-gray-600 mb-6 leading-relaxed" data-i18n="showcase.feature1.desc">
          我们的AI系统能够自动识别视频中的关键词汇，进行精确的词性分析和语法标注。通过不同颜色的高亮显示，让您一眼就能识别名词、动词、形容词等词性，大大提升学习效率。
        </p>
        <ul class="space-y-3 text-gray-600">
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature1.features.0">实时词性分析和语法标注</span>
          </li>
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature1.features.1">多彩字幕显示，直观易懂</span>
          </li>
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature1.features.2">支持英语和日语双语分析</span>
          </li>
        </ul>
      </div>
      <div class="order-1 md:order-2">
        <div class="modern-card p-4 bg-white">
          <img src="/assets/images/features/feature-1.png" alt="AI智能字幕分析界面" class="feature-image w-full h-auto rounded-lg shadow-sm">
        </div>
      </div>
    </div>
    
    <!-- 功能2: 实时翻译功能 - 左图片右文字 -->
    <div class="grid md:grid-cols-2 gap-12 items-center mb-20 fade-in">
      <div class="order-1">
        <div class="modern-card p-4 bg-white">
          <img src="/assets/images/features/feature-2.png" alt="实时翻译功能界面" class="feature-image w-full h-auto rounded-lg shadow-sm">
        </div>
      </div>
      <div class="order-2">
        <div class="flex items-center mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center mr-4">
            <i class="fas fa-language text-green-600 text-xl"></i>
          </div>
          <span class="text-sm font-semibold text-green-600 tracking-wide uppercase" data-i18n="showcase.feature2.category">实时翻译</span>
        </div>
        <h3 class="text-3xl font-bold text-gray-900 mb-6" data-i18n="showcase.feature2.title">悬浮翻译与深度解析</h3>
        <p class="text-lg text-gray-600 mb-6 leading-relaxed" data-i18n="showcase.feature2.desc">
          只需将鼠标悬停在任意单词或短语上，即可获得精准的翻译结果。我们的AI不仅提供基础翻译，还会解析语法结构、惯用搭配和文化背景，让您真正理解语言的精髓。
        </p>
        <ul class="space-y-3 text-gray-600">
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature2.features.0">即时悬浮翻译，无需点击</span>
          </li>
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature2.features.1">语法结构和惯用搭配解析</span>
          </li>
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature2.features.2">文化背景和使用场景说明</span>
          </li>
        </ul>
      </div>
    </div>
    
    <!-- 功能3: 个性化学习路径 - 左文字右图片 -->
    <div class="grid md:grid-cols-2 gap-12 items-center mb-20 fade-in">
      <div class="order-2 md:order-1">
        <div class="flex items-center mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center mr-4">
            <i class="fas fa-chart-line text-purple-600 text-xl"></i>
          </div>
          <span class="text-sm font-semibold text-purple-600 tracking-wide uppercase" data-i18n="showcase.feature3.category">学习跟踪</span>
        </div>
        <h3 class="text-3xl font-bold text-gray-900 mb-6" data-i18n="showcase.feature3.title">智能学习进度管理</h3>
        <p class="text-lg text-gray-600 mb-6 leading-relaxed" data-i18n="showcase.feature3.desc">
          系统会自动记录您的学习历史，分析您的学习偏好和薄弱环节。通过数据可视化图表，清晰展示学习进度，并提供个性化的复习建议和学习计划。
        </p>
        <ul class="space-y-3 text-gray-600">
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature3.features.0">详细的学习数据统计分析</span>
          </li>
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature3.features.1">个性化复习计划推荐</span>
          </li>
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature3.features.2">学习成果可视化展示</span>
          </li>
        </ul>
      </div>
      <div class="order-1 md:order-2">
        <div class="modern-card p-4 bg-white">
          <img src="/assets/images/features/feature-3.png" alt="个性化学习路径界面" class="feature-image w-full h-auto rounded-lg shadow-sm">
        </div>
      </div>
    </div>
    
    <!-- 功能4: 场景对话生成 - 左图片右文字 -->
    <div class="grid md:grid-cols-2 gap-12 items-center mb-20 fade-in">
      <div class="order-1">
        <div class="modern-card p-4 bg-white">
          <img src="/assets/images/features/feature-4.png" alt="场景对话生成界面" class="feature-image w-full h-auto rounded-lg shadow-sm">
        </div>
      </div>
      <div class="order-2">
        <div class="flex items-center mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-orange-100 to-orange-200 rounded-xl flex items-center justify-center mr-4">
            <i class="fas fa-comments text-orange-600 text-xl"></i>
          </div>
          <span class="text-sm font-semibold text-orange-600 tracking-wide uppercase" data-i18n="showcase.feature4.category">AI对话</span>
        </div>
        <h3 class="text-3xl font-bold text-gray-900 mb-6" data-i18n="showcase.feature4.title">智能场景对话生成</h3>
        <p class="text-lg text-gray-600 mb-6 leading-relaxed" data-i18n="showcase.feature4.desc">
          基于视频内容和您的学习需求，AI会自动生成相关的生活场景对话。无论是商务会议、日常购物还是旅行交流，都能为您提供贴近实际的对话练习机会。
        </p>
        <ul class="space-y-3 text-gray-600">
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature4.features.0">基于视频内容的智能对话生成</span>
          </li>
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature4.features.1">多种生活场景模拟练习</span>
          </li>
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature4.features.2">Azure神经网络语音合成</span>
          </li>
        </ul>
      </div>
    </div>
    
    <!-- 功能5: AI视频摘要 - 左文字右图片 -->
    <div class="grid md:grid-cols-2 gap-12 items-center mb-20 fade-in">
      <div class="order-2 md:order-1">
        <div class="flex items-center mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-xl flex items-center justify-center mr-4">
            <i class="fas fa-file-alt text-indigo-600 text-xl"></i>
          </div>
          <span class="text-sm font-semibold text-indigo-600 tracking-wide uppercase" data-i18n="showcase.feature5.category">智能摘要</span>
        </div>
        <h3 class="text-3xl font-bold text-gray-900 mb-6" data-i18n="showcase.feature5.title">AI视频内容摘要</h3>
        <p class="text-lg text-gray-600 mb-6 leading-relaxed" data-i18n="showcase.feature5.desc">
          利用先进的自然语言处理技术，AI会自动分析视频内容并生成精准的摘要。无论是长达几小时的讲座还是短小的教学视频，都能快速提取核心要点，节省您的时间。
        </p>
        <ul class="space-y-3 text-gray-600">
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature5.features.0">自动提取视频核心要点</span>
          </li>
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature5.features.1">多层级摘要结构化展示</span>
          </li>
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature5.features.2">支持长视频内容快速理解</span>
          </li>
        </ul>
      </div>
      <div class="order-1 md:order-2">
        <div class="modern-card p-4 bg-white">
          <img src="/assets/images/features/feature-5.png" alt="AI视频摘要界面" class="feature-image w-full h-auto rounded-lg shadow-sm">
        </div>
      </div>
    </div>
    
    <!-- 功能6: 语音合成TTS - 左图片右文字 -->
    <div class="grid md:grid-cols-2 gap-12 items-center mb-20 fade-in">
      <div class="order-1">
        <div class="modern-card p-4 bg-white">
          <img src="/assets/images/features/feature-6.png" alt="语音合成TTS界面" class="feature-image w-full h-auto rounded-lg shadow-sm">
        </div>
      </div>
      <div class="order-2">
        <div class="flex items-center mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-pink-100 to-pink-200 rounded-xl flex items-center justify-center mr-4">
            <i class="fas fa-volume-up text-pink-600 text-xl"></i>
          </div>
          <span class="text-sm font-semibold text-pink-600 tracking-wide uppercase" data-i18n="showcase.feature6.category">语音合成</span>
        </div>
        <h3 class="text-3xl font-bold text-gray-900 mb-6" data-i18n="showcase.feature6.title">高质量语音合成TTS</h3>
        <p class="text-lg text-gray-600 mb-6 leading-relaxed" data-i18n="showcase.feature6.desc">
          采用Azure神经网络语音技术，为您提供接近真人的高质量语音合成服务。支持多种语言和音色选择，让您在学习过程中享受自然流畅的听觉体验。
        </p>
        <ul class="space-y-3 text-gray-600">
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature6.features.0">Azure神经网络拟人发音</span>
          </li>
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature6.features.1">多语言多音色选择</span>
          </li>
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature6.features.2">支持语速和音调调节</span>
          </li>
        </ul>
      </div>
    </div>
    
    <!-- 功能7: 沉浸式学习模式 - 左文字右图片 -->
    <div class="grid md:grid-cols-2 gap-12 items-center fade-in">
      <div class="order-2 md:order-1">
        <div class="flex items-center mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-teal-100 to-teal-200 rounded-xl flex items-center justify-center mr-4">
            <i class="fas fa-eye text-teal-600 text-xl"></i>
          </div>
          <span class="text-sm font-semibold text-teal-600 tracking-wide uppercase" data-i18n="showcase.feature7.category">专注学习</span>
        </div>
        <h3 class="text-3xl font-bold text-gray-900 mb-6" data-i18n="showcase.feature7.title">沉浸式学习模式</h3>
        <p class="text-lg text-gray-600 mb-6 leading-relaxed" data-i18n="showcase.feature7.desc">
          专为语言学习优化的观看模式，去除干扰元素，突出学习重点。支持字幕遮罩、重复播放、语速调节等功能，创造最佳的学习环境，让您全身心投入语言学习。
        </p>
        <ul class="space-y-3 text-gray-600">
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature7.features.0">去除干扰的专注界面</span>
          </li>
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature7.features.1">字幕遮罩和重复播放</span>
          </li>
          <li class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span data-i18n="showcase.feature7.features.2">语速调节和快捷键支持</span>
          </li>
        </ul>
      </div>
      <div class="order-1 md:order-2">
        <div class="modern-card p-4 bg-white">
          <img src="/assets/images/features/feature-7.png" alt="沉浸式学习模式界面" class="feature-image w-full h-auto rounded-lg shadow-sm">
        </div>
      </div>
    </div>
  </div>
</section>

<!-- 定价 -->
<section id="pricing" class="py-20 px-6 bg-white">
  <div class="max-w-6xl mx-auto">
    <div class="text-center mb-16 fade-in">
      <span class="text-sm font-semibold text-orange-600 tracking-wide uppercase" data-i18n="pricing.sectionTitle">智能学习计划</span>
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4 mt-2" data-i18n="pricing.title">选择适合的套餐</h2>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto" data-i18n="pricing.subtitle">升级账户以解锁全部学习功能，加速您的语言学习之旅</p>
      
      <!-- 月付/年付切换 -->
      <div class="mt-8 mb-2 flex justify-center">
        <div class="bg-white rounded-full p-1 flex shadow-sm border border-gray-200">
          <button id="monthly-btn" class="py-2 px-8 rounded-full text-sm font-medium transition-all duration-200 active text-white bg-blue-600" data-i18n="pricing.monthly">月付</button>
          <button id="yearly-btn" class="py-2 px-8 rounded-full text-sm font-medium transition-all duration-200 text-gray-700" data-i18n="pricing.yearly">年付</button>
        </div>
      </div>
      
      <!-- 折扣标签 -->
      <div class="mb-4 flex justify-center">
        <span id="yearly-discount" class="bg-green-100 text-green-800 text-xs px-3 py-1.5 rounded-full font-medium" data-i18n="pricing.yearlyDiscount">年付可省 15%</span>
      </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- 免费版 -->
      <div class="price-card modern-card p-8 text-center fade-in">
        <div class="flex justify-center mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center">
            <i class="fas fa-feather text-gray-600 text-2xl"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 mb-2" data-i18n="pricing.free.title">免费版</h3>
        <p class="text-sm text-gray-600 mb-6" data-i18n="pricing.free.subtitle">入门级基础功能</p>
        
        <div class="price-container mb-8">
          <div class="monthly-price">
            <p class="text-4xl font-bold text-gray-900">¥0<span class="text-base font-normal text-gray-600" data-i18n="pricing.free.period">/月</span></p>
            <p class="text-xs text-gray-500 mt-1" data-i18n="pricing.free.note">无需信用卡</p>
          </div>
          <div class="yearly-price hidden">
            <p class="text-4xl font-bold text-gray-900">¥0<span class="text-base font-normal text-gray-600" data-i18n="pricing.free.yearlyPeriod">/年</span></p>
            <p class="text-xs text-gray-500 mt-1" data-i18n="pricing.free.note">无需信用卡</p>
          </div>
        </div>
        
        <ul class="text-gray-600 mb-8 space-y-3 text-left">
          <li class="flex items-start">
            <svg class="w-5 h-5 text-green-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.free.features.0">注册即送7天premium会员</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-green-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.free.features.1">沉浸式学习体验</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-green-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.free.features.2">英语/日语 双语视频支持</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-green-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.free.features.3">双语字幕功能</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-green-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.free.features.6">弹幕功能</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-green-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.free.features.7">黑暗模式</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-green-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.free.features.8">历史记录功能</span>
          </li>
        </ul>
        
        <button class="w-full btn-secondary" data-i18n="pricing.free.button">
          开始使用
        </button>
      </div>

      <!-- Plus 专业版 -->
      <div class="price-card modern-card p-8 text-center relative border-2 border-blue-400 fade-in">
        <span class="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white text-xs px-4 py-2 font-medium rounded-full" data-i18n="pricing.plus.popular">最受欢迎</span>
        <div class="flex justify-center mb-6 mt-4">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center">
            <i class="fas fa-rocket text-blue-600 text-2xl"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 mb-2" data-i18n="pricing.plus.title">Plus 专业版</h3>
        <p class="text-sm text-gray-600 mb-6" data-i18n="pricing.plus.subtitle">最适合语言学习者</p>
        
        <div class="price-container mb-8">
          <div class="monthly-price">
            <p class="text-4xl font-bold text-gray-900">¥<span class="plus-monthly-price">29</span><span class="text-base font-normal text-gray-600" data-i18n="pricing.plus.period">/月</span></p>
            <p class="text-xs text-gray-500 mt-1" data-i18n="pricing.plus.note">单独购买</p>
          </div>
          <div class="yearly-price hidden">
            <p class="text-4xl font-bold text-gray-900">¥<span class="plus-yearly-price">295</span><span class="text-base font-normal text-gray-600" data-i18n="pricing.plus.yearlyPeriod">/年</span></p>
            <p class="text-xs text-green-600 mt-1"><span data-i18n="pricing.plus.yearlySave">省 ¥</span><span class="plus-yearly-save">53</span><span data-i18n="pricing.plus.yearlyPeriod">/年</span></p>
          </div>
        </div>
        
        <ul class="text-gray-600 mb-8 space-y-3 text-left">
          <li class="flex items-start">
            <svg class="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.plus.features.0">所有免费功能</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.free.features.4">AI多彩字幕功能</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.free.features.5">基于NLP的AI词性分析功能</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.plus.features.1">单词悬浮翻译功能（经过AI还原解析）</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.plus.features.2">AI语法、接续、惯用搭配、俚语等的分析功能</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.plus.features.3">单词、固定搭配、语法接续、俚语等的收藏功能</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.plus.features.4">高质量AI单词解析功能</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.plus.features.5">贴近日常生活的AI高质量例句生成功能</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.plus.features.6">外语视频的中文发音功能（Azure神经网络拟人发音）</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.plus.features.7">英语/日语 双语例句、对话朗读功能（Azure神经网络拟人发音）</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.plus.features.8">数据统计、分析功能</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.plus.features.9">解析蒙版功能</span>
          </li>
        </ul>
        
        <button 
          class="checkout-button w-full btn-primary"
          data-plan-type="plus"
          data-monthly-price-id="YOUR_PLUS_MONTHLY_PRICE_ID"
          data-yearly-price-id="YOUR_PLUS_YEARLY_PRICE_ID"
          data-i18n="pricing.plus.button">
          立即订阅
        </button>
      </div>

      <!-- Premium 尊享版 -->
      <div class="price-card modern-card p-8 text-center fade-in">
        <div class="flex justify-center mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center">
            <i class="fas fa-crown text-purple-600 text-2xl"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 mb-2" data-i18n="pricing.premium.title">Premium 尊享版</h3>
        <p class="text-sm text-gray-600 mb-6" data-i18n="pricing.premium.subtitle">极致学习体验</p>
        
        <div class="price-container mb-8">
          <div class="monthly-price">
            <p class="text-4xl font-bold text-gray-900">¥<span class="premium-monthly-price">59</span><span class="text-base font-normal text-gray-600" data-i18n="pricing.premium.period">/月</span></p>
            <p class="text-xs text-gray-500 mt-1" data-i18n="pricing.premium.note">单独购买</p>
          </div>
          <div class="yearly-price hidden">
            <p class="text-4xl font-bold text-gray-900">¥<span class="premium-yearly-price">595</span><span class="text-base font-normal text-gray-600" data-i18n="pricing.premium.yearlyPeriod">/年</span></p>
            <p class="text-xs text-green-600 mt-1"><span data-i18n="pricing.premium.yearlySave">省 ¥</span><span class="premium-yearly-save">113</span><span data-i18n="pricing.premium.yearlyPeriod">/年</span></p>
          </div>
        </div>
        
        <ul class="text-gray-600 mb-8 space-y-3 text-left">
          <li class="flex items-start">
            <svg class="w-5 h-5 text-purple-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.premium.features.0">所有 Plus 功能</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-purple-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.premium.features.1">AI视频摘要功能</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-purple-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.premium.features.2">AI聊天助手功能（基于视频上下文回答问题）</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-purple-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.premium.features.3">AI无字幕视频转录功能（基于大语言模型）</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-purple-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.premium.features.4">AI词汇、语法、惯用搭配等的联想拓展功能</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-purple-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.premium.features.5">AI任意生活场景的对话生成和解析功能</span>
          </li>
          <li class="flex items-start">
            <svg class="w-5 h-5 text-purple-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span data-i18n="pricing.premium.features.6">优先体验最新功能</span>
          </li>
        </ul>
        
        <button 
          class="checkout-button w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
          data-plan-type="premium"
          data-monthly-price-id="YOUR_PREMIUM_MONTHLY_PRICE_ID"
          data-yearly-price-id="YOUR_PREMIUM_YEARLY_PRICE_ID" data-i18n="pricing.premium.button">
          立即订阅
        </button>
      </div>
    </div>

    <div class="mt-12 text-center text-sm text-white/60">
      <div class="flex justify-center space-x-4 items-center">
        <span data-i18n="pricing.footer.transparent">优惠透明价格</span>
        <span>•</span>
        <span data-i18n="pricing.footer.secure">安全支付</span>
        <span>•</span>
        <span data-i18n="pricing.footer.support">7x24 支持</span>
      </div>
    </div>
  </div>
</section>

<!-- 技术支持 -->
<section id="tech-support" class="py-20 px-6 bg-gray-50">
  <div class="max-w-6xl mx-auto">
    <div class="text-center mb-16 fade-in">
      <span class="text-sm font-semibold text-blue-600 tracking-wide uppercase" data-i18n="techSupport.sectionTitle">合作伙伴</span>
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4 mt-2" data-i18n="techSupport.title">技术支持</h2>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto" data-i18n="techSupport.subtitle">Lingtube由顶尖科技公司提供技术支持</p>
    </div>
    
    <!-- 合作伙伴滚动展示 -->
    <div class="partner-scroll-container fade-in">
      <div class="partner-scroll-track">
        <!-- 第一组logo -->
        <div class="partner-item">
          <img src="/assets/images/partners/openai.png" alt="OpenAI" class="partner-logo">
        </div>
        <div class="partner-item">
          <img src="/assets/images/partners/google.png" alt="Google" class="partner-logo">
        </div>
        <div class="partner-item">
          <img src="/assets/images/partners/deepseek.png" alt="DeepSeek" class="partner-logo">
        </div>
        <div class="partner-item">
          <img src="/assets/images/partners/qwen.png" alt="Qwen" class="partner-logo">
        </div>
        <div class="partner-item">
          <img src="/assets/images/partners/aliyun.png" alt="阿里云" class="partner-logo">
        </div>
        <div class="partner-item">
          <img src="/assets/images/partners/cloudflare.png" alt="Cloudflare" class="partner-logo">
        </div>
        
        <!-- 第二组logo（用于无缝循环） -->
        <div class="partner-item">
          <img src="/assets/images/partners/openai.png" alt="OpenAI" class="partner-logo">
        </div>
        <div class="partner-item">
          <img src="/assets/images/partners/google.png" alt="Google" class="partner-logo">
        </div>
        <div class="partner-item">
          <img src="/assets/images/partners/deepseek.png" alt="DeepSeek" class="partner-logo">
        </div>
        <div class="partner-item">
          <img src="/assets/images/partners/qwen.png" alt="Qwen" class="partner-logo">
        </div>
        <div class="partner-item">
          <img src="/assets/images/partners/aliyun.png" alt="阿里云" class="partner-logo">
        </div>
        <div class="partner-item">
          <img src="/assets/images/partners/cloudflare.png" alt="Cloudflare" class="partner-logo">
        </div>
      </div>
    </div>
  </div>
</section>

<!-- 常见问题 -->
<section id="faq" class="py-20 px-6 bg-white">
  <div class="max-w-3xl mx-auto">
    <div class="text-center mb-16 fade-in">
      <span class="text-sm font-semibold text-blue-600 tracking-wide uppercase" data-i18n="faq.sectionTitle">帮助中心</span>
      <h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4 mt-2" data-i18n="faq.title">常见问题</h2>
      <p class="text-xl text-gray-600" data-i18n="faq.subtitle">快速了解平台使用方法</p>
    </div>
    
    <div class="space-y-4">
      <div class="modern-card overflow-hidden fade-in">
        <button class="faq-toggle w-full text-left p-6 text-gray-900 font-semibold flex justify-between items-center hover:bg-gray-50 transition-colors">
          <span data-i18n="faq.q1.question">如何开始使用lingtube.net？</span>
          <i class="fas fa-chevron-down transition-transform text-gray-400"></i>
        </button>
        <div class="faq-content px-6">
          <p class="text-gray-600" data-i18n="faq.q1.answer">只需使用Google账号登录，然后安装我们的浏览器扩展程序，即可在YouTube上直接使用AI分析功能。</p>
        </div>
      </div>
      
      <div class="modern-card overflow-hidden fade-in">
        <button class="faq-toggle w-full text-left p-6 text-gray-900 font-semibold flex justify-between items-center hover:bg-gray-50 transition-colors">
          <span data-i18n="faq.q2.question">支持哪些语言的学习？</span>
          <i class="fas fa-chevron-down transition-transform text-gray-400"></i>
        </button>
        <div class="faq-content px-6">
          <p class="text-gray-600" data-i18n="faq.q2.answer">目前支持英语和日语的学习，我们正在不断添加更多语言支持，包括韩语、法语、德语等。</p>
        </div>
      </div>
      
      <div class="modern-card overflow-hidden fade-in">
        <button class="faq-toggle w-full text-left p-6 text-gray-900 font-semibold flex justify-between items-center hover:bg-gray-50 transition-colors">
          <span data-i18n="faq.q3.question">是否需要安装特殊软件？</span>
          <i class="fas fa-chevron-down transition-transform text-gray-400"></i>
        </button>
        <div class="faq-content px-6">
          <p class="text-gray-600" data-i18n="faq.q3.answer">只需安装我们的Chrome浏览器扩展程序即可，无需安装其他软件。扩展程序轻量级且安全。</p>
        </div>
      </div>
      
      <div class="modern-card overflow-hidden fade-in">
        <button class="faq-toggle w-full text-left p-6 text-gray-900 font-semibold flex justify-between items-center hover:bg-gray-50 transition-colors">
          <span data-i18n="faq.q4.question">如何取消订阅？</span>
          <i class="fas fa-chevron-down transition-transform text-gray-400"></i>
        </button>
        <div class="faq-content px-6">
          <p class="text-gray-600" data-i18n="faq.q4.answer">您可以随时在个人设置页面取消订阅，取消后仍可使用到当前计费周期结束。</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Footer -->
<footer class="bg-gray-50 border-t border-gray-200 mt-20 py-12 px-6">
  <div class="max-w-6xl mx-auto">
    <div class="grid md:grid-cols-4 gap-8 mb-8">
      <div>
        <div class="flex items-center space-x-2 mb-4">
          <img src="/favicon.png" alt="lingtube.net" class="w-5 h-5">
          <span class="text-gray-900 text-lg font-bold">lingtube.net</span>
        </div>
        <p class="text-gray-600 text-sm leading-relaxed" data-i18n="footer.description">
          利用AI技术让语言学习更高效、更有趣，成为您专属的语言学习助手
        </p>
      </div>
      
      <div>
        <h4 class="text-gray-900 font-semibold mb-4" data-i18n="footer.product">产品</h4>
        <ul class="space-y-3">
          <li><a href="#features" class="text-gray-600 hover:text-blue-600 transition-colors text-sm" data-i18n="footer.links.features">功能特点</a></li>
          <li><a href="#pricing" class="text-gray-600 hover:text-blue-600 transition-colors text-sm" data-i18n="footer.links.pricing">定价方案</a></li>
          <li><a href="/pages/profile/profile.html" class="text-gray-600 hover:text-blue-600 transition-colors text-sm" data-i18n="footer.links.start">开始使用</a></li>
        </ul>
      </div>
      
      <div>
        <h4 class="text-gray-900 font-semibold mb-4" data-i18n="footer.support">支持</h4>
        <ul class="space-y-3">
          <li><a href="#faq" class="text-gray-600 hover:text-blue-600 transition-colors text-sm" data-i18n="footer.links.faq">常见问题</a></li>
          <li><a href="mailto:<EMAIL>" class="text-gray-600 hover:text-blue-600 transition-colors text-sm" data-i18n="footer.links.contact">联系客服</a></li>
          <li><a href="/pages/help" class="text-gray-600 hover:text-blue-600 transition-colors text-sm" data-i18n="footer.links.help">使用帮助</a></li>
        </ul>
      </div>
      
      <div>
        <h4 class="text-gray-900 font-semibold mb-4" data-i18n="footer.legal">法律</h4>
        <ul class="space-y-3">
          <li><a href="/pages/privacy" class="text-gray-600 hover:text-blue-600 transition-colors text-sm" data-i18n="footer.links.privacy">隐私政策</a></li>
          <li><a href="/pages/terms" class="text-gray-600 hover:text-blue-600 transition-colors text-sm" data-i18n="footer.links.terms">服务条款</a></li>
          <li><a href="/pages/cookies" class="text-gray-600 hover:text-blue-600 transition-colors text-sm" data-i18n="footer.links.cookies">Cookie政策</a></li>
        </ul>
      </div>
    </div>
    
    <div class="border-t border-gray-300 pt-8 text-center">
      <p class="text-gray-500 text-sm" data-i18n="footer.copyright">
        © 2024 lingtube.net. 保留所有权利。
      </p>
    </div>
  </div>
</footer>

<!-- 图片放大模态框 -->
<div id="image-modal" class="image-modal" role="dialog" aria-labelledby="modal-title" aria-hidden="true">
  <div class="image-modal-content">
    <button class="image-modal-close" aria-label="关闭图片预览">
      <i class="fas fa-times"></i>
    </button>
    <img id="modal-image" src="" alt="功能展示图片" />
  </div>
</div>

  <!-- 主页JavaScript逻辑 -->
  <script src="/assets/js/index.js"></script>
</body>
</html>
