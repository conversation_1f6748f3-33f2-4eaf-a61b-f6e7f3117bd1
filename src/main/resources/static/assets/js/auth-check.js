// 检查用户登录状态，如果未登录则重定向到登录页面
function checkAuthentication() {
    fetch('/api/user/me')
        .then(response => response.json())
        .then(data => {
            if (!data.authenticated) {
                alert('请先登录后再访问此页面');
                window.location.href = '/login.html';
            }
        })
        .catch(error => {
            console.error('检查登录状态失败:', error);
            alert('检查登录状态失败，请重试');
            window.location.href = '/login.html';
        });
}

// 页面加载时检查用户登录状态
document.addEventListener('DOMContentLoaded', checkAuthentication); 