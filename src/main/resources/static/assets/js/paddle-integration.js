/**
 * Paddle支付集成核心代码
 * 
 * 整体流程:
 * 1. 初始化Paddle SDK并设置环境（沙盒/生产）
 * 2. 从Paddle获取价格信息并更新UI
 * 3. 当用户点击"立即开始"按钮时，打开Paddle结账页面
 * 4. 支付成功后，跳转到支付成功页面
 */
document.addEventListener('DOMContentLoaded', function() {
    /**
     * Paddle配置信息
     */
    // 环境设置:sandbox用于测试，production用于生产环境
    const PADDLE_ENVIRONMENT = 'sandbox';
    // 客户端令牌:用于API认证，从Paddle开发者控制台获取
    const PADDLE_CLIENT_TOKEN = 'test_6e1820fe9a8b53d886309796e20';
    
    /**
     * 价格ID配置
     * 
     * 每个产品都有唯一的价格ID，用于标识特定的订阅计划
     * pri_开头的字符串是Paddle分配的唯一价格标识符
     */
    const PRICE_IDS = {
        plus: {
            monthly: 'pri_01jvbgb2h5q8s0t9stp6pm854t', // Plus月付价格ID
            yearly: 'pri_01jvbxp9dwz3001swbx0bpg7tk'   // Plus年付价格ID
        },
        premium: {
            monthly: 'pri_01jvbgc2h13eh7qwxhqq6f44ak', // Premium月付价格ID
            yearly: 'pri_01jvbxjwhyw6qja47dxrghymw2'   // Premium年付价格ID
        }
    };

    /**
     * 价格显示元素引用
     * 
     * 存储页面上需要动态更新价格的DOM元素
     */
    const priceElements = {
        plus: {
            monthlyPrice: document.querySelector('.plus-monthly-price'),
            yearlyPrice: document.querySelector('.plus-yearly-price'),
            yearlySave: document.querySelector('.plus-yearly-save')
        },
        premium: {
            monthlyPrice: document.querySelector('.premium-monthly-price'),
            yearlyPrice: document.querySelector('.premium-yearly-price'),
            yearlySave: document.querySelector('.premium-yearly-save')
        }
    };

    // 获取价格卡片容器元素
    const pricingContainer = document.querySelector('.grid');

    // Paddle初始化状态标志
    let paddleInitialized = false;
    
    // 页面加载时立即显示订阅卡片
    animateCards();
    
    // 设置初始loading效果
    setLoadingPrices();

    // 新增:存储从后端获取的用户信息
    let currentUserDetails = null;

    /**
     * 新增:从后端获取当前用户信息
     */
    async function fetchUserDetails() {
        try {
            const response = await fetch('/api/user/paddle-details');
            if (!response.ok) {
                console.error('获取用户信息失败:', response.status);
                return null;
            }
            const data = await response.json();
            if (data.error) {
                console.error('获取用户信息错误:', data.error);
                return null;
            }
            currentUserDetails = {
                email: data.email,
                your_user_id: data.googleId // 后端返回的是googleId，对应Paddle的your_user_id
            };
            console.log('成功获取用户信息:', currentUserDetails);
            return currentUserDetails;
        } catch (error) {
            console.error('请求用户信息API时出错:', error);
            return null;
        }
    }

    /**
     * 初始化Paddle SDK
     * 
     * 这是整个支付流程的第一步，在页面加载后立即执行
     * 成功初始化后，会获取价格信息并设置结账按钮事件
     */
    async function initPaddle() {
        try {
            // 1. 设置Paddle环境（沙盒或生产）
            Paddle.Environment.set(PADDLE_ENVIRONMENT);
            
            // 2. 初始化Paddle SDK，提供客户端令牌
            Paddle.Initialize({
                token: PADDLE_CLIENT_TOKEN, // 客户端认证令牌
                eventCallback: function(data) {
                   // 监听Paddle事件，用于跟踪支付流程中的各种状态
                   console.log('Paddle Event:', data);
                   if (data.name === 'checkout.completed') {
                       // 支付完成事件，此时可以执行后续逻辑
                       // 例如:记录购买数据、更新用户权限等
                       console.log('支付完成，交易详情:', data.data);
                       
                       // 注意:通常情况下，建议由后端服务器处理支付成功的后续逻辑
                       // 前端可以跳转到成功页面，但不要仅依赖前端来更新用户的订阅状态
                   }
                }
            });
            
            // 3. 标记初始化成功
            paddleInitialized = true;
            console.log('Paddle SDK初始化成功');
            
            // 新增:在Paddle初始化成功后获取用户信息
            await fetchUserDetails();
            
            // 4. 获取价格信息并更新UI
            fetchPrices();
            
            // 5. 设置结账按钮的点击事件
            setupCheckoutButtons();
        } catch (error) {
            console.error('Paddle SDK初始化失败:', error);
            // 获取价格失败，使用默认价格
            setDefaultPrices();
        }
    }

    /**
     * 设置价格为加载中状态
     * 在获取实际价格之前显示加载状态
     */
    function setLoadingPrices() {
        // 为所有价格显示loading效果
        const priceSpans = document.querySelectorAll('.plus-monthly-price, .plus-yearly-price, .premium-monthly-price, .premium-yearly-price');
        priceSpans.forEach(span => {
            span.innerHTML = '<span class="loading-price">加载中...</span>';
        });
        
        const saveSpans = document.querySelectorAll('.plus-yearly-save, .premium-yearly-save');
        saveSpans.forEach(span => {
            span.innerHTML = '<span class="loading-price">...</span>';
        });
    }

    /**
     * 获取价格信息
     * 
     * 使用Paddle API获取最新的价格信息，然后更新UI显示
     * 这确保了用户看到的是最新的价格，而不是硬编码的价格
     */
    async function fetchPrices() {
        if (!paddleInitialized) {
            console.log('Paddle尚未初始化，无法获取价格');
            // 获取价格失败，使用默认价格
            setDefaultPrices();
            return;
        }
        try {
            /**
             * Paddle.PricePreview API
             * 
             * 这个API用于获取指定价格ID的详细价格信息
             * 它考虑了用户的位置、税费等因素，返回最终价格
             */
            
            // 1. 获取Plus月付价格
            const plusMonthlyPreview = await Paddle.PricePreview({
                items: [{priceId: PRICE_IDS.plus.monthly, quantity: 1}]
            });
            
            // 2. 获取Plus年付价格
            const plusYearlyPreview = await Paddle.PricePreview({
                items: [{priceId: PRICE_IDS.plus.yearly, quantity: 1}]
            });
            
            // 3. 获取Premium月付价格
            const premiumMonthlyPreview = await Paddle.PricePreview({
                items: [{priceId: PRICE_IDS.premium.monthly, quantity: 1}]
            });
            
            // 4. 获取Premium年付价格
            const premiumYearlyPreview = await Paddle.PricePreview({
                items: [{priceId: PRICE_IDS.premium.yearly, quantity: 1}]
            });

            // 5. 更新UI显示
            updatePriceDisplay(plusMonthlyPreview.data, plusYearlyPreview.data, premiumMonthlyPreview.data, premiumYearlyPreview.data);
        } catch (error) {
            console.error('获取价格失败:', error);
            console.log('使用默认价格显示');
            
            // 获取价格失败，使用默认价格
            setDefaultPrices();
        }
    }

    /**
     * 设置默认价格
     * 当无法从Paddle获取价格时，使用默认价格
     */
    function setDefaultPrices() {
        priceElements.plus.monthlyPrice.textContent = '40';
        priceElements.plus.yearlyPrice.textContent = '480';
        priceElements.plus.yearlySave.textContent = '96';
        
        priceElements.premium.monthlyPrice.textContent = '82';
        priceElements.premium.yearlyPrice.textContent = '980';
        priceElements.premium.yearlySave.textContent = '196';
    }

    /**
     * 更新价格显示
     * 
     * 将从Paddle API获取的价格信息更新到UI上
     * 注意:Paddle返回的价格是以分为单位的，需要除以100转换为元
     * 
     * @param {Object} plusMonthly - Plus月付价格数据
     * @param {Object} plusYearly - Plus年付价格数据
     * @param {Object} premiumMonthly - Premium月付价格数据
     * @param {Object} premiumYearly - Premium年付价格数据
     */
    function updatePriceDisplay(plusMonthly, plusYearly, premiumMonthly, premiumYearly) {
        try {
            // 验证数据结构，确保可以安全访问嵌套属性
            if (!plusMonthly || !plusMonthly.details || !plusMonthly.details.lineItems || !plusMonthly.details.lineItems[0] || !plusMonthly.details.lineItems[0].totals) {
                console.error('Plus月付价格数据结构错误:', plusMonthly);
                setDefaultPrices();
                return;
            }
            if (!plusYearly || !plusYearly.details || !plusYearly.details.lineItems || !plusYearly.details.lineItems[0] || !plusYearly.details.lineItems[0].totals) {
                console.error('Plus年付价格数据结构错误:', plusYearly);
                setDefaultPrices();
                return;
            }
            if (!premiumMonthly || !premiumMonthly.details || !premiumMonthly.details.lineItems || !premiumMonthly.details.lineItems[0] || !premiumMonthly.details.lineItems[0].totals) {
                console.error('Premium月付价格数据结构错误:', premiumMonthly);
                setDefaultPrices();
                return;
            }
            if (!premiumYearly || !premiumYearly.details || !premiumYearly.details.lineItems || !premiumYearly.details.lineItems[0] || !premiumYearly.details.lineItems[0].totals) {
                console.error('Premium年付价格数据结构错误:', premiumYearly);
                setDefaultPrices();
                return;
            }

            // 1. 更新Plus月付价格
            // Paddle返回的价格是以分为单位，需要除以100转换为元
            const plusMonthlyAmount = Math.round(plusMonthly.details.lineItems[0].totals.total / 100);
            priceElements.plus.monthlyPrice.textContent = plusMonthlyAmount;
            
            // 2. 更新Plus年付价格
            const plusYearlyAmount = Math.round(plusYearly.details.lineItems[0].totals.total / 100);
            priceElements.plus.yearlyPrice.textContent = plusYearlyAmount;
            
            // 3. 计算Plus年付节省金额（月付*12 - 年付）
            const plusSaveAmount = Math.round((plusMonthlyAmount * 12) - plusYearlyAmount);
            priceElements.plus.yearlySave.textContent = plusSaveAmount > 0 ? plusSaveAmount : 0;
            
            // 4. 更新Premium月付价格
            const premiumMonthlyAmount = Math.round(premiumMonthly.details.lineItems[0].totals.total / 100);
            priceElements.premium.monthlyPrice.textContent = premiumMonthlyAmount;
            
            // 5. 更新Premium年付价格
            const premiumYearlyAmount = Math.round(premiumYearly.details.lineItems[0].totals.total / 100);
            priceElements.premium.yearlyPrice.textContent = premiumYearlyAmount;
            
            // 6. 计算Premium年付节省金额
            const premiumSaveAmount = Math.round((premiumMonthlyAmount * 12) - premiumYearlyAmount);
            priceElements.premium.yearlySave.textContent = premiumSaveAmount > 0 ? premiumSaveAmount : 0;
            
            console.log('价格更新成功');
        } catch (error) {
            console.error('更新价格显示失败:', error);
            console.error('传入 updatePriceDisplay 的数据:', {plusMonthly, plusYearly, premiumMonthly, premiumYearly});
            setDefaultPrices();
        }
    }

    /**
     * 显示卡片的序列动画
     * 在页面加载时立即调用，显示订阅卡片
     */
    function animateCards() {
        const priceCards = document.querySelectorAll('.price-card');
        
        // 先显示容器
        pricingContainer.style.opacity = '1';
        
        // 然后为每个卡片添加动画
        priceCards.forEach((card, index) => {
            setTimeout(() => {
                card.style.animation = `fadeInUp 0.5s ease forwards`;
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 200 * index); // 200ms延迟每个卡片
        });
    }

    /**
     * 设置结账按钮点击事件
     * 
     * 当用户点击"立即开始"按钮时，调用Paddle Checkout API打开支付页面
     * 包含以下关键步骤:
     * 1. 检查Paddle是否已初始化
     * 2. 根据用户选择的套餐和支付周期（月付/年付）获取对应的价格ID
     * 3. 调用Paddle.Checkout.open API发起支付
     */
    function setupCheckoutButtons() {
        const checkoutButtons = document.querySelectorAll('.checkout-button');
        
        checkoutButtons.forEach(button => {
            button.addEventListener('click', async function() { // 改为async函数
                // 1. 检查Paddle是否已初始化
                if (!paddleInitialized) {
                    console.log('Paddle尚未初始化，无法开始结账');
                    alert('支付服务尚未准备好，请稍后再试。');
                    return;
                }
                
                // 新增:检查是否已获取用户信息
                if (!currentUserDetails) {
                    console.log('用户信息尚未获取，正在尝试重新获取...');
                    await fetchUserDetails(); // 尝试再次获取
                    if (!currentUserDetails) {
                        alert('无法获取用户信息，无法继续支付。请确保您已登录。');
                        return;
                    }
                }

                // 2. 获取用户选择的套餐类型（plus/premium）
                const planType = this.dataset.planType;
                
                // 3. 判断当前是月付还是年付
                const isYearly = document.getElementById('yearly-btn').classList.contains('active');
                
                // 4. 获取对应的价格ID
                let priceId;
                if (planType === 'plus') {
                    priceId = isYearly ? PRICE_IDS.plus.yearly : PRICE_IDS.plus.monthly;
                } else if (planType === 'premium') {
                    priceId = isYearly ? PRICE_IDS.premium.yearly : PRICE_IDS.premium.monthly;
                } else {
                    console.error('未知的套餐类型:', planType);
                    alert('选择的套餐无效，请重试。');
                    return;
                }

                    /**
                     * Paddle.Checkout.open API
                     * 
                     * 打开Paddle支付页面进行结账
                     * 这是Paddle支付流程的核心步骤
                     */
                try {
                    Paddle.Checkout.open({
                        items: [{ priceId: priceId, quantity: 1 }],
                        // email: currentUserDetails.email, // 可选，如果Paddle自动填充不符合预期
                        customer: {
                           email: currentUserDetails.email // 建议传递customer.email
                        },
                        customData: currentUserDetails, // 从后端获取的用户信息
                        settings: {
                            successUrl: window.location.origin + '/pages/payment/payment-success.html', // 修改这里，指向支付成功页面
                            // displayMode: 'overlay', // 可选:'inline', 'overlay'
                            // theme: 'light', // 可选:'light', 'dark'
                            // locale: 'zh-CN' // 可选:设置支付页面语言
                        }
                    });
                    
                    /**
                     * 后续流程:
                     * 1. Paddle Checkout页面打开，用户完成支付
                     * 2. Paddle处理支付并触发'checkout.completed'事件
                     * 3. 如果支付成功，Paddle会将用户重定向到successUrl指定的页面
                     * 4. 通常还需要设置Paddle Webhook，让后端接收和处理支付成功的通知
                     *    (Webhook设置在Paddle开发者控制台，不在前端代码中)
                     */
                } catch (error) {
                    console.error('打开Paddle Checkout失败:', error);
                    alert('启动支付流程失败，请稍后再试。');
                }
            });
        });
    }

    // 页面加载时，检查Paddle SDK是否可用并初始化
    if (window.Paddle) {
        initPaddle();
    } else {
        console.error('Paddle SDK未加载成功，请检查网络连接或<script>标签');
        // Paddle未加载，使用默认价格
        setDefaultPrices();
    }
}); 