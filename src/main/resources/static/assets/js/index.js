/* 主页JavaScript逻辑 */

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
  checkUserLogin();
  initializeInteractions();
  initializePaddle();
  initializeScrollAnimations();
  initializeLanguageToggle();
});

// 滚动动画初始化
function initializeScrollAnimations() {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
      }
    });
  }, observerOptions);

  // 观察所有需要动画的元素
  document.querySelectorAll('.fade-in').forEach(el => {
    observer.observe(el);
  });
}

// 检查用户登录状态
function checkUserLogin() {
  fetch('/api/user/me')
    .then(response => response.json())
    .then(data => {
      if (data.authenticated) {
        document.getElementById('login-button').classList.add('hidden');
        document.getElementById('user-profile').classList.remove('hidden');
        
        document.getElementById('user-name').textContent = data.name;
        document.getElementById('user-avatar').src = data.picture;
        document.getElementById('dropdown-user-name').textContent = data.name;
        document.getElementById('dropdown-user-email').textContent = data.email || '';
      } else {
        document.getElementById('login-button').classList.remove('hidden');
        document.getElementById('user-profile').classList.add('hidden');
      }
    })
    .catch(error => {
      console.error('获取用户信息失败:', error);
    });
}

// 初始化交互功能
function initializeInteractions() {
  // Header滚动效果
  window.addEventListener('scroll', function() {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
      header.classList.add('scrolled');
    } else {
      header.classList.remove('scrolled');
    }
  });
  
  // 初始化图片放大功能
  initializeImageModal();

  // 移动端菜单
  const mobileMenuBtn = document.getElementById('mobile-menu-btn');
  const mobileMenu = document.getElementById('mobile-menu');
  const mobileMenuClose = document.getElementById('mobile-menu-close');

  mobileMenuBtn?.addEventListener('click', function() {
    mobileMenu.classList.add('active');
  });

  mobileMenuClose?.addEventListener('click', function() {
    mobileMenu.classList.remove('active');
  });

  // 点击菜单链接时关闭移动端菜单
  const mobileNavLinks = mobileMenu.querySelectorAll('a');
  mobileNavLinks.forEach(link => {
    link.addEventListener('click', function() {
      mobileMenu.classList.remove('active');
    });
  });

  // FAQ折叠功能
  const faqToggles = document.querySelectorAll('.faq-toggle');
  faqToggles.forEach(toggle => {
    toggle.addEventListener('click', function() {
      const content = this.nextElementSibling;
      const icon = this.querySelector('i');
      
      // 关闭其他打开的FAQ
      faqToggles.forEach(otherToggle => {
        if (otherToggle !== this) {
          const otherContent = otherToggle.nextElementSibling;
          const otherIcon = otherToggle.querySelector('i');
          otherContent.classList.remove('active');
          otherIcon.style.transform = 'rotate(0deg)';
        }
      });
      
      // 切换当前FAQ
      content.classList.toggle('active');
      if (content.classList.contains('active')) {
        icon.style.transform = 'rotate(180deg)';
      } else {
        icon.style.transform = 'rotate(0deg)';
      }
    });
  });

  // 定价切换功能
  initializePricingToggle();
  
  // 订阅按钮处理
  initializeSubscriptionButtons();
}

// 定价切换功能
function initializePricingToggle() {
  const monthlyBtn = document.getElementById('monthly-btn');
  const yearlyBtn = document.getElementById('yearly-btn');
  const monthlyPrices = document.querySelectorAll('.monthly-price');
  const yearlyPrices = document.querySelectorAll('.yearly-price');
  let isYearly = false;

  monthlyBtn?.addEventListener('click', function() {
    if (isYearly) {
      togglePricing(false);
    }
  });

  yearlyBtn?.addEventListener('click', function() {
    if (!isYearly) {
      togglePricing(true);
    }
  });

  function togglePricing(yearly) {
    isYearly = yearly;
    
    // 重置所有按钮状态
    monthlyBtn.classList.remove('text-white', 'bg-blue-600', 'active');
    monthlyBtn.classList.add('text-gray-700');
    yearlyBtn.classList.remove('text-white', 'bg-blue-600', 'active');
    yearlyBtn.classList.add('text-gray-700');
    
    // 设置激活按钮状态
    if (yearly) {
      yearlyBtn.classList.remove('text-gray-700');
      yearlyBtn.classList.add('text-white', 'bg-blue-600', 'active');
    } else {
      monthlyBtn.classList.remove('text-gray-700');
      monthlyBtn.classList.add('text-white', 'bg-blue-600', 'active');
    }

    // 切换价格显示
    monthlyPrices.forEach(price => {
      price.style.display = yearly ? 'none' : 'block';
    });
    
    yearlyPrices.forEach(price => {
      price.style.display = yearly ? 'block' : 'none';
    });
  }
}

// 订阅按钮处理
function initializeSubscriptionButtons() {
  const checkoutButtons = document.querySelectorAll('.checkout-button');
  
  checkoutButtons.forEach(button => {
    button.addEventListener('click', function(e) {
      e.preventDefault();
      
      // 检查用户登录状态
      fetch('/api/user/me')
        .then(response => response.json())
        .then(data => {
          if (data.authenticated) {
            // 用户已登录，处理订阅逻辑
            const planType = this.getAttribute('data-plan-type');
            handleSubscription(planType);
          } else {
            // 用户未登录，显示登录提示
            showLoginHint();
          }
        })
        .catch(error => {
          console.error('检查登录状态失败:', error);
          showLoginHint();
        });
    });
  });
}

// Paddle配置
const PADDLE_ENVIRONMENT = 'sandbox';
const PADDLE_CLIENT_TOKEN = 'test_6e1820fe9a8b53d886309796e20';
const PRICE_IDS = {
  plus: {
    monthly: 'pri_01jvbgb2h5q8s0t9stp6pm854t',
    yearly: 'pri_01jvbxp9dwz3001swbx0bpg7tk'
  },
  premium: {
    monthly: 'pri_01jvbgc2h13eh7qwxhqq6f44ak',
    yearly: 'pri_01jvbxjwhyw6qja47dxrghymw2'
  }
};

let paddleInitialized = false;
let currentUserDetails = null;

// 初始化Paddle
async function initializePaddle() {
  if (!window.Paddle) {
    console.error('Paddle SDK未加载成功，请检查网络连接');
    setDefaultPrices();
    return;
  }

  try {
    Paddle.Environment.set(PADDLE_ENVIRONMENT);
    Paddle.Initialize({
      token: PADDLE_CLIENT_TOKEN,
      eventCallback: function(data) {
        console.log('Paddle Event:', data);
        if (data.name === 'checkout.completed') {
          console.log('支付完成，交易详情:', data.data);
        }
      }
    });
    
    paddleInitialized = true;
    console.log('Paddle SDK初始化成功');
    
    // 获取价格信息
    await fetchPaddlePrices();
    
  } catch (error) {
    console.error('Paddle SDK初始化失败:', error);
    setDefaultPrices();
  }
}

// 获取用户详情
async function fetchUserDetails() {
  try {
    const response = await fetch('/api/user/paddle-details');
    if (!response.ok) {
      console.error('获取用户信息失败:', response.status);
      return null;
    }
    const data = await response.json();
    if (data.error) {
      console.error('获取用户信息错误:', data.error);
      return null;
    }
    currentUserDetails = {
      email: data.email,
      your_user_id: data.googleId
    };
    console.log('成功获取用户信息:', currentUserDetails);
    return currentUserDetails;
  } catch (error) {
    console.error('请求用户信息API时出错:', error);
    return null;
  }
}

// 获取Paddle价格
async function fetchPaddlePrices() {
  if (!paddleInitialized) {
    setDefaultPrices();
    return;
  }
  
  try {
    const plusMonthlyPreview = await Paddle.PricePreview({
      items: [{priceId: PRICE_IDS.plus.monthly, quantity: 1}]
    });
    
    const plusYearlyPreview = await Paddle.PricePreview({
      items: [{priceId: PRICE_IDS.plus.yearly, quantity: 1}]
    });
    
    const premiumMonthlyPreview = await Paddle.PricePreview({
      items: [{priceId: PRICE_IDS.premium.monthly, quantity: 1}]
    });
    
    const premiumYearlyPreview = await Paddle.PricePreview({
      items: [{priceId: PRICE_IDS.premium.yearly, quantity: 1}]
    });

    updatePaddlePriceDisplay(plusMonthlyPreview.data, plusYearlyPreview.data, premiumMonthlyPreview.data, premiumYearlyPreview.data);
  } catch (error) {
    console.error('获取价格失败:', error);
    setDefaultPrices();
  }
}

// 设置默认价格
function setDefaultPrices() {
  const plusMonthlyPrice = document.querySelector('.plus-monthly-price');
  const plusYearlyPrice = document.querySelector('.plus-yearly-price');
  const plusYearlySave = document.querySelector('.plus-yearly-save');
  const premiumMonthlyPrice = document.querySelector('.premium-monthly-price');
  const premiumYearlyPrice = document.querySelector('.premium-yearly-price');
  const premiumYearlySave = document.querySelector('.premium-yearly-save');

  if (plusMonthlyPrice) plusMonthlyPrice.textContent = '40';
  if (plusYearlyPrice) plusYearlyPrice.textContent = '480';
  if (plusYearlySave) plusYearlySave.textContent = '96';
  if (premiumMonthlyPrice) premiumMonthlyPrice.textContent = '82';
  if (premiumYearlyPrice) premiumYearlyPrice.textContent = '980';
  if (premiumYearlySave) premiumYearlySave.textContent = '196';
}

// 更新价格显示
function updatePaddlePriceDisplay(plusMonthly, plusYearly, premiumMonthly, premiumYearly) {
  try {
    if (!plusMonthly?.details?.lineItems?.[0]?.totals ||
        !plusYearly?.details?.lineItems?.[0]?.totals ||
        !premiumMonthly?.details?.lineItems?.[0]?.totals ||
        !premiumYearly?.details?.lineItems?.[0]?.totals) {
      console.error('价格数据结构错误');
      setDefaultPrices();
      return;
    }

    const plusMonthlyAmount = Math.round(plusMonthly.details.lineItems[0].totals.total / 100);
    const plusYearlyAmount = Math.round(plusYearly.details.lineItems[0].totals.total / 100);
    const premiumMonthlyAmount = Math.round(premiumMonthly.details.lineItems[0].totals.total / 100);
    const premiumYearlyAmount = Math.round(premiumYearly.details.lineItems[0].totals.total / 100);

    const plusSaveAmount = Math.round((plusMonthlyAmount * 12) - plusYearlyAmount);
    const premiumSaveAmount = Math.round((premiumMonthlyAmount * 12) - premiumYearlyAmount);

    document.querySelector('.plus-monthly-price').textContent = plusMonthlyAmount;
    document.querySelector('.plus-yearly-price').textContent = plusYearlyAmount;
    document.querySelector('.plus-yearly-save').textContent = plusSaveAmount > 0 ? plusSaveAmount : 0;
    
    document.querySelector('.premium-monthly-price').textContent = premiumMonthlyAmount;
    document.querySelector('.premium-yearly-price').textContent = premiumYearlyAmount;
    document.querySelector('.premium-yearly-save').textContent = premiumSaveAmount > 0 ? premiumSaveAmount : 0;
    
    console.log('价格更新成功');
  } catch (error) {
    console.error('更新价格显示失败:', error);
    setDefaultPrices();
  }
}

// 处理订阅逻辑
async function handleSubscription(planType) {
  if (!paddleInitialized) {
    showToast('支付服务尚未准备好，请稍后再试');
    return;
  }

  // 获取用户信息
  if (!currentUserDetails) {
    await fetchUserDetails();
    if (!currentUserDetails) {
      showToast('无法获取用户信息，请确保您已登录');
      return;
    }
  }

  // 判断当前是月付还是年付
  const isYearly = document.getElementById('yearly-btn').classList.contains('text-white');
  
  // 获取对应的价格ID
  let priceId;
  if (planType === 'plus') {
    priceId = isYearly ? PRICE_IDS.plus.yearly : PRICE_IDS.plus.monthly;
  } else if (planType === 'premium') {
    priceId = isYearly ? PRICE_IDS.premium.yearly : PRICE_IDS.premium.monthly;
  } else {
    showToast('选择的套餐无效，请重试');
    return;
  }

  try {
    Paddle.Checkout.open({
      items: [{ priceId: priceId, quantity: 1 }],
      customer: {
        email: currentUserDetails.email
      },
      customData: currentUserDetails,
      settings: {
        successUrl: window.location.origin + '/pages/payment/payment-success.html'
      }
    });
  } catch (error) {
    console.error('打开Paddle Checkout失败:', error);
    showToast('启动支付流程失败，请稍后再试');
  }
}

// 显示登录提示
function showLoginHint() {
  const loginHint = document.getElementById('login-hint');
  const loginButton = document.querySelector('#login-button a');
  
  if (loginHint && loginButton) {
    loginHint.classList.remove('scale-0');
    loginHint.classList.add('scale-100');
    
    loginButton.classList.add('animate-pulse');
    loginButton.classList.add('ring-4');
    loginButton.classList.add('ring-white');
    
    setTimeout(() => {
      hideLoginHint();
    }, 5000);
  }
}

// 隐藏登录提示
function hideLoginHint() {
  const loginHint = document.getElementById('login-hint');
  const loginButton = document.querySelector('#login-button a');
  
  if (loginHint) {
    loginHint.classList.remove('scale-100');
    loginHint.classList.add('scale-0');
  }
  
  if (loginButton) {
    loginButton.classList.remove('animate-pulse', 'ring-4', 'ring-white');
  }
}

// 显示提示消息
function showToast(message) {
  let toast = document.getElementById('toast-message');
  if (!toast) {
    toast = document.createElement('div');
    toast.id = 'toast-message';
    toast.className = 'fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-6 py-3 rounded-lg shadow-lg opacity-0 transition-opacity duration-300 z-50';
    document.body.appendChild(toast);
  }
  
  toast.textContent = message;
  toast.classList.remove('opacity-0');
  toast.classList.add('opacity-100');
  
  setTimeout(() => {
    toast.classList.remove('opacity-100');
    toast.classList.add('opacity-0');
  }, 3000);
}

// 图片放大模态框功能
function initializeImageModal() {
  const modal = document.getElementById('image-modal');
  const modalImage = document.getElementById('modal-image');
  const closeButton = modal.querySelector('.image-modal-close');
  const featureImages = document.querySelectorAll('.feature-image');
  
  // 为所有功能图片添加点击事件
  featureImages.forEach(img => {
    img.addEventListener('click', function() {
      openImageModal(this.src, this.alt);
    });
  });
  
  // 关闭按钮事件
  closeButton.addEventListener('click', closeImageModal);
  
  // 点击背景关闭模态框
  modal.addEventListener('click', function(e) {
    if (e.target === modal) {
      closeImageModal();
    }
  });
  
  // ESC键关闭模态框
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && modal.classList.contains('active')) {
      closeImageModal();
    }
  });
  
  // 打开模态框
  function openImageModal(src, alt) {
    modalImage.src = src;
    modalImage.alt = alt;
    modal.classList.add('active');
    modal.setAttribute('aria-hidden', 'false');
    document.body.classList.add('modal-open');
    
    // 聚焦到模态框以支持键盘导航
    modal.focus();
  }
  
  // 关闭模态框
  function closeImageModal() {
    modal.classList.remove('active');
    modal.setAttribute('aria-hidden', 'true');
    document.body.classList.remove('modal-open');
    
    // 清空图片src以节省内存
    setTimeout(() => {
      if (!modal.classList.contains('active')) {
        modalImage.src = '';
      }
    }, 300);
  }
}

// 初始化语言切换功能
function initializeLanguageToggle() {
  const languageToggle = document.getElementById('language-toggle');
  const mobileLanguageToggle = document.getElementById('mobile-language-toggle');
  const languageText = languageToggle.querySelector('.language-text');
  const mobileLanguageText = mobileLanguageToggle.querySelector('.language-text');
  
  // 根据当前语言更新按钮文本
  function updateLanguageButton() {
    const currentLang = window.i18n.getCurrentLanguage();
    const text = currentLang === 'zh' ? '中' : 'EN';
    languageText.textContent = text;
    mobileLanguageText.textContent = text;
  }
  
  // 语言切换处理函数
  function handleLanguageSwitch() {
    const currentLang = window.i18n.getCurrentLanguage();
    const newLang = currentLang === 'zh' ? 'en' : 'zh';
    
    // 切换语言
    window.i18n.switchLanguage(newLang);
    
    // 更新按钮文本
    updateLanguageButton();
    
    // 显示切换成功提示
    const message = newLang === 'zh' ? '已切换到中文' : 'Switched to English';
    showToast(message);
  }
  
  // 初始化按钮状态
  updateLanguageButton();
  
  // 桌面端语言切换事件
  languageToggle.addEventListener('click', handleLanguageSwitch);
  
  // 移动端语言切换事件
  mobileLanguageToggle.addEventListener('click', handleLanguageSwitch);
  
  // 监听语言切换事件
  window.addEventListener('languageChanged', function(e) {
    updateLanguageButton();
  });
} 