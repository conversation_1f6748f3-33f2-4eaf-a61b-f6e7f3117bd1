/* 黑暗模式样式 */
:root {
  --bg-light: #ffffff;
  --text-light: #1f2937;
  --bg-dark: #1f2937;
  --text-dark: #f3f4f6;
  --card-bg-light: #ffffff;
  --card-bg-dark: #2d3748;
  --nav-bg-light: #ffffff;
  --nav-bg-dark: #1a202c;
  --border-light: rgba(229, 231, 235, 0.7);
  --border-dark: rgba(55, 65, 81, 0.7);
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-dark: rgba(0, 0, 0, 0.3);
  --glass-bg-light: rgba(255, 255, 255, 0.9);
  --glass-bg-dark: rgba(26, 32, 44, 0.9);
  --footer-bg-light: #ffffff;
  --footer-bg-dark: #1a202c;
  --footer-text-light: #6b7280;
  --footer-text-dark: #9ca3af;
  --gradient-light-from: #f3f4f6;
  --gradient-light-to: #e5e7eb;
  --gradient-dark-from: #374151;
  --gradient-dark-to: #1f2937;
}

/* 立即应用暗色模式到html元素，避免页面闪烁 */
html.dark-mode {
  background-color: var(--bg-dark);
  color: var(--text-dark);
}

/* 确保body在暗色模式下也有正确的背景 */
html.dark-mode body {
  background-color: var(--bg-dark) !important;
  color: var(--text-dark) !important;
  background-image: none !important;
  background: linear-gradient(135deg, #1a202c, #2d3748) !important;
  animation: none !important;
  background-size: auto !important;
  background-position: initial !important;
  transition: none !important;
  min-height: 100vh !important;
  font-family: 'Inter', sans-serif !important;
}

/* 页面背景渐变 */
html.dark-mode body.min-h-screen.flex.flex-col.bg-gradient-to-br,
html.dark-mode body.min-h-screen.flex.flex-col.bg-\[\#f3f3ff\],
html.dark-mode body.min-h-screen.flex.flex-col.bg-gradient-to-br.from-indigo-50.via-blue-50.to-purple-50 {
  background: linear-gradient(135deg, #1a202c, #2d3748) !important;
  background-size: auto !important;
  animation: none !important;
  background-position: initial !important;
}

/* 导航栏黑暗模式 */
html.dark-mode nav {
  background-color: var(--nav-bg-dark);
  box-shadow: 0 4px 6px var(--shadow-dark);
}

html.dark-mode nav a:not(:hover) {
  color: #d1d5db !important;
}

html.dark-mode nav .text-gray-700 {
  color: #d1d5db !important;
}

/* 卡片黑暗模式 - 只改变颜色，不改变尺寸 */
html.dark-mode .glass-container {
  background: var(--glass-bg-dark) !important;
  border: 1px solid rgba(55, 65, 81, 0.5);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

html.dark-mode .scenario-card,
html.dark-mode .card,
html.dark-mode .analysis-item {
  background-color: var(--card-bg-dark) !important;
  border-color: var(--border-dark);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

html.dark-mode .scenario-card h3,
html.dark-mode .card h3,
html.dark-mode .card-title {
  color: #e5e7eb !important;
}

html.dark-mode .scenario-card p,
html.dark-mode .card p,
html.dark-mode .card-text {
  color: #d1d5db !important;
}

/* 会员订阅页面卡片 */
html.dark-mode .price-card {
  background-color: var(--card-bg-dark) !important;
  border-color: var(--border-dark);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

/* 会员订阅页面的月付/年付滑块 */
html.dark-mode main .bg-white.rounded-full {
  background-color: #374151 !important;
}

html.dark-mode main .bg-white.rounded-full button {
  color: #9ca3af !important;
}

html.dark-mode main .bg-white.rounded-full button.active {
  background-color: #4f46e5 !important;
  color: #ffffff !important;
}

html.dark-mode #yearly-discount {
  background-color: #064e3b !important;
  color: #d1fae5 !important;
}

html.dark-mode .price-card .text-gray-900 {
  color: #e5e7eb !important;
}

html.dark-mode .price-card .text-gray-600,
html.dark-mode .price-card .text-gray-500 {
  color: #9ca3af !important;
}

html.dark-mode .price-card .bg-gray-100 {
  background-color: #374151 !important;
}

html.dark-mode .price-card .bg-blue-100 {
  background-color: #1e3a8a !important;
}

html.dark-mode .price-card .bg-purple-100 {
  background-color: #5b21b6 !important;
}

html.dark-mode .price-card .text-gray-800 {
  color: #e5e7eb !important;
}

html.dark-mode .price-card .border-gray-200 {
  border-color: #4b5563 !important;
}

html.dark-mode .price-card .bg-blue-50 {
  background-color: #1e40af !important;
}

html.dark-mode .price-card .text-blue-600 {
  color: #60a5fa !important;
}

html.dark-mode .price-card .bg-gray-50 {
  background-color: #374151 !important;
}

html.dark-mode .price-card .text-gray-400 {
  color: #9ca3af !important;
}

/* 表单元素黑暗模式 */
html.dark-mode input,
html.dark-mode textarea,
html.dark-mode select {
  background-color: #374151;
  color: #f3f4f6;
  border-color: #4b5563;
}

html.dark-mode input::placeholder,
html.dark-mode textarea::placeholder {
  color: #9ca3af;
}

/* 按钮黑暗模式 */
html.dark-mode button.bg-white,
html.dark-mode .btn-light {
  background-color: #374151 !important;
  color: #f3f4f6 !important;
}

/* 登录按钮黑暗模式 */
html.dark-mode .glass-button {
  background-color: #2d3748 !important;
  color: #e5e7eb !important;
  border: 1px solid #4b5563 !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2) !important;
}

html.dark-mode #login-button a {
  background-color: #2d3748 !important;
  color: #e5e7eb !important;
  border: 1px solid #4b5563 !important;
}

html.dark-mode #login-button img {
  filter: brightness(1.2);
}

/* 下拉菜单黑暗模式 */
html.dark-mode .profile-dropdown,
html.dark-mode .dropdown-menu {
  background-color: #2d3748;
  border-color: #4b5563;
}

html.dark-mode .profile-dropdown a,
html.dark-mode .dropdown-menu a {
  color: #f3f4f6 !important;
}

html.dark-mode .profile-dropdown a:hover,
html.dark-mode .dropdown-menu a:hover {
  background-color: #4a5568 !important;
}

/* 文本颜色修正 */
html.dark-mode .text-gray-700 {
  color: #d1d5db !important;
}

html.dark-mode .text-gray-800,
html.dark-mode .text-gray-900 {
  color: #f3f4f6 !important;
}

html.dark-mode .text-gray-600 {
  color: #9ca3af !important;
}

html.dark-mode .text-gray-500 {
  color: #9ca3af !important;
}

html.dark-mode .text-gray-400 {
  color: #9ca3af !important;
}

/* 单词复习页面 */
html.dark-mode .word-card {
  background-color: var(--card-bg-dark) !important;
  border-color: var(--border-dark);
}

/* 单词复习页面的tab标签 - 只改变颜色，保持原有尺寸 */
html.dark-mode .tab-btn {
  background-color: #2d3748 !important;
  color: #d1d5db !important;
  border-color: #4b5563 !important;
}

html.dark-mode .tab-btn.active-tab {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
  border-color: #3b82f6 !important;
}

html.dark-mode .tabs {
  border-bottom-color: #4b5563 !important;
}

/* 单词复习页面的主体内容区域 */
html.dark-mode .saved-item {
  background-color: #2d3748 !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2) !important;
}

html.dark-mode .saved-item-column-word {
  color: #e5e7eb !important;
}

html.dark-mode .saved-item-column-details {
  color: #d1d5db !important;
}

/* --- 单词复习页面解析列 Dark Mode Styles --- */

/* 主容器样式: .saved-item-column-details - 只改变颜色，保持原有尺寸 */
html.dark-mode .saved-item .saved-item-column-details {
  background-image: none !important;
  background-color: #2d3748 !important;
  color: #d1d5db !important;
  border-color: #4b5563 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

/* 容器内通用子元素 */
html.dark-mode .saved-item .saved-item-column-details span:not([class*="marker-word"]):not([class*="part-of-speech"]),
html.dark-mode .saved-item .saved-item-column-details div:not([class*="example-text"]):not([class*="translation-text"]):not([class*="details-cover"]),
html.dark-mode .saved-item .saved-item-column-details p {
  background-color: transparent !important;
  color: inherit !important;
}

/* 标记词样式 - 保持原有尺寸 */
html.dark-mode .saved-item .saved-item-column-details .marker-word {
  color: #60a5fa !important;
  background: rgba(96, 165, 250, 0.1) !important;
  border-color: rgba(96, 165, 250, 0.2) !important;
}

/* 词性标记样式 - 保持原有尺寸 */
html.dark-mode .saved-item .saved-item-column-details .part-of-speech,
html.dark-mode .saved-item .saved-item-column-details strong[class*="part-of-speech"] {
  color: #c084fc !important;
  background: rgba(192, 132, 252, 0.1) !important;
  border-color: rgba(192, 132, 252, 0.2) !important;
}

/* 例句样式 - 保持原有尺寸 */
html.dark-mode .saved-item .saved-item-column-details .example-text {
  color: #e5e7eb !important;
  background-color: #374151 !important;
  border-left-color: #60a5fa !important;
}

/* 翻译文本样式 - 保持原有尺寸 */
html.dark-mode .saved-item .saved-item-column-details .translation-text {
  color: #a1a1aa !important;
  background-color: #374151 !important;
  border-left-color: #c084fc !important;
}

/* "点击显示"覆盖层 */
html.dark-mode .saved-item .saved-item-column-details .details-cover {
  background-color: rgba(30, 41, 59, 0.95) !important;
  color: #94a3b8 !important;
}

/* 覆盖通用的strong, b标签 */
html.dark-mode .saved-item .saved-item-column-details strong:not(.part-of-speech),
html.dark-mode .saved-item .saved-item-column-details b:not(.part-of-speech) {
  color: #e5e7eb !important;
  background-color: transparent !important;
}

html.dark-mode .saved-item-column-details .pos-noun,
html.dark-mode .saved-item-column-details .pos-verb,
html.dark-mode .saved-item-column-details .pos-adjective,
html.dark-mode .saved-item-column-details .pos-adverb,
html.dark-mode .saved-item-column-details .pos-particle,
html.dark-mode .saved-item-column-details .pos-pron,
html.dark-mode .saved-item-column-details .pos-aux,
html.dark-mode .saved-item-column-details .pos-conj,
html.dark-mode .saved-item-column-details .pos-other,
html.dark-mode .saved-item-column-details .pos-interjection {
  filter: brightness(0.8);
}

html.dark-mode .saved-item-checkbox label {
  border-color: #6b7280 !important;
}

html.dark-mode .details-cover {
  background-color: #374151 !important;
  color: #9ca3af !important;
}

html.dark-mode #saved-items-container .text-gray-500 {
  color: #9ca3af !important;
}

html.dark-mode .learned-btn {
  background-color: #065f46 !important;
  color: #ffffff !important;
}

html.dark-mode .pagination-controls {
  color: #e5e7eb !important;
}

html.dark-mode .pagination-button {
  background-color: #374151 !important;
  color: #e5e7eb !important;
  border-color: #4b5563 !important;
}

html.dark-mode .word-card .japanese-word {
  color: #e5e7eb !important;
}

html.dark-mode .word-card .word-meaning {
  color: #9ca3af !important;
}

html.dark-mode .word-card .word-reading {
  color: #60a5fa !important;
}

html.dark-mode .word-card .word-pos {
  background-color: #374151 !important;
  color: #9ca3af !important;
}

html.dark-mode .word-card .example-sentence {
  color: #d1d5db !important;
}

html.dark-mode .word-card .example-meaning {
  color: #9ca3af !important;
}

/* 边框颜色修正 */
html.dark-mode .border-gray-100,
html.dark-mode .border-gray-200 {
  border-color: #4b5563;
}

/* 特殊元素黑暗模式 */
html.dark-mode #particles-js {
  opacity: 0.5;
}

/* 词性标注颜色调整 */
html.dark-mode .pos-noun,
html.dark-mode .pos-verb,
html.dark-mode .pos-adjective,
html.dark-mode .pos-adverb,
html.dark-mode .pos-particle,
html.dark-mode .pos-pron,
html.dark-mode .pos-aux,
html.dark-mode .pos-conj,
html.dark-mode .pos-other,
html.dark-mode .pos-interjection {
  filter: brightness(0.8);
}

/* 场景对话的聊天内容背景颜色 */
html.dark-mode .chat-wrapper {
  background-color: transparent !important;
}

html.dark-mode .chat-bubble {
  background-color: #2d3748 !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2) !important;
}

html.dark-mode .chat-bubble.left {
  background-color: #1e293b !important;
}

html.dark-mode .chat-bubble.right {
  background-color: #0f172a !important;
}

/* 对话页面聊天记录旁边的问号按钮 */
html.dark-mode .analyze-button {
  background-color: #374151 !important;
  color: #e5e7eb !important;
  border-color: #4b5563 !important;
}

html.dark-mode .analyze-button i {
  color: #e5e7eb !important;
}

html.dark-mode .analyze-button:hover {
  background-color: #4b5563 !important;
}

html.dark-mode .fas.fa-question {
  color: #000000 !important;
}

html.dark-mode .fa-question-circle {
  color: #000000 !important;
}

html.dark-mode i.fas.fa-question {
  color: #000000 !important;
}

html.dark-mode .analyze-button i.fas.fa-question {
  color: #000000 !important;
}

html.dark-mode .chat-speaker {
  color: #e5e7eb !important;
}

html.dark-mode .chat-japanese {
  color: #e5e7eb !important;
}

html.dark-mode .chat-chinese {
  color: #d1d5db !important;
}

html.dark-mode .speak-button {
  background-color: #374151 !important;
  color: #e5e7eb !important;
  border-color: #4b5563 !important;
}

html.dark-mode #word-tooltip {
  background-color: #1e293b !important;
  color: #e5e7eb !important;
  border-color: #4b5563 !important;
}

html.dark-mode #tooltip-text {
  color: #e5e7eb !important;
}

/* 对话下划线颜色 - 参考插件学习模式的字幕颜色 */
html.dark-mode .pos-noun {
  background-image: linear-gradient(to top, rgba(240, 231, 159, 0.5) 50%, transparent 50%) !important;
}

html.dark-mode .pos-verb {
  background-image: linear-gradient(to top, rgba(167, 243, 208, 0.5) 50%, transparent 50%) !important;
}

html.dark-mode .pos-adjective {
  background-image: linear-gradient(to top, rgba(147, 197, 253, 0.5) 50%, transparent 50%) !important;
}

html.dark-mode .pos-adverb {
  background-image: linear-gradient(to top, rgba(249, 168, 212, 0.5) 50%, transparent 50%) !important;
}

html.dark-mode .pos-particle {
  background-image: linear-gradient(to top, rgba(216, 180, 254, 0.5) 50%, transparent 50%) !important;
}

html.dark-mode .pos-pron {
  background-image: linear-gradient(to top, rgba(252, 165, 165, 0.5) 50%, transparent 50%) !important;
}

html.dark-mode .pos-aux {
  background-image: linear-gradient(to top, rgba(167, 243, 208, 0.5) 50%, transparent 50%) !important;
}

html.dark-mode .pos-conj {
  background-image: linear-gradient(to top, rgba(254, 215, 170, 0.5) 50%, transparent 50%) !important;
}

html.dark-mode .pos-other {
  background-image: linear-gradient(to top, rgba(209, 213, 219, 0.5) 50%, transparent 50%) !important;
}

html.dark-mode .pos-interjection {
  background-image: linear-gradient(to top, rgba(251, 146, 60, 0.5) 50%, transparent 50%) !important;
}

/* 个人主页统计卡片 */
html.dark-mode .bg-gradient-to-r.from-blue-50.to-indigo-50,
html.dark-mode .bg-gradient-to-r.from-green-50.to-emerald-50,
html.dark-mode .bg-gradient-to-r.from-orange-50.to-amber-50,
html.dark-mode .bg-gradient-to-r.from-amber-50.to-yellow-50,
html.dark-mode .bg-gradient-to-r.from-purple-50.to-pink-50 {
  background: linear-gradient(to right, #2d3748, #1e293b) !important;
}

/* 加载动画样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    min-height: 200px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.loading-text {
    font-size: 16px;
    color: #6b7280;
    text-align: center;
    animation: pulse 2s ease-in-out infinite;
}

.loading-dots {
    display: inline-block;
    animation: dots 1.5s steps(4, end) infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* 渐入动画 */
.fade-in {
    opacity: 0;
    animation: fadeIn 0.5s ease-in-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 深色模式下的加载动画 */
html.dark-mode .loading-spinner {
    border-color: #374151;
    border-top-color: #60a5fa;
}

html.dark-mode .loading-text {
    color: #9ca3af;
}

/* 个人主页本月视频部分 */
html.dark-mode #history-container .history-item {
  background-color: #2d3748 !important;
  border-color: #4b5563 !important;
}

html.dark-mode #history-container .history-item:hover {
  background-color: #374151 !important;
}

html.dark-mode #history-container .history-item-title {
  color: #e5e7eb !important;
}

html.dark-mode #history-container .history-item-timestamp {
  color: #9ca3af !important;
}

html.dark-mode #history-container .history-item img {
  border-color: #4b5563 !important;
}

html.dark-mode #history-container .play-button {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

html.dark-mode .bg-purple-100 {
  background-color: #374151 !important;
}

html.dark-mode .w-8.h-8.rounded-full.bg-purple-100 {
  background-color: #374151 !important;
}

html.dark-mode .text-purple-600 {
  color: #c4b5fd !important;
}

html.dark-mode #history-container .pagination-button {
  background-color: #2d3748 !important;
  color: #e5e7eb !important;
  border-color: #4b5563 !important;
}

html.dark-mode #history-container .pagination-button:disabled {
  background-color: #1f2937 !important;
  color: #6b7280 !important;
}

html.dark-mode #history-container .pagination-info {
  color: #9ca3af !important;
}

/* Profile页面历史记录hover样式修复 - 针对Tailwind CSS类名 */
html.dark-mode #history-container .flex.items-center.gap-4.p-2.hover\:bg-indigo-50:hover {
  background-color: #4a5568 !important;
}

/* 通用方式 - 覆盖所有在history-container内使用hover:bg-indigo-50的元素 */
html.dark-mode #history-container .hover\:bg-indigo-50:hover {
  background-color: #4a5568 !important;
}

/* 确保历史记录项的文字在hover状态下仍然清晰可读 */
html.dark-mode #history-container .hover\:bg-indigo-50:hover .text-lg.font-medium.text-gray-800 {
  color: #f3f4f6 !important;
}

html.dark-mode #history-container .hover\:bg-indigo-50:hover .text-gray-600 {
  color: #d1d5db !important;
}

html.dark-mode .streak-container {
  background-color: #2d3748 !important;
  border-color: #4b5563 !important;
}

html.dark-mode .streak-emoji {
  filter: brightness(1.2);
}

html.dark-mode .bg-blue-100,
html.dark-mode .bg-green-100,
html.dark-mode .bg-orange-100,
html.dark-mode .bg-amber-100 {
  background-color: #374151 !important;
}

html.dark-mode .text-blue-500,
html.dark-mode .text-green-500,
html.dark-mode .text-orange-500,
html.dark-mode .text-amber-500 {
  filter: brightness(1.2);
}

html.dark-mode .text-blue-700,
html.dark-mode .text-green-700,
html.dark-mode .text-orange-700,
html.dark-mode .text-amber-700 {
  color: #e5e7eb !important;
}

/* 页脚黑暗模式 */
html.dark-mode footer {
  background-color: var(--footer-bg-dark) !important;
  color: var(--footer-text-dark) !important;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.2) !important;
}

html.dark-mode footer p {
  color: var(--footer-text-dark) !important;
}

/* 滑块开关样式 */
.switch .slider {
  background-color: #ccc;
}

.switch .slider:before {
  background-color: white;
}

.switch input:checked + .slider {
  background-color: #6366f1;
}

.switch input:focus + .slider {
  box-shadow: 0 0 1px #6366f1;
}

.switch input:checked + .slider:before {
  transform: translateX(24px);
}

.slider.round {
  border-radius: 34px;
}

/* 滑块开关颜色调整 */
html.dark-mode .switch .slider {
  background-color: #4b5563;
}

html.dark-mode .switch .slider:before {
  background-color: #e5e7eb;
}

html.dark-mode .switch input:checked + .slider {
  background-color: #6366f1;
}

html.dark-mode .switch input:focus + .slider {
  box-shadow: 0 0 1px #6366f1;
}

/* 滚动条黑暗模式 - 只改变颜色，保持系统默认宽度 */
html.dark-mode ::-webkit-scrollbar-track {
  background: #1f2937;
}

html.dark-mode ::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

html.dark-mode ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}
