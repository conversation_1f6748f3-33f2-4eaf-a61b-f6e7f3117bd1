/* 主页样式文件 */
:root {
  --primary-blue: #1e40af;
  --secondary-blue: #3b82f6;
  --accent-orange: #f59e0b;
  --bg-light: #f8fafc;
  --text-dark: #1f2937;
  --text-light: #6b7280;
  --white: #ffffff;
}

html {
  scroll-behavior: smooth;
}

body {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #ffffff 100%);
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* 几何装饰背景 */
body::before {
  content: '';
  position: fixed;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(30, 64, 175, 0.05) 0%, transparent 50%);
  z-index: -2;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}

/* 简洁的装饰点 */
.hero::before {
  content: '';
  position: absolute;
  top: 20%;
  right: 10%;
  width: 4px;
  height: 4px;
  background: var(--secondary-blue);
  border-radius: 50%;
  opacity: 0.3;
  animation: float-dot 4s ease-in-out infinite;
}

.hero::after {
  content: '';
  position: absolute;
  bottom: 30%;
  left: 15%;
  width: 6px;
  height: 6px;
  background: var(--accent-orange);
  border-radius: 50%;
  opacity: 0.2;
  animation: float-dot 6s ease-in-out infinite reverse;
}

@keyframes float-dot {
  0%, 100% { transform: translateY(0px); opacity: 0.2; }
  50% { transform: translateY(-10px); opacity: 0.4; }
}

/* Header样式 */
.header {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header.scrolled {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.header .nav-link {
  color: var(--text-dark) !important;
  font-weight: 500;
}

.header .brand-text {
  color: var(--primary-blue) !important;
  font-weight: 700;
}

.header .fas.fa-language {
  color: var(--primary-blue) !important;
}

/* Header Grid布局 */
.header-container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.header-logo {
  justify-self: start;
}

.header-nav {
  justify-self: center;
}

.header-actions {
  justify-self: end;
}

/* 移动端header布局调整 */
@media (max-width: 768px) {
  .header-container {
    grid-template-columns: 1fr auto;
    gap: 1rem;
  }
  
  .header-nav {
    display: none !important;
  }
  
  .header-actions {
    justify-self: end;
  }
}

/* 现代标题样式 */
.hero-title {
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue), var(--accent-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  line-height: 1.1;
}

/* 现代按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  color: white;
  padding: 12px 32px;
  border-radius: 12px;
  font-weight: 600;
  box-shadow: 0 4px 14px rgba(59, 130, 246, 0.4);
  transition: all 0.3s ease;
  border: none;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-dark);
  padding: 12px 32px;
  border-radius: 12px;
  font-weight: 600;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: white;
}

/* 现代卡片样式 */
.modern-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.modern-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 移动端菜单 */
.mobile-menu {
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.mobile-menu.active {
  transform: translateX(0);
}

/* 用户下拉菜单 */
.profile-dropdown {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 50;
  min-width: 200px;
  padding: 0.5rem 0;
  margin-top: 0.25rem;
  transform-origin: top right;
  transition: all 0.2s ease;
  border: 1px solid rgba(243, 244, 246, 1);
}

/* 添加透明连接区域，确保鼠标移动时不会断开hover状态 */
.profile-dropdown::before {
  content: '';
  position: absolute;
  top: -0.25rem;
  left: 0;
  right: 0;
  height: 0.25rem;
  background: transparent;
}

.profile-wrapper {
  position: relative;
}

.profile-wrapper:hover .profile-dropdown,
.profile-dropdown:hover {
  display: block;
  animation: menu-appear 0.2s ease;
}

@keyframes menu-appear {
  0% { opacity: 0; transform: scale(0.95); }
  100% { opacity: 1; transform: scale(1); }
}

/* 滚动动画 */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* 特殊效果 */
.text-gradient {
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.icon-gradient {
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* FAQ折叠效果 */
.faq-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease, padding 0.3s ease;
  opacity: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.faq-content.active {
  max-height: 200px;
  opacity: 1;
  padding-top: 0;
  padding-bottom: 1.5rem;
}

/* 定价部分特殊样式 */
.price-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.price-card ul {
  flex-grow: 1;
}

.price-card .checkout-button {
  margin-top: auto;
}

/* 定价切换按钮样式 */
#monthly-btn.active, #yearly-btn.active {
  background-color: #2563eb !important;
  color: white !important;
}

/* 语言切换按钮样式 */
.language-toggle-container {
  display: flex;
  align-items: center;
}

.language-toggle {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 8px;
  padding: 6px 12px;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.language-toggle:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(59, 130, 246, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.language-text {
  min-width: 20px;
  text-align: center;
}

/* 黑暗模式下的语言切换按钮 */
body.dark-mode .language-toggle {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(55, 65, 81, 0.8);
  color: #e2e8f0;
}

body.dark-mode .language-toggle:hover {
  background: rgba(30, 41, 59, 0.9);
  border-color: rgba(55, 65, 81, 0.9);
}

/* 移动端语言切换按钮 */
#mobile-language-toggle {
  background: rgba(107, 114, 128, 0.1);
  border-color: rgba(107, 114, 128, 0.2);
  color: #374151;
}

#mobile-language-toggle:hover {
  background: rgba(107, 114, 128, 0.2);
  border-color: rgba(107, 114, 128, 0.3);
}

body.dark-mode #mobile-language-toggle {
  background: rgba(55, 65, 81, 0.8);
  border-color: rgba(75, 85, 99, 0.8);
  color: #e2e8f0;
}

body.dark-mode #mobile-language-toggle:hover {
  background: rgba(55, 65, 81, 0.9);
  border-color: rgba(75, 85, 99, 0.9);
}

/* 黑暗模式切换滑块样式 */
.theme-toggle-container {
  display: flex;
  align-items: center;
}

.theme-toggle {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.toggle-slider {
  width: 60px;
  height: 30px;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  border-radius: 30px;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.toggle-circle {
  width: 22px;
  height: 22px;
  background: white;
  border-radius: 50%;
  position: absolute;
  left: 4px;
  top: 4px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.toggle-icon {
  font-size: 12px;
  z-index: 1;
  transition: all 0.3s ease;
}

.sun-icon {
  color: #ffffff;
  opacity: 1;
}

.moon-icon {
  color: #ffffff;
  opacity: 0.3;
}

/* 黑暗模式激活状态 */
#dark-mode-toggle:checked + .toggle-slider {
  background: linear-gradient(135deg, #4f46e5, #6366f1);
}

#dark-mode-toggle:checked + .toggle-slider .toggle-circle {
  transform: translateX(30px);
}

#dark-mode-toggle:checked + .toggle-slider .sun-icon {
  opacity: 0.3;
}

#dark-mode-toggle:checked + .toggle-slider .moon-icon {
  opacity: 1;
}

/* hover效果 */
.theme-toggle:hover .toggle-slider {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 黑暗模式下的header样式调整 */
body.dark-mode .header {
  background: rgba(26, 32, 44, 0.9);
  border-bottom: 1px solid rgba(55, 65, 81, 0.8);
}

body.dark-mode .header .nav-link {
  color: #e2e8f0 !important;
}

body.dark-mode .header .brand-text {
  color: #60a5fa !important;
}

/* 黑暗模式下的主要样式 */
body.dark-mode {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
}

body.dark-mode .hero {
  color: #e2e8f0;
}

body.dark-mode .hero-title {
  background: linear-gradient(135deg, #60a5fa, #3b82f6, #f59e0b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

body.dark-mode .modern-card {
  background: rgba(30, 41, 59, 0.9);
  border: 1px solid rgba(55, 65, 81, 0.8);
}

body.dark-mode .text-gray-600 {
  color: #94a3b8 !important;
}

body.dark-mode .text-gray-900 {
  color: #e2e8f0 !important;
}

body.dark-mode .text-gray-500 {
  color: #64748b !important;
}

body.dark-mode .bg-white {
  background-color: #1e293b !important;
}

body.dark-mode .bg-gray-50 {
  background-color: #0f172a !important;
}

/* 移动端菜单黑暗模式 */
body.dark-mode .mobile-menu {
  background: rgba(15, 23, 42, 0.95) !important;
  backdrop-filter: blur(20px);
}

body.dark-mode .mobile-menu .text-gray-600 {
  color: #94a3b8 !important;
}

body.dark-mode .mobile-menu .text-gray-700 {
  color: #e2e8f0 !important;
}

body.dark-mode .mobile-menu a:hover {
  color: #60a5fa !important;
}

/* FAQ部分黑暗模式 */
body.dark-mode #faq .text-gray-900 {
  color: #e2e8f0 !important;
}

body.dark-mode #faq .text-gray-600 {
  color: #94a3b8 !important;
}

body.dark-mode .faq-toggle {
  color: #e2e8f0 !important;
}

body.dark-mode .faq-toggle:hover {
  background-color: rgba(55, 65, 81, 0.5) !important;
}

body.dark-mode .faq-content {
  color: #94a3b8 !important;
}

body.dark-mode .faq-toggle .text-gray-400 {
  color: #9ca3af !important;
}

/* 图片放大模态框样式 */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.image-modal.active {
  opacity: 1;
  visibility: visible;
}

.image-modal-content {
  position: relative;
  max-width: 72vw;
  max-height: 72vh;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.image-modal.active .image-modal-content {
  transform: scale(1);
}

.image-modal img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.image-modal-close {
  position: absolute;
  top: -50px;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #374151;
  font-size: 18px;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.image-modal-close:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

/* 功能图片hover效果 */
.feature-image {
  cursor: zoom-in;
  transition: all 0.3s ease;
}

.feature-image:hover {
  transform: scale(1.02);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* 防止模态框打开时背景滚动 */
body.modal-open {
  overflow: hidden;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }
  
  .btn-primary, .btn-secondary {
    padding: 10px 24px;
    font-size: 0.9rem;
  }
  
  .toggle-slider {
    width: 50px;
    height: 26px;
  }
  
  .toggle-circle {
    width: 18px;
    height: 18px;
  }
  
  #dark-mode-toggle:checked + .toggle-slider .toggle-circle {
    transform: translateX(24px);
  }
  
  .image-modal-content {
    max-width: 76vw;
    max-height: 68vh;
  }
  
  .image-modal-close {
    top: -40px;
    right: -10px;
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
}

/* 技术支持水平滚动样式 */
.partner-scroll-container {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  overflow: hidden;
  border-radius: 16px;
  background: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 2rem 0;
}

.partner-scroll-track {
  display: flex;
  align-items: center;
  animation: scroll-left 30s linear infinite;
  width: fit-content;
}

.partner-scroll-track:hover {
  animation-play-state: paused;
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.partner-item {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 3rem;
  transition: all 0.3s ease;
}

.partner-item:hover {
  transform: scale(1.05);
}

.partner-logo {
  max-width: 160px;
  max-height: 60px;
  object-fit: contain;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.partner-logo:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .partner-item {
    margin: 0 2rem;
  }
  
  .partner-logo {
    max-width: 120px;
    max-height: 45px;
  }
}

@media (max-width: 768px) {
  .partner-scroll-container {
    padding: 1.5rem 0;
  }
  
  .partner-item {
    margin: 0 1.5rem;
  }
  
  .partner-logo {
    max-width: 100px;
    max-height: 40px;
  }
  
  .partner-scroll-track {
    animation-duration: 25s;
  }
}

@media (max-width: 480px) {
  .partner-item {
    margin: 0 1rem;
  }
  
  .partner-logo {
    max-width: 80px;
    max-height: 35px;
  }
  
  .partner-scroll-track {
    animation-duration: 20s;
  }
}

/* 黑暗模式下的合作伙伴样式 */
body.dark-mode .partner-scroll-container {
  background: rgba(30, 41, 59, 0.9);
  border: 1px solid rgba(55, 65, 81, 0.8);
}

body.dark-mode .partner-logo {
  opacity: 0.6;
}

body.dark-mode .partner-logo:hover {
  opacity: 0.9;
}

/* 性能优化 */
* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
}

/* 现代化滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
} 