# lingtube.net 静态资源目录

一个基于Spring Boot的多页面Web应用，提供日语学习相关功能，包括场景对话、单词复习、个人主页、会员订阅等。

## 📁 项目结构

```
src/main/resources/static/
├── index.html                      # 首页（网站入口）
├── favicon.png                     # 网站图标
├── assets/                         # 静态资源目录
│   ├── css/                        # 全局样式文件
│   │   └── dark-mode.css           # 黑暗模式样式
│   └── js/                         # 全局脚本文件
│       ├── auth-check.js           # 认证检查脚本
│       └── paddle-integration.js   # 支付集成脚本
├── components/                     # 可复用组件目录
│   ├── navbar/                     # 导航栏组件
│   │   ├── navbar.html             # 导航栏HTML模板
│   │   └── navbar.js               # 导航栏控制脚本
│   └── analysis-panel/             # 分析面板组件
│       └── analysis-panel.js       # 分析面板脚本
├── pages/                          # 页面文件目录
│   ├── dialog/                     # 场景对话页面
│   │   ├── dialog.html             # 对话页面HTML
│   │   ├── dialog.css              # 对话页面样式
│   │   └── dialog.js               # 对话页面脚本
│   ├── review/                     # 单词复习页面
│   │   ├── review.html             # 复习页面HTML
│   │   ├── review.css              # 复习页面样式
│   │   ├── review-*.js             # 复习功能模块脚本
│   │   └── text-to-speech.js       # 语音合成脚本
│   ├── profile/                    # 个人主页
│   │   ├── profile.html            # 主页HTML
│   │   ├── profile.css             # 主页样式
│   │   └── profile.js              # 主页脚本
│   ├── plan/                       # 会员订阅页面
│   │   ├── plan.html               # 订阅页面HTML
│   │   ├── plan.css                # 订阅页面样式
│   │   └── plan.js                 # 订阅页面脚本
│   ├── suggestions/                # 建议页面
│   │   ├── suggestions.html        # 建议页面HTML
│   │   ├── suggestions.css         # 建议页面样式
│   │   └── suggestions.js          # 建议页面脚本
│   ├── payment/                    # 支付相关页面
│   │   ├── payment-success.html    # 支付成功页面
│   │   └── payment-success.js      # 支付成功脚本
│   ├── error/                      # 错误页面
│   │   ├── 404.html                # 404错误页面
│   │   ├── 404.css                 # 404页面样式
│   │   ├── 404.js                  # 404页面脚本
│   │   ├── 500.html                # 500错误页面
│   │   ├── 500.css                 # 500页面样式
│   │   └── 500.js                  # 500页面脚本
│   └── tools/                      # 工具页面
│       ├── text-to-speech.html     # 语音合成工具页面
│       └── data-dashboard.html     # 数据仪表板页面
├── shared/                         # 共享模块目录
│   ├── managers/                   # 管理器类
│   │   ├── dialog-manager.js       # 对话管理器
│   │   ├── analysis-manager.js     # 分析管理器
│   │   ├── favorite-manager.js     # 收藏管理器
│   │   ├── storage-manager.js      # 存储管理器
│   │   ├── tooltip-manager.js      # 提示管理器
│   │   └── tts-manager.js          # 语音合成管理器
│   └── utils/                      # 工具类
│       ├── ui-utils.js             # UI工具函数
│       └── dark-mode.js            # 黑暗模式工具
└── subtitles/                      # 字幕文件目录（保持现有结构）
```

## 🚀 主要功能模块

### 1. 首页 (index.html)
- 网站入口页面，提供用户登录和导航功能
- 集成Google OAuth2认证
- 响应式设计，支持粒子背景效果

### 2. 场景对话 (pages/dialog/)
- AI驱动的日语对话生成功能
- 支持多种生活场景（餐厅、酒店、购物等）
- 词汇分析和语法解析功能
- 文本转语音和收藏功能

### 3. 单词复习 (pages/review/)
- 个性化单词复习系统
- 支持学习状态管理（正在学习/已学会）
- 音频播放和联想记忆功能
- 分页浏览和批量操作

### 4. 个人主页 (pages/profile/)
- 用户信息展示和管理
- 学习历史和统计数据
- 图表可视化学习进度

### 5. 会员订阅 (pages/plan/)
- 多层级订阅计划展示
- 集成Paddle支付系统
- 订阅状态管理

### 6. 可复用组件 (components/)
- **导航栏组件**: 统一的页面导航和用户状态显示
- **分析面板组件**: 词汇和语法分析结果展示

### 7. 共享模块 (shared/)
- **管理器类**: 封装业务逻辑的管理器模式
- **工具类**: 通用的UI操作和功能函数

## 🔧 技术架构

### 前端技术栈
- **HTML5**: 语义化标记
- **CSS3**: 响应式设计，支持黑暗模式
- **JavaScript ES6+**: 模块化开发
- **Tailwind CSS**: 实用优先的CSS框架
- **Font Awesome**: 图标库

### 模块化设计
- **页面模块化**: 每个页面的相关文件集中管理
- **组件化**: 可复用组件独立开发和维护
- **管理器模式**: 业务逻辑封装在专门的管理器类中
- **工具函数**: 通用功能抽取为工具类

### 路径管理
- **绝对路径**: 所有资源引用使用以"/"开头的绝对路径
- **Spring Boot映射**: 静态资源映射到根路径
- **组件引用**: 组件间通过明确的路径引用

## 📋 开发规范

### 文件组织
- 页面相关文件（HTML、CSS、JS）放在同一目录下
- 共享资源放在对应的共享目录中
- 组件文件独立组织，便于复用

### 命名规范
- 页面目录使用功能名称（如dialog、review、profile）
- 脚本文件使用功能前缀（如review-actions.js、dialog-manager.js）
- 样式文件与对应页面同名

### 代码组织
- 每个文件职责单一，便于维护
- 使用ES6+语法和模块化开发
- 统一的错误处理和用户反馈
- 清晰的注释和文档

### 路径引用规范
- HTML中的资源引用使用绝对路径
- JavaScript中的动态加载使用绝对路径
- 组件间引用通过明确的路径指定

## 🔄 页面导航

### 主要页面路径
- 首页: `/index.html`
- 场景对话: `/pages/dialog/dialog.html`
- 单词复习: `/pages/review/review.html`
- 个人主页: `/pages/profile/profile.html`
- 会员订阅: `/pages/plan/plan.html`
- 建议反馈: `/pages/suggestions/suggestions.html`

### 错误页面
- 404错误: `/pages/error/404.html`
- 500错误: `/pages/error/500.html`

### 工具页面
- 语音合成: `/pages/tools/text-to-speech.html`
- 数据仪表板: `/pages/tools/data-dashboard.html`
