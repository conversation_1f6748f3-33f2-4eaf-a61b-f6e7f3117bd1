#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
转录音频文件为字幕文件
使用faster-whisper库
"""

import argparse
import os
import sys
import time
import logging
from datetime import timedelta
from typing import List, Dict, Any, Tuple, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger('transcribe')

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="使用faster-whisper转录音频文件")
    parser.add_argument("--audio", type=str, required=True, help="音频文件路径")
    parser.add_argument("--model", type=str, default="base", help="模型大小 (tiny, base, small, medium, large-v1, large-v2)")
    parser.add_argument("--device", type=str, default="cpu", help="设备类型 (cpu, cuda, mps)")
    parser.add_argument("--compute_type", type=str, default="float32", help="计算类型 (float32, float16, int8)")
    parser.add_argument("--language", type=str, default="auto", help="音频语言代码 (en, ja, zh, auto)")
    parser.add_argument("--output", type=str, required=True, help="输出文件路径")
    parser.add_argument("--format", type=str, default="vtt", choices=["vtt", "srt"], help="输出格式")
    parser.add_argument("--verbose", action="store_true", help="显示详细输出")
    parser.add_argument("--num_threads", type=int, default=1, help="并行处理线程数")
    
    return parser.parse_args()

def format_timestamp(seconds: float, always_include_hours: bool = False) -> str:
    """将秒数格式化为VTT时间戳格式"""
    hours = int(seconds // 3600)
    seconds = seconds - hours * 3600
    minutes = int(seconds // 60)
    seconds = seconds - minutes * 60
    
    if always_include_hours or hours > 0:
        return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}".replace(".", ",")
    else:
        return f"{minutes:02d}:{seconds:06.3f}".replace(".", ",")

def write_vtt(segments: List[Dict[str, Any]], output_path: str) -> None:
    """将转录结果写入VTT文件"""
    with open(output_path, "w", encoding="utf-8") as f:
        f.write("WEBVTT\n\n")
        
        for i, segment in enumerate(segments):
            # 获取开始和结束时间
            start = segment["start"]
            end = segment["end"]
            
            # 格式化时间戳 (VTT使用小数点而不是逗号)
            start_str = format_timestamp(start).replace(",", ".")
            end_str = format_timestamp(end).replace(",", ".")
            
            # 写入时间戳行
            f.write(f"{start_str} --> {end_str}\n")
            
            # 写入文本行
            f.write(f"{segment['text'].strip()}\n\n")
    
    logger.info(f"VTT文件已保存到: {output_path}")

def main():
    """主函数"""
    args = parse_args()
    
    # 检查音频文件是否存在
    if not os.path.isfile(args.audio):
        logger.error(f"音频文件不存在: {args.audio}")
        sys.exit(1)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    logger.info(f"开始转录音频: {args.audio}")
    logger.info(f"使用模型: {args.model}, 设备: {args.device}, 计算类型: {args.compute_type}, 线程数: {args.num_threads}")
    
    try:
        # 导入faster-whisper库
        try:
            from faster_whisper import WhisperModel
        except ImportError:
            logger.error("未找到faster-whisper库，请安装: pip install faster-whisper")
            sys.exit(1)
        
        # 初始化模型
        start_time = time.time()
        logger.info("加载模型中...")
        
        model = WhisperModel(
            args.model,
            device=args.device,
            compute_type=args.compute_type,
            num_workers=args.num_threads  # 设置并行处理线程数
        )
        
        logger.info(f"模型加载完成，耗时: {time.time() - start_time:.2f}秒")
        
        # 转录音频
        logger.info("开始转录...")
        start_time = time.time()
        
        segments, info = model.transcribe(
            args.audio,
            beam_size=5,
            language=None if args.language == "auto" else args.language,
            vad_filter=True,
            vad_parameters=dict(min_silence_duration_ms=500),
            word_timestamps=False
        )
        
        # 收集转录结果
        result_segments = []
        for segment in segments:
            result_segments.append({
                "start": segment.start,
                "end": segment.end,
                "text": segment.text
            })
            if args.verbose:
                logger.info(f"[{segment.start:.2f}s -> {segment.end:.2f}s] {segment.text}")
        
        transcription_time = time.time() - start_time
        logger.info(f"转录完成，耗时: {transcription_time:.2f}秒")
        logger.info(f"检测到的语言: {info.language} (概率: {info.language_probability:.2f})")
        
        # 写入输出文件
        if args.format == "vtt":
            write_vtt(result_segments, args.output)
        else:
            logger.error(f"不支持的输出格式: {args.format}")
            sys.exit(1)
        
        logger.info(f"转录完成，结果已保存到: {args.output}")
        
    except Exception as e:
        logger.error(f"转录过程中出错: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 