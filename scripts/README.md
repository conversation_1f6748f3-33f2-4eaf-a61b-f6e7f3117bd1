# 音频转录脚本

本目录包含用于音频转录的Python脚本，使用faster-whisper库将音频文件转录为VTT格式的字幕文件。

## 安装依赖

```bash
# 安装依赖
pip install -r requirements.txt

# 如果使用Apple Silicon Mac，还需要安装特定的PyTorch版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/nightly/cpu
```

## 使用方法

```bash
python transcribe.py --audio /path/to/audio.mp3 --output /path/to/output.vtt --model base --device mps --language en
```

### 参数说明

- `--audio`: 音频文件路径（必需）
- `--model`: 模型大小，可选值：tiny, base, small, medium, large-v1, large-v2（默认：base）
- `--device`: 设备类型，可选值：cpu, cuda, mps（默认：cpu）
- `--compute_type`: 计算类型，可选值：float32, float16, int8_float16（默认：float32）
- `--language`: 音频语言代码，例如：en, ja, zh, auto（默认：auto）
- `--output`: 输出文件路径（必需）
- `--format`: 输出格式，可选值：vtt（默认：vtt）
- `--verbose`: 显示详细输出

## 设备选择

- 对于CPU：使用 `--device cpu`
- 对于NVIDIA GPU：使用 `--device cuda --compute_type float16`
- 对于Apple Silicon：使用 `--device mps --compute_type int8_float16`

## 模型选择

模型大小会影响转录质量和速度：

- `tiny`: 最快但质量最低
- `base`: 快速，质量适中
- `small`: 平衡速度和质量
- `medium`: 较高质量，速度较慢
- `large-v1`/`large-v2`: 最高质量，速度最慢

对于大多数情况，`base`或`small`模型已经足够。 