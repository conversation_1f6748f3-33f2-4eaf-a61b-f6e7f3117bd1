// Subtitle Core Module - 核心字幕管理模块
class SubtitleCore {
    // 静态属性，用于记录上次会话验证时间
    static lastSessionCheckTime = 0;
    // 权限缓存相关
    static permissionCache = {
        isPlusOrPremium: false,
        lastCheckTime: 0,
        cacheValidDuration: 5 * 60 * 1000 // 5分钟缓存
    };

    constructor(playerContainer) {
        this.playerContainer = playerContainer;
        this.jaSubtitles = [];
        this.zhSubtitles = [];
        this.subtitleInterval = null;
        this.lastSubtitleText = { ja: '', zh: '' };
        this.isLoading = false;
        this.subtitleFileId = null;
        this.subtitleJaPath = '';
        this.subtitleZhPath = '';
        this.areSubtitlePathsFetched = false;
        this.videoUrl = '';
        
        console.log('字幕核心管理器初始化完成');
    }

    // 获取当前选择的语言
    async getSelectedLanguage() {
        return new Promise((resolve) => {
            chrome.storage.sync.get(['selectedVideoLanguage'], function(result) {
                const language = result.selectedVideoLanguage || 'english'; // 默认为英语
                resolve(language);
            });
        });
    }

    // 显示字幕加载动画
    showSubtitleLoadingAnimation() {
        const jaSubtitleElement = document.getElementById('extension-subtitle-ja');
        const zhSubtitleElement = document.getElementById('extension-subtitle-zh');
        const loadingHint = document.getElementById('extension-loading-hint');

        if (loadingHint) {
            loadingHint.style.display = 'flex';
            loadingHint.className = 'subtitle-loading-hint';
            loadingHint.classList.remove('hidden'); // 确保移除隐藏类
            loadingHint.innerHTML = `
                <div class="loading-content">
                    <div class="loading-text">正在从YouTube获取字幕，请稍候...</div>
                </div>
            `;
        }

        if (jaSubtitleElement) jaSubtitleElement.innerHTML = '';
        if (zhSubtitleElement) zhSubtitleElement.textContent = '';
    }

    // 更新字幕加载提示信息
    updateSubtitleLoadingHint(message, color) {
        const loadingHint = document.getElementById('extension-loading-hint');
        if (loadingHint) {
            loadingHint.style.display = 'flex';
            loadingHint.className = `subtitle-loading-hint`;
            loadingHint.classList.remove('hidden'); // 确保移除隐藏类
            loadingHint.innerHTML = `
                <div class="loading-content">
                    <div class="loading-text">${message}</div>
                </div>
            `;
        }
    }

    // 显示字幕加载成功提示并自动隐藏
    showSubtitleLoadingSuccess(message) {
        const loadingHint = document.getElementById('extension-loading-hint');
        if (loadingHint) {
            loadingHint.style.display = 'flex';
            loadingHint.className = 'subtitle-loading-hint';
            loadingHint.classList.remove('hidden');
            loadingHint.innerHTML = `
                <div class="loading-content">
                    <div class="success-icon">✓</div>
                    <div class="loading-text">${message}</div>
                </div>
            `;

            // 2秒后自动隐藏
            setTimeout(() => {
                const currentLoadingHint = document.getElementById('extension-loading-hint');
                if (currentLoadingHint) {
                    currentLoadingHint.style.display = 'none';
                    currentLoadingHint.classList.add('hidden');
                }
            }, 2000);
        }
    }

    // 与注入的字幕监听器通信
    async sendMessageToSubtitleListener(action, data = null, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const requestId = Date.now() + Math.random();

            // 设置超时
            const timeoutId = setTimeout(() => {
                window.removeEventListener('message', responseHandler);
                reject(new Error(`字幕监听器通信超时: ${action}`));
            }, timeout);

            // 响应处理器
            const responseHandler = (event) => {
                if (event.source !== window) return;

                const message = event.data;
                if (!message ||
                    message.type !== 'SUBTITLE_LISTENER_RESPONSE' ||
                    message.requestId !== requestId) {
                    return;
                }

                clearTimeout(timeoutId);
                window.removeEventListener('message', responseHandler);

                if (message.success) {
                    resolve(message.data);
                } else {
                    reject(new Error(message.error || '字幕监听器操作失败'));
                }
            };

            // 监听响应
            window.addEventListener('message', responseHandler);

            // 发送请求
            window.postMessage({
                type: 'SUBTITLE_LISTENER_REQUEST',
                requestId: requestId,
                action: action,
                data: data
            }, '*');
        });
    }

    // 从前端三语言URL获取字幕
    async fetchSubtitlesFromFrontend() {
        console.log('[前端字幕获取] 开始从前端获取字幕...');

        try {
            // 检查字幕监听器是否可用
            console.log('[前端字幕获取] 检查字幕监听器状态...');
            const status = await this.sendMessageToSubtitleListener('checkStatus');
            console.log('[前端字幕获取] 字幕监听器状态:', status);

            if (!status.isAvailable) {
                throw new Error('字幕监听器不可用');
            }

            // 获取当前选择的语言
            const selectedLanguage = await this.getSelectedLanguage();
            console.log('[前端字幕获取] 当前选择的语言:', selectedLanguage);

            // 优先尝试获取三语言URL
            let subtitleUrls = null;
            try {
                console.log('[前端字幕获取] 尝试获取三语言URL...');
                subtitleUrls = await this.sendMessageToSubtitleListener('getTrilingualUrls');
                console.log('[前端字幕获取] 三语言URL:', subtitleUrls);
            } catch (error) {
                console.warn('[前端字幕获取] 获取三语言URL失败，回退到双语URL:', error);
            }

            // 如果三语言URL不可用，回退到双语URL
            if (!subtitleUrls || Object.keys(subtitleUrls).length === 0) {
                console.log('[前端字幕获取] 回退到双语URL...');
                subtitleUrls = await this.sendMessageToSubtitleListener('getBilingualUrls');
                console.log('[前端字幕获取] 双语URL:', subtitleUrls);
            }

            // 检查是否有可用的字幕URL
            if (!subtitleUrls || Object.keys(subtitleUrls).length === 0) {
                throw new Error('字幕URL未准备就绪');
            }

            // 根据选择的语言获取目标语言字幕
            let targetSubtitles = [];
            let targetLanguageUrl = null;

            if (selectedLanguage === 'english' && subtitleUrls.english) {
                targetLanguageUrl = subtitleUrls.english.url;
                console.log('[前端字幕获取] 使用英文字幕URL');
            } else if (selectedLanguage === 'japanese' && subtitleUrls.japanese) {
                targetLanguageUrl = subtitleUrls.japanese.url;
                console.log('[前端字幕获取] 使用日文字幕URL');
            } else if (subtitleUrls.target) {
                // 回退到双语URL的target
                targetLanguageUrl = subtitleUrls.target.url;
                console.log('[前端字幕获取] 使用双语URL的目标语言字幕');
            }

            if (targetLanguageUrl) {
                console.log('[前端字幕获取] 获取目标语言字幕...');
                const targetResponse = await fetch(targetLanguageUrl);
                if (!targetResponse.ok) {
                    throw new Error(`目标语言字幕请求失败: ${targetResponse.status}`);
                }
                const targetJson3 = await targetResponse.json();
                const targetVtt = SubtitleUtils.json3ToVTT(targetJson3);
                targetSubtitles = SubtitleUtils.parseVTT(targetVtt);
                console.log(`[前端字幕获取] 目标语言字幕解析完成: ${targetSubtitles.length} 条`);
            }

            // 获取中文字幕
            let chineseSubtitles = [];
            if (subtitleUrls.chinese) {
                console.log('[前端字幕获取] 获取中文字幕...');
                const chineseResponse = await fetch(subtitleUrls.chinese.url);
                if (!chineseResponse.ok) {
                    throw new Error(`中文字幕请求失败: ${chineseResponse.status}`);
                }
                const chineseJson3 = await chineseResponse.json();
                const chineseVtt = SubtitleUtils.json3ToVTT(chineseJson3);
                chineseSubtitles = SubtitleUtils.parseVTT(chineseVtt);
                console.log(`[前端字幕获取] 中文字幕解析完成: ${chineseSubtitles.length} 条`);
            }

            // 保存字幕数据
            this.jaSubtitles = targetSubtitles;
            this.zhSubtitles = chineseSubtitles;

            console.log('[前端字幕获取] 字幕获取成功！');
            return true;

        } catch (error) {
            console.error('[前端字幕获取] 获取失败:', error);
            throw error;
        }
    }

    // 从服务器获取字幕路径信息（作为fallback）
    async fetchSubtitlesFromServer() {
        try {
            console.log(`[获取字幕路径] 开始请求字幕路径`);
            
            // 记录之前的字幕文件ID，用于清理
            const previousSubtitleFileId = this.subtitleFileId;
            
            // 获取视频URL
            const videoUrl = window.location.href;
            console.log(`[获取字幕路径] 视频URL: ${videoUrl}`);
            
            // 获取当前选择的语言
            const selectedLanguage = await this.getSelectedLanguage();
            console.log(`[获取字幕路径] 选择的语言: ${selectedLanguage}`);
            
            // 通过background script发送请求
            const data = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'fetchExtensionSubtitles',
                    videoUrl: videoUrl,
                    language: selectedLanguage,
                    forceNewSession: false,
                    timestamp: new Date().getTime()
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else if (!response || !response.success) {
                        reject(new Error(response?.error || '获取字幕失败'));
                    } else {
                        resolve(response);
                    }
                });
            });
            
            // 更新字幕文件ID和路径
            this.subtitleFileId = data.subtitleFileId;
            this.subtitleJaPath = data.subtitleJa || '';
            this.subtitleZhPath = data.subtitleZh || '';
            
            // 获取语言信息用于显示
            const primaryLanguageDisplayName = data.primaryLanguageDisplayName || '主语言';
            const translationLanguageDisplayName = data.translationLanguageDisplayName || '翻译语言';
            
            console.log(`[获取字幕路径] 成功 - 字幕文件ID: ${this.subtitleFileId}`);
            console.log(`[获取字幕路径] ${primaryLanguageDisplayName}字幕: ${this.subtitleJaPath}`);
            console.log(`[获取字幕路径] ${translationLanguageDisplayName}字幕: ${this.subtitleZhPath}`);
            
            // 如果字幕文件ID发生变化，清理旧的字幕文件
            if (previousSubtitleFileId && previousSubtitleFileId !== this.subtitleFileId) {
                console.log(`[获取字幕路径] 字幕文件ID已更新: ${previousSubtitleFileId} -> ${this.subtitleFileId}`);
                console.log('[获取字幕路径] 清理旧的字幕文件');
                
                // 向background.js发送请求清理旧的字幕文件
                chrome.runtime.sendMessage({
                    action: 'cleanupExtensionSubtitles',
                    subtitleFileId: previousSubtitleFileId
                }, (response) => {
                    console.log('[获取字幕路径] 旧字幕文件清理结果:', response);
                });
            }

            // 显示警告信息
            if (data.warning) {
                console.warn(`[获取字幕路径] 警告: ${data.warning}`);
            }
            
            this.areSubtitlePathsFetched = true;
            
            // 重要修复：获取字幕路径后调用onPlayerReady处理字幕
            console.log('[获取字幕路径] 字幕路径已获取，调用onPlayerReady处理字幕');
            await this.onPlayerReady();
            
        } catch (error) {
            console.error(`[获取字幕路径] 网络错误: ${error.message}`);
            this.updateSubtitleLoadingHint(`网络错误，字幕获取失败`, "red");

            this.areSubtitlePathsFetched = true;
            this.subtitleJaPath = '';
            this.subtitleZhPath = '';
            await this.onPlayerReady();
        }
    }

    // 加载字幕
    async loadSubtitles(videoUrl) {
        if (this.isLoading) {
            console.log('字幕正在加载中，跳过重复请求');
            return;
        }

        this.isLoading = true;
        this.videoUrl = videoUrl;
        console.log('开始加载字幕:', videoUrl);

        try {
            // 立即清空字幕状态和显示区域，防止视频切换时字幕重叠
            this.clearSubtitleState();
            this.showSubtitleLoadingAnimation();

            // 优先尝试从前端获取字幕
            try {
                console.log('尝试从前端获取字幕...');
                await this.fetchSubtitlesFromFrontend();

                // 前端获取成功，显示成功提示并自动隐藏
                this.showSubtitleLoadingSuccess('字幕加载完成');

                // 开始字幕更新
                if (this.subtitleInterval) clearInterval(this.subtitleInterval);
                this.subtitleInterval = setInterval(() => this.updateSubtitles(), 100);

                console.log('前端字幕获取成功，开始显示字幕');
                return;

            } catch (frontendError) {
                console.warn('前端字幕获取失败，回退到后端方式:', frontendError.message);

                // 回退到后端获取
                await this.fetchSubtitlesFromServer();
            }

        } catch (error) {
            console.error('字幕加载失败:', error);
            this.updateSubtitleLoadingHint(`字幕加载失败: ${error.message}`, 'red');
        } finally {
            this.isLoading = false;
        }
    }

    // 清空字幕状态
    clearSubtitleState() {
        console.log('清空字幕状态');
        
        // 停止字幕更新定时器
        if (this.subtitleInterval) {
            clearInterval(this.subtitleInterval);
            this.subtitleInterval = null;
        }
        
        // 清空字幕数据
        this.jaSubtitles = [];
        this.zhSubtitles = [];
        this.lastSubtitleText = { ja: '', zh: '' };
        
        // 重置路径获取状态
        this.areSubtitlePathsFetched = false;
        this.subtitleJaPath = '';
        this.subtitleZhPath = '';
        
        // 立即清空字幕显示区域
        const jaSubtitleEl = document.getElementById('extension-subtitle-ja');
        const zhSubtitleEl = document.getElementById('extension-subtitle-zh');
        if (jaSubtitleEl) jaSubtitleEl.innerHTML = '';
        if (zhSubtitleEl) zhSubtitleEl.textContent = '';
    }

    // onPlayerReady - 优化字幕加载逻辑
    async onPlayerReady() {
        if (!this.areSubtitlePathsFetched) {
            console.log('字幕路径尚未从服务器获取，onPlayerReady 将等待。');
            return;
        }

        const loadingHint = document.getElementById('extension-loading-hint');
        const jaSubtitleEl = document.getElementById('extension-subtitle-ja');
        const zhSubtitleEl = document.getElementById('extension-subtitle-zh');

        // 无论如何，先清空字幕区域
        if (jaSubtitleEl) jaSubtitleEl.innerHTML = '';
        if (zhSubtitleEl) zhSubtitleEl.textContent = '';

        const jaPath = this.subtitleJaPath || '';
        const zhPath = this.subtitleZhPath || '';

        // 获取当前选择的语言用于显示
        const selectedLanguage = await this.getSelectedLanguage();
        const primaryLanguageName = selectedLanguage === 'english' ? '英文' : '日文';
        const translationLanguageName = '中文';

        console.log(`[准备加载字幕] ${primaryLanguageName}路径: ${jaPath}, ${translationLanguageName}路径: ${zhPath}`);

        if (!jaPath && !zhPath) {
            if (loadingHint) {
                loadingHint.style.display = 'flex';
                loadingHint.className = 'subtitle-loading-hint';
                loadingHint.classList.remove('hidden'); // 确保移除隐藏类
                loadingHint.innerHTML = `
                    <div class="loading-content">
                        <img src="${chrome.runtime.getURL('assets/images/warning.png')}" style="width: 16px; height: 16px; margin-right: 8px;" alt="警告">
                        <div class="loading-text" style="color: #f59e0b;">未找到任何字幕文件，premium会员可以使用字幕转录功能。</div>
                    </div>
                `;
            }
            return;
        }

        // 确保字幕文件已完全写入
        console.log('[字幕加载] 等待2秒确保服务器文件准备就绪...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('[字幕加载] 开始加载字幕文件');

        try {
            // 分别加载主语言和中文字幕
            if (jaPath) {
                this.jaSubtitles = await this.loadSubtitleFile(jaPath, 'ja');
            } else {
                this.jaSubtitles = [];
            }

            if (zhPath) {
                this.zhSubtitles = await this.loadSubtitleFile(zhPath, 'zh');
            } else {
                this.zhSubtitles = [];
            }
            
            console.log('[字幕加载] 所有字幕加载完成');
            
            if (jaSubtitleEl) jaSubtitleEl.innerHTML = '';
            if (zhSubtitleEl) zhSubtitleEl.textContent = '';

            const jaLoaded = this.jaSubtitles && this.jaSubtitles.length > 0;
            const zhLoaded = this.zhSubtitles && this.zhSubtitles.length > 0;

            // 如果有字幕加载成功，设置更新字幕的定时器
            if (jaLoaded || zhLoaded) {
                console.log(`[字幕加载] 有效字幕加载成功 - ${primaryLanguageName}: ${jaLoaded ? this.jaSubtitles.length + '条' : '无'}, ${translationLanguageName}: ${zhLoaded ? this.zhSubtitles.length + '条' : '无'}`);

                // 显示成功加载提示并自动隐藏
                console.log('[字幕加载] 显示成功加载提示');
                this.showSubtitleLoadingSuccess('字幕加载成功');

                // 设置更新字幕的定时器
                this.updateSubtitles();
                if (this.subtitleInterval) clearInterval(this.subtitleInterval);
                this.subtitleInterval = setInterval(() => this.updateSubtitles(), 100);
            } else {
                console.error('[字幕加载] 所有字幕加载失败');
            }

            // 处理部分加载成功的情况
            if (loadingHint && !jaLoaded && !zhLoaded) {
                // 只有在完全加载失败时才显示错误信息
                console.log('[字幕加载] 所有字幕加载失败，显示错误提示');
            } else if (loadingHint && ((jaLoaded && !zhLoaded) || (!jaLoaded && zhLoaded))) {
                // 部分加载成功的情况，显示警告信息
                const successLang = jaLoaded ? primaryLanguageName : translationLanguageName;
                const failedLang = jaLoaded ? translationLanguageName : primaryLanguageName;

                setTimeout(() => {
                    if (loadingHint) {
                        loadingHint.style.display = 'flex';
                        loadingHint.className = 'subtitle-loading-hint';
                        loadingHint.classList.remove('hidden'); // 确保移除隐藏类
                        loadingHint.innerHTML = `
                            <div class="loading-content">
                                <img src="${chrome.runtime.getURL('assets/images/warning.png')}" style="width: 16px; height: 16px; margin-right: 8px;" alt="警告">
                                <div class="loading-text" style="color: #f59e0b;">
                                    ${successLang}字幕已加载，但${failedLang}字幕加载失败。
                                    <button id="retry-subtitle-btn" style="margin-left: 10px; padding: 4px 8px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">重试</button>
                                </div>
                            </div>
                        `;
                        // 添加重试按钮事件
                        const retryBtn = document.getElementById('retry-subtitle-btn');
                        if (retryBtn) {
                            retryBtn.addEventListener('click', () => this.retrySubtitleLoad());
                        }
                    }
                }, 2500); // 在成功提示消失后显示
            }

        } catch (error) {
            console.error(`[字幕加载] 加载字幕过程中出错: ${error.message}`);
            if (loadingHint) {
                this.updateSubtitleLoadingHint(`字幕加载错误: ${error.message}`, 'red');
            }
        }
    }

    // 从服务器下载并解析字幕文件
    async loadSubtitleFile(subtitlePath, language) {
        // 获取当前选择的语言用于显示正确的名称
        const selectedLanguage = await this.getSelectedLanguage();
        const languageDisplayName = language === 'ja' ? 
            (selectedLanguage === 'english' ? '英文' : '日文') : 
            '中文';
            
        console.log(`尝试加载${languageDisplayName}字幕文件: ${subtitlePath}`);
        try {
            // 构建完整的绝对URL
            const fullSubtitleUrl = `http://localhost:8080${subtitlePath}`;
            console.log(`请求${languageDisplayName}字幕的完整URL: ${fullSubtitleUrl}`);

            // 通过background script代理请求字幕文件
            const response = await chrome.runtime.sendMessage({
                action: 'proxyFetch',
                url: fullSubtitleUrl,
                method: 'GET',
                headers: {},
                responseType: 'text' // 明确指定响应类型为文本
            });

            if (response.success && response.data) {
                const vttContent = response.data;
                const parsedSubtitles = SubtitleUtils.parseVTT(vttContent);
                console.log(`${languageDisplayName}字幕解析完成，共 ${parsedSubtitles.length} 条`);
                return parsedSubtitles;
            } else {
                throw new Error(response.error || `通过Background Script获取${languageDisplayName}字幕失败`);
            }
        } catch (error) {
            console.error(`加载${languageDisplayName}字幕文件失败:`, error);
            this.updateSubtitleLoadingHint(`加载${languageDisplayName}字幕失败: ${error.message}`, 'red');
            throw error;
        }
    }

    // 检查Plus/Premium权限（带缓存）
    async checkPlusOrPremiumPermissionCached() {
        const now = Date.now();
        const cache = SubtitleCore.permissionCache;
        
        // 如果缓存仍然有效，直接返回缓存结果
        if (cache.lastCheckTime && (now - cache.lastCheckTime) < cache.cacheValidDuration) {
            console.log('使用缓存的权限结果:', cache.isPlusOrPremium);
            return cache.isPlusOrPremium;
        }
        
        console.log('权限缓存已过期或不存在，重新检查权限');
        
        try {
            // 通过background.js检查权限
            const permissionResult = await new Promise((resolve) => {
                chrome.runtime.sendMessage({
                    action: 'checkPlusOrPremiumPermission'
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.error('检查Plus/Premium权限失败:', chrome.runtime.lastError.message);
                        resolve({
                            success: false,
                            isPlusOrPremium: false
                        });
                    } else {
                        resolve(response);
                    }
                });
            });
            
            // 更新缓存
            cache.isPlusOrPremium = permissionResult.success && permissionResult.isPlusOrPremium;
            cache.lastCheckTime = now;
            
            console.log('权限检查完成，结果:', cache.isPlusOrPremium);
            return cache.isPlusOrPremium;
            
        } catch (error) {
            console.error('权限检查失败:', error);
            // 出错时默认为false，但不更新缓存时间，下次会重试
            return false;
        }
    }

    // 清除权限缓存（用于用户登录/登出时）
    static clearPermissionCache() {
        console.log('清除权限缓存');
        SubtitleCore.permissionCache = {
            isPlusOrPremium: false,
            lastCheckTime: 0,
            cacheValidDuration: 5 * 60 * 1000
        };
    }

    // 更新字幕显示
    async updateSubtitles() {
        const videoElement = this.playerContainer.querySelector('video');
        if (!videoElement) {
            return;
        }
        
        const currentTime = videoElement.currentTime;
        
        const jaSubtitle = SubtitleUtils.findSubtitleByTime(this.jaSubtitles, currentTime) || { text: '' };
        const zhSubtitle = SubtitleUtils.findSubtitleByTime(this.zhSubtitles, currentTime) || { text: '' };

        if (jaSubtitle.text !== this.lastSubtitleText.ja) {
            const jaElement = document.getElementById('extension-subtitle-ja');
            if (jaElement) {
                if (jaSubtitle.text !== '') {
                    // 检查Plus/Premium权限（使用缓存）
                    const hasAdvancedPermission = await this.checkPlusOrPremiumPermissionCached();
                    
                    if (hasAdvancedPermission) {
                        // Plus/Premium用户：显示彩色词性标注
                        try {
                            // 获取当前选择的语言
                            const selectedLanguage = await this.getSelectedLanguage();
                            
                            const response = await chrome.runtime.sendMessage({
                                action: 'proxyFetch',
                                url: 'http://localhost:8080/api/subtitle/tag',
                                method: 'POST',
                                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                                body: `text=${encodeURIComponent(jaSubtitle.text)}&language=${encodeURIComponent(selectedLanguage)}`,
                                responseType: 'json'
                            });

                            if (response.success && response.data) {
                                const data = response.data;
                                const coloredText = data.tagged_text.map(word => {
                                    const span = `<span class="pos-${word.pos.toLowerCase()}">${word.text}</span>`;
                                    // 只有日语才显示读音，英语不显示
                                    if (selectedLanguage === 'japanese' && word.reading) {
                                        return `<ruby>${span}<rt>${word.reading}</rt></ruby>`;
                                    } else {
                                        return span;
                                    }
                                }).join(selectedLanguage === 'english' ? ' ' : ''); // 英语用空格分隔，日语不分隔
                                jaElement.innerHTML = coloredText;
                            } else {
                                throw new Error(response.error || '词性标注失败');
                            }
                        } catch (error) {
                            console.error('词性标注失败:', error);
                            // 降级到普通显示
                            jaElement.innerHTML = this.createPlainSubtitleHTML(jaSubtitle.text);
                        }
                    } else {
                        // 免费用户：显示普通字幕（无彩色标注）
                        jaElement.innerHTML = this.createPlainSubtitleHTML(jaSubtitle.text);
                    }
                } else {
                    jaElement.innerHTML = '';
                }
            }
            this.lastSubtitleText.ja = jaSubtitle.text;
        }

        if (zhSubtitle.text !== this.lastSubtitleText.zh) {
            const zhElement = document.getElementById('extension-subtitle-zh');
            if (zhElement) {
                zhElement.textContent = zhSubtitle.text;
            }
            this.lastSubtitleText.zh = zhSubtitle.text;
        }
    }

    // 创建普通字幕HTML（免费用户使用）
    createPlainSubtitleHTML(text) {
        // 为免费用户创建不带词性标注的字幕
        // 保持原有的字幕样式，只是不添加pos-类，这样不会触发tooltip
        return text; // 直接返回文本，保持原有样式
    }

    // 通过background.js加载字幕文件
    async loadSubtitleViaBackground(subtitlePath, language) {
        if (!subtitlePath) {
            // 获取当前选择的语言用于显示正确的名称
            const selectedLanguage = await this.getSelectedLanguage();
            const languageDisplayName = language === 'ja' ? 
                (selectedLanguage === 'english' ? '英文' : '日文') : 
                '中文';
            console.log(`${languageDisplayName}字幕路径为空，跳过加载`);
            return;
        }
        
        // 获取当前选择的语言用于显示正确的名称
        const selectedLanguage = await this.getSelectedLanguage();
        const languageDisplayName = language === 'ja' ? 
            (selectedLanguage === 'english' ? '英文' : '日文') : 
            '中文';
        
        console.log(`通过background.js加载${languageDisplayName}字幕: ${subtitlePath}`);
        
        try {
            // 构建完整URL
            const fileUrl = `http://localhost:8080${subtitlePath}`;
            
            // 通过background.js发送请求
            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'proxyFetch',
                    url: fileUrl,
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else if (!response || !response.success) {
                        reject(new Error(response?.error || '获取字幕失败'));
                    } else {
                        resolve(response);
                    }
                });
            });
            
            // 获取VTT内容
            const content = response.data;
            
            // 检查内容是否为空或无效
            if (!content) {
                throw new Error('获取到的字幕内容为空');
            }
            
            // 确保内容是字符串类型
            let vttContent = content;
            if (typeof content !== 'string') {
                console.warn(`${languageDisplayName}字幕内容不是字符串类型，尝试转换`);
                try {
                    if (typeof content === 'object' && content.originalText) {
                        vttContent = content.originalText;
                    } else {
                        vttContent = String(content);
                    }
                } catch (e) {
                    console.error('转换字幕内容为字符串失败:', e);
                    throw new Error('字幕内容格式无效');
                }
            }
            
            // 检查响应是否为HTML（错误页面）
            if (vttContent.includes('<!DOCTYPE html>')) {
                throw new Error('服务器返回了HTML而不是字幕文件');
            }
            
            // 检查是否是有效的VTT内容
            if (!vttContent.includes('WEBVTT')) {
                console.warn(`${languageDisplayName}字幕内容不是标准的VTT格式，尝试修复`);
                const fixedContent = 'WEBVTT\n\n' + vttContent;
                const subtitles = SubtitleUtils.parseVTT(fixedContent);
                console.log(`${languageDisplayName}字幕解析结果(修复后): ${subtitles.length}条`);
                
                if (language === 'ja') {
                    this.jaSubtitles = subtitles;
                } else if (language === 'zh') {
                    this.zhSubtitles = subtitles;
                }
                return;
            }
            
            // 解析字幕内容
            const subtitles = SubtitleUtils.parseVTT(vttContent);
            console.log(`${languageDisplayName}字幕解析结果: ${subtitles.length}条`);
            
            // 保存解析结果
            if (language === 'ja') {
                this.jaSubtitles = subtitles;
            } else if (language === 'zh') {
                this.zhSubtitles = subtitles;
            }
            
        } catch (error) {
            console.error(`加载${languageDisplayName}字幕失败:`, error);
            throw error;
        }
    }

    // 清理字幕文件
    cleanupSubtitles() {
        if (this.subtitleFileId) {
            console.log('[清理] 请求清理字幕文件');
            chrome.runtime.sendMessage({
                action: 'cleanupExtensionSubtitles',
                subtitleFileId: this.subtitleFileId
            }, (response) => {
                console.log('[清理] 字幕文件清理结果:', response);
            });
        }
    }

    // 清理核心管理器
    cleanup() {
        console.log('开始清理字幕核心管理器');

        if (this.subtitleInterval) {
            clearInterval(this.subtitleInterval);
            this.subtitleInterval = null;
        }

        this.jaSubtitles = [];
        this.zhSubtitles = [];
        this.lastSubtitleText = { ja: '', zh: '' };

        this.cleanupSubtitles();
        console.log('字幕核心管理器清理完成');
    }

    // 重试字幕加载
    async retrySubtitleLoad() {
        console.log('[字幕重试] 开始重试字幕加载');
        
        const loadingHint = document.getElementById('extension-loading-hint');
        if (loadingHint) {
            loadingHint.style.display = 'flex';
            loadingHint.className = 'subtitle-loading-hint';
            loadingHint.classList.remove('hidden'); // 确保移除隐藏类
            loadingHint.innerHTML = `
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">正在重新加载字幕...</div>
                </div>
            `;
        }
        
        try {
            // 清空现有字幕
            this.jaSubtitles = [];
            this.zhSubtitles = [];
            
            // 重新获取字幕路径
            await this.fetchSubtitlePaths();
            
            // 重新加载字幕
            await this.onPlayerReady();
            
        } catch (error) {
            console.error('[字幕重试] 重试失败:', error);
            if (loadingHint) {
                loadingHint.innerHTML = `
                    <div class="loading-content">
                        <img src="${chrome.runtime.getURL('assets/images/warning.png')}" style="width: 16px; height: 16px; margin-right: 8px;" alt="警告">
                        <div class="loading-text" style="color: #f59e0b;">
                            字幕重试失败: ${error.message}
                            <button id="retry-subtitle-btn" style="margin-left: 10px; padding: 4px 8px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">再次重试</button>
                        </div>
                    </div>
                `;
                // 重新添加重试按钮事件
                const retryBtn = document.getElementById('retry-subtitle-btn');
                if (retryBtn) {
                    retryBtn.addEventListener('click', () => this.retrySubtitleLoad());
                }
            }
        }
    }
}

// 注册到全局对象
window.SubtitleCore = SubtitleCore; 