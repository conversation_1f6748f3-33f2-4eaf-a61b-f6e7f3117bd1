// Subtitle TTS Module - 字幕TTS语音合成模块
class SubtitleTTS {
    constructor(core) {
        this.core = core;
        
        // TTS相关属性
        this.isTTSEnabled = false;
        this.ttsAudio = null;
        this.currentTTSText = '';
        this.ttsToggleBtn = null;
        this.videoElement = null;
        this.isVideoMuted = false;
        this.pendingTTS = false;
        this.lastVideoTime = null;
        this.originalPlaybackRate = 1.0;
        
        // TTS队列管理
        this.ttsQueue = [];
        this.isProcessingQueue = false;
        
        // TTS预加载
        this.nextSubtitleIndex = 0;
        this.preloadedAudio = {};
        this.preloadingSubtitle = false;
        
        console.log('字幕TTS管理器初始化完成');
    }

    // 初始化TTS功能
    init() {
        // 监听字幕文本变化来触发TTS
        this.setupSubtitleWatcher();
    }

    // 设置字幕监听器
    setupSubtitleWatcher() {
        // 监听核心模块的字幕文本变化
        if (this.core.lastSubtitleText) {
            const originalUpdateSubtitles = this.core.updateSubtitles.bind(this.core);
            this.core.updateSubtitles = async () => {
                const prevZhText = this.core.lastSubtitleText.zh;
                await originalUpdateSubtitles();
                const newZhText = this.core.lastSubtitleText.zh;
                
                // 检测视频进度是否发生跳跃
                if (this.videoElement && this.lastVideoTime) {
                    const videoElement = this.core.playerContainer.querySelector('video');
                    if (videoElement) {
                        const timeDiff = Math.abs(videoElement.currentTime - this.lastVideoTime);
                        if (timeDiff > 1 && this.isTTSEnabled) {
                            console.log(`检测到视频进度跳跃: ${this.lastVideoTime.toFixed(2)}s -> ${videoElement.currentTime.toFixed(2)}s，重置TTS`);
                            this.resetTTS();
                        }
                        this.lastVideoTime = videoElement.currentTime;
                        this.videoElement = videoElement;
                    }
                }
                
                // 如果启用了TTS，且新的中文字幕不为空且不同于之前，将其加入朗读队列
                if (this.isTTSEnabled && newZhText && newZhText.trim() !== '' && newZhText !== prevZhText) {
                    this.addToTTSQueue(newZhText);
                    
                    // 尝试预加载下一条字幕
                    const videoElement = this.core.playerContainer.querySelector('video');
                    if (videoElement) {
                        this.preloadNextSubtitle(videoElement.currentTime);
                    }
                }
            };
        }
    }

    // 切换TTS功能
    toggleTTS(enabled) {
        this.isTTSEnabled = enabled;
        console.log(`TTS功能已${enabled ? '启用' : '禁用'}`);
        
        this.videoElement = this.core.playerContainer.querySelector('video');
        if (!this.videoElement) {
            console.error('无法找到视频元素');
            return;
        }
        
        if (enabled) {
            // 启用TTS，静音视频并调整播放速度
            this.isVideoMuted = this.videoElement.muted;
            this.originalPlaybackRate = this.videoElement.playbackRate;
            this.videoElement.muted = true;
            this.videoElement.playbackRate = 0.85; // 设置为0.85倍速
            
            // 开始朗读当前字幕
            const currentZhText = this.core.lastSubtitleText.zh;
            if (currentZhText && currentZhText.trim() !== '') {
                this.addToTTSQueue(currentZhText);
            }
            
            // 尝试预加载下一条字幕
            if (this.videoElement) {
                this.preloadNextSubtitle(this.videoElement.currentTime);
            }
        } else {
            // 禁用TTS，恢复视频声音和播放速度
            this.videoElement.muted = this.isVideoMuted;
            if (this.originalPlaybackRate !== undefined) {
                this.videoElement.playbackRate = this.originalPlaybackRate;
            }
            
            // 停止当前朗读和清空队列
            this.stopSpeaking();
            this.ttsQueue = [];
            
            // 清理预加载的音频
            for (const key in this.preloadedAudio) {
                if (this.preloadedAudio.hasOwnProperty(key)) {
                    delete this.preloadedAudio[key];
                }
            }
        }
        
        // 更新按钮状态
        const ttsButton = document.getElementById('tts-toggle');
        if (ttsButton) {
            if (enabled) {
                ttsButton.classList.add('active');
                ttsButton.textContent = '原声';
            } else {
                ttsButton.classList.remove('active');
                ttsButton.textContent = '中文朗读';
            }
        }
    }

    // 重置TTS状态（停止朗读并清空队列）
    resetTTS() {
        if (!this.isTTSEnabled) return;
        
        console.log('重置TTS状态：停止当前朗读并清空队列');
        
        // 停止当前朗读
        this.stopSpeaking();
        
        // 清空队列
        this.ttsQueue = [];
        
        // 重置处理状态
        this.isProcessingQueue = false;
        this.pendingTTS = false;
    }

    // 将文本加入TTS队列
    addToTTSQueue(text) {
        if (!text || text.trim() === '') return;
        
        // 如果队列为空且当前没有正在播放的TTS，直接播放
        if (this.ttsQueue.length === 0 && !this.isProcessingQueue) {
            this.ttsQueue.push(text);
            this.processTTSQueue();
        } else {
            // 否则加入队列
            this.ttsQueue.push(text);
            console.log(`TTS队列长度: ${this.ttsQueue.length}`);
        }
    }
    
    // 处理TTS队列
    async processTTSQueue() {
        if (this.ttsQueue.length === 0 || this.isProcessingQueue || !this.isTTSEnabled) {
            return;
        }
        
        this.isProcessingQueue = true;
        
        while (this.ttsQueue.length > 0 && this.isTTSEnabled) {
            const text = this.ttsQueue[0];
            
            // 检查是否已预加载
            if (this.preloadedAudio[text]) {
                console.log('使用预加载的TTS音频');
                await this.playAudioFromBlob(this.preloadedAudio[text]);
                // 播放后删除缓存
                delete this.preloadedAudio[text];
            } else {
                // 未预加载，请求生成TTS
                await this.generateAndPlayTTS(text);
            }
            
            // 移除已处理的文本
            this.ttsQueue.shift();
        }
        
        this.isProcessingQueue = false;
    }
    
    // 生成并播放TTS
    async generateAndPlayTTS(text) {
        if (!text || text.trim() === '' || !this.isTTSEnabled) return;
        
        this.pendingTTS = true;
        this.currentTTSText = text;
        
        return new Promise((resolve) => {
            // 通过background.js代理请求后端TTS接口
            chrome.runtime.sendMessage({
                action: 'proxyFetch',
                url: 'http://localhost:8080/api/tts/speak',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text: text,
                    voice: 'zh-CN-XiaoxiaoNeural', // 默认使用小筱声音
                    rate: 1.3 // 设置语速为1.3倍
                })
            }, async response => {
                if (chrome.runtime.lastError) {
                    console.error('TTS请求失败:', chrome.runtime.lastError);
                    this.pendingTTS = false;
                    resolve();
                    return;
                }
                
                if (!response || !response.success) {
                    console.error('TTS生成失败:', response ? response.error : '未知错误');
                    this.pendingTTS = false;
                    resolve();
                    return;
                }
                
                // 将Base64音频数据转换为Blob对象
                const audioBlob = SubtitleUtils.base64ToBlob(response.data);
                
                // 播放音频
                await this.playAudioFromBlob(audioBlob);
                
                this.pendingTTS = false;
                resolve();
            });
        });
    }
    
    // 从Blob播放音频
    async playAudioFromBlob(audioBlob) {
        return new Promise((resolve) => {
            if (this.ttsAudio) {
                this.ttsAudio.pause();
                URL.revokeObjectURL(this.ttsAudio.src);
            }
            
            const audioUrl = URL.createObjectURL(audioBlob);
            this.ttsAudio = new Audio(audioUrl);
            
            this.ttsAudio.onended = () => {
                console.log('TTS音频播放完成');
                URL.revokeObjectURL(audioUrl);
                resolve();
            };
            
            this.ttsAudio.onerror = (error) => {
                console.error('TTS音频播放错误:', error);
                URL.revokeObjectURL(audioUrl);
                resolve();
            };
            
            this.ttsAudio.play().catch(error => {
                console.error('TTS音频播放失败:', error);
                resolve();
            });
        });
    }
    
    // 预加载下一条字幕
    preloadNextSubtitle(currentTime) {
        if (this.preloadingSubtitle || !this.core.zhSubtitles || this.core.zhSubtitles.length === 0) {
            return;
        }
        
        // 查找当前时间之后的下一条字幕
        const nextSubtitles = this.core.zhSubtitles.filter(s => s.start > currentTime);
        if (nextSubtitles.length === 0) return;
        
        // 按开始时间排序，获取最近的下一条字幕
        const nextSubtitle = nextSubtitles.sort((a, b) => a.start - b.start)[0];
        
        // 如果已经预加载过该字幕，或该字幕内容为空，则跳过
        if (this.preloadedAudio[nextSubtitle.text] || !nextSubtitle.text || nextSubtitle.text.trim() === '') {
            return;
        }
        
        this.preloadingSubtitle = true;
        console.log('预加载下一条字幕:', nextSubtitle.text);
        
        // 请求TTS音频但不播放
        chrome.runtime.sendMessage({
            action: 'proxyFetch',
            url: 'http://localhost:8080/api/tts/speak',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                text: nextSubtitle.text,
                voice: 'zh-CN-XiaoxiaoNeural',
                rate: 1.2
            })
        }, response => {
            this.preloadingSubtitle = false;
            
            if (chrome.runtime.lastError || !response || !response.success) {
                console.error('预加载TTS失败:', chrome.runtime.lastError || (response ? response.error : '未知错误'));
                return;
            }
            
            // 缓存Blob对象
            this.preloadedAudio[nextSubtitle.text] = SubtitleUtils.base64ToBlob(response.data);
            console.log('字幕预加载完成:', nextSubtitle.text);
        });
    }
    
    // 停止当前朗读
    stopSpeaking() {
        if (this.ttsAudio) {
            this.ttsAudio.pause();
            URL.revokeObjectURL(this.ttsAudio.src);
            this.ttsAudio = null;
        }
        this.currentTTSText = '';
        this.isProcessingQueue = false;
    }

    // 清理TTS管理器
    cleanup() {
        console.log('开始清理字幕TTS管理器');

        // 停止TTS朗读
        this.stopSpeaking();
        this.ttsQueue = [];
        
        // 清理预加载的音频
        for (const key in this.preloadedAudio) {
            if (this.preloadedAudio.hasOwnProperty(key)) {
                delete this.preloadedAudio[key];
            }
        }
        
        // 恢复视频声音状态和播放速度
        if (this.videoElement && this.isTTSEnabled) {
            this.videoElement.muted = this.isVideoMuted;
            if (this.originalPlaybackRate !== undefined) {
                this.videoElement.playbackRate = this.originalPlaybackRate;
            }
        }

        console.log('字幕TTS管理器清理完成');
    }
}

// 注册到全局对象
window.SubtitleTTS = SubtitleTTS; 