// Subtitle Transcription Module - 字幕转录功能模块
class SubtitleTranscription {
    constructor(core) {
        this.core = core;
        
        // 转录相关属性
        this.isTranscribing = false;
        this.transcriptionProgress = 0;
        this.transcriptionStartTime = null;
        this.transcriptionLanguage = 'en';
        
        console.log('字幕转录管理器初始化完成');
    }

    // 开始转录流程
    async startTranscription() {
        if (this.isTranscribing) {
            if (this.core.ui) {
                this.core.ui.showNotification('正在转录中，请等待完成', 'warning');
            }
            return;
        }
        
        // 使用已设置的转录语言，如果没有设置则默认为英语
        if (!this.transcriptionLanguage) {
            this.transcriptionLanguage = 'en';
        }
        
        // 获取当前视频URL
        const videoUrl = window.location.href;
        if (!videoUrl.includes('youtube.com/watch')) {
            if (this.core.ui) {
                this.core.ui.showNotification('请在YouTube视频页面使用此功能', 'error');
            }
            return;
        }
        
        const progressContainer = document.getElementById('transcribe-progress');
        const progressBar = document.getElementById('transcribe-progress-bar');
        
        if (progressContainer) progressContainer.style.display = 'block';
        if (progressBar) progressBar.style.width = '0%';
        
        // 显示转录中的加载提示
        const loadingHint = document.getElementById('extension-loading-hint');
        if (loadingHint) {
            loadingHint.style.display = 'flex';
            loadingHint.className = 'subtitle-loading-hint';
            loadingHint.innerHTML = `
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">AI转录中，预计用时为视频时长的1/4。请勿关闭页面，完成后将通过声音提示您。</div>
                </div>
            `;
        }
        
        this.isTranscribing = true;
        this.transcriptionProgress = 0;
        this.transcriptionStartTime = Date.now();
        
        try {
            // 通过background.js发送转录请求
            chrome.runtime.sendMessage({
                action: 'transcribeVideo',
                videoUrl: videoUrl,
                language: this.transcriptionLanguage,
                subtitleFileId: this.core.subtitleFileId
            }, (response) => {
                if (chrome.runtime.lastError) {
                    this.handleTranscriptionError(chrome.runtime.lastError.message);
                    return;
                }
                
                if (!response || !response.success) {
                    // 检查是否为Premium权限错误
                    if (response && response.data && response.data.error === 'PREMIUM_REQUIRED') {
                        this.handlePremiumRequired('字幕转录');
                    } else if (response && response.data && response.data.error === 'AUTHENTICATION_REQUIRED') {
                        this.handleAuthRequired();
                    } else {
                        this.handleTranscriptionError(response?.error || '转录请求失败');
                    }
                    return;
                }
                
                // 设置轮询来获取进度
                this.pollTranscriptionProgress(response.taskId);
            });
            
        } catch (error) {
            this.handleTranscriptionError(error.message);
        }
    }
    
    // 轮询转录进度
    pollTranscriptionProgress(taskId) {
        if (!this.isTranscribing) return;
        
        const progressInterval = setInterval(() => {
            chrome.runtime.sendMessage({
                action: 'getTranscriptionProgress',
                taskId: taskId
            }, (response) => {
                if (!this.isTranscribing || chrome.runtime.lastError) {
                    clearInterval(progressInterval);
                    if (chrome.runtime.lastError) {
                        this.handleTranscriptionError(chrome.runtime.lastError.message);
                    }
                    return;
                }
                
                if (!response || !response.success) {
                    clearInterval(progressInterval);
                    this.handleTranscriptionError(response?.error || '获取转录进度失败');
                    return;
                }
                
                // 更新进度
                this.updateTranscriptionProgress(response);
                
                // 如果完成，清除定时器并加载字幕
                if (response.status === 'completed') {
                    clearInterval(progressInterval);
                    this.loadTranscribedSubtitles(response);
                } else if (response.status === 'failed') {
                    clearInterval(progressInterval);
                    this.handleTranscriptionError(response.error || '转录失败');
                }
            });
        }, 3000); // 每3秒轮询一次
    }
    
    // 更新转录进度条
    updateTranscriptionProgress(progressData) {
        const progressBar = document.getElementById('transcribe-progress-bar');
        
        if (!progressBar) return;
        
        // 更新进度百分比
        const progress = progressData.progress || 0;
        this.transcriptionProgress = progress;
        progressBar.style.width = `${progress}%`;
    }
    
    // 播放转录完成提示音
    playCompletionSound() {
        try {
            // 首先尝试播放mp3文件
            const audioUrl = chrome.runtime.getURL('assets/audio/dingdong.mp3');
            const audio = new Audio(audioUrl);
            
            // 设置音量
            audio.volume = 0.7;
            
            // 播放音频
            audio.play().then(() => {
                console.log('播放转录完成提示音(MP3)');
            }).catch(error => {
                console.warn('播放MP3提示音失败，使用备用方案:', error);
                // 如果MP3播放失败，回退到Web Audio API
                this.playFallbackSound();
            });
            
        } catch (error) {
            console.warn('播放完成提示音失败，使用备用方案:', error);
            // 如果出现任何错误，使用备用方案
            this.playFallbackSound();
        }
    }
    
    // 备用的声音播放方案（使用Web Audio API）
    playFallbackSound() {
        try {
            // 创建音频上下文
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 播放叮咚声音的函数
            const playTone = (frequency, startTime, duration) => {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(frequency, startTime);
                oscillator.type = 'sine';
                
                // 设置音量包络，避免爆音
                gainNode.gain.setValueAtTime(0, startTime);
                gainNode.gain.linearRampToValueAtTime(0.2, startTime + 0.01);
                gainNode.gain.linearRampToValueAtTime(0.2, startTime + duration - 0.01);
                gainNode.gain.linearRampToValueAtTime(0, startTime + duration);
                
                oscillator.start(startTime);
                oscillator.stop(startTime + duration);
            };
            
            const currentTime = audioContext.currentTime;
            
            // 叮：高音 800Hz，持续0.2秒
            playTone(800, currentTime + 0.1, 0.2);
            
            // 咚：低音 400Hz，持续0.3秒，稍微延迟
            playTone(400, currentTime + 0.4, 0.3);
            
            console.log('播放转录完成提示音(备用方案)');
            
        } catch (error) {
            console.warn('备用提示音播放失败:', error);
            // 不抛出错误，避免影响主要功能
        }
    }

    // 加载转录的字幕
    loadTranscribedSubtitles(response) {
        this.isTranscribing = false;
        
        // 隐藏进度条
        const progressContainer = document.getElementById('transcribe-progress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
        
        // 显示成功通知
        if (this.core.ui) {
            this.core.ui.showNotification('转录成功，正在加载字幕', 'success');
        }
        
        console.log('转录完成，加载字幕:', response);
        
        // 清空之前的字幕状态，防止与新字幕重叠
        if (typeof this.core.clearSubtitleState === 'function') {
            this.core.clearSubtitleState();
        } else {
            // 如果clearSubtitleState方法不存在，手动清理
            if (this.core.subtitleInterval) {
                clearInterval(this.core.subtitleInterval);
                this.core.subtitleInterval = null;
            }
            this.core.jaSubtitles = [];
            this.core.zhSubtitles = [];
            this.core.lastSubtitleText = { ja: '', zh: '' };
        }
        
        // 更新字幕路径并加载字幕
        this.core.subtitleFileId = response.subtitleFileId;
        
        // 根据原始语言设置字幕路径
        if (response.language === 'ja') {
            this.core.subtitleJaPath = response.originalSubtitlePath;
            this.core.subtitleZhPath = response.translatedSubtitlePath;
        } else {
            // 如果是英语或其他语言，将原始字幕放在jaSubtitles中显示
            this.core.subtitleJaPath = response.originalSubtitlePath;
            this.core.subtitleZhPath = response.translatedSubtitlePath;
        }
        
        console.log('字幕路径设置:', {
            subtitleFileId: this.core.subtitleFileId,
            jaPath: this.core.subtitleJaPath,
            zhPath: this.core.subtitleZhPath
        });
        
        this.core.areSubtitlePathsFetched = true;
        
        // 通过background.js加载字幕文件，避免跨域问题
        this.loadSubtitleViaBackground(this.core.subtitleJaPath, 'ja')
            .then(() => {
                console.log('原始语言字幕加载完成');
                return this.loadSubtitleViaBackground(this.core.subtitleZhPath, 'zh');
            })
            .then(() => {
                console.log('中文字幕加载完成');
                // 更新字幕显示
                this.core.updateSubtitles();
                if (this.core.subtitleInterval) clearInterval(this.core.subtitleInterval);
                this.core.subtitleInterval = setInterval(() => this.core.updateSubtitles(), 100);
                
                // 隐藏加载提示
                const loadingHint = document.getElementById('extension-loading-hint');
                if (loadingHint) {
                    loadingHint.style.display = 'none';
                }
                
                // 播放转录完成提示音
                this.playCompletionSound();
            })
            .catch(error => {
                console.error('加载转录字幕失败:', error);
                this.core.updateSubtitleLoadingHint(`加载转录字幕失败: ${error.message}`, 'red');
            });
    }
    
    // 通过background.js加载字幕文件
    async loadSubtitleViaBackground(subtitlePath, language) {
        if (!subtitlePath) {
            console.log(`${language}字幕路径为空，跳过加载`);
            return;
        }
        
        console.log(`通过background.js加载${language}字幕: ${subtitlePath}`);
        
        try {
            // 构建完整URL
            const fileUrl = `http://localhost:8080${subtitlePath}`;
            
            // 通过background.js发送请求
            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'proxyFetch',
                    url: fileUrl,
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else if (!response || !response.success) {
                        reject(new Error(response?.error || '获取字幕失败'));
                    } else {
                        resolve(response);
                    }
                });
            });
            
            // 获取VTT内容
            const content = response.data;
            
            // 检查内容是否为空或无效
            if (!content) {
                throw new Error('获取到的字幕内容为空');
            }
            
            // 确保内容是字符串类型
            let vttContent = content;
            if (typeof content !== 'string') {
                console.warn(`${language}字幕内容不是字符串类型，尝试转换`);
                try {
                    // 如果是对象，可能是JSON解析错误，尝试获取原始文本
                    if (typeof content === 'object' && content.originalText) {
                        vttContent = content.originalText;
                    } else {
                        // 尝试转换为字符串
                        vttContent = String(content);
                    }
                } catch (e) {
                    console.error('转换字幕内容为字符串失败:', e);
                    throw new Error('字幕内容格式无效');
                }
            }
            
            // 检查响应是否为HTML（错误页面）
            if (vttContent.includes('<!DOCTYPE html>')) {
                throw new Error('服务器返回了HTML而不是字幕文件');
            }
            
            // 检查是否是有效的VTT内容
            if (!vttContent.includes('WEBVTT')) {
                console.warn(`${language}字幕内容不是标准的VTT格式，尝试修复`);
                // 尝试添加WEBVTT头
                const fixedContent = 'WEBVTT\n\n' + vttContent;
                
                // 解析字幕内容
                const subtitles = SubtitleUtils.parseVTT(fixedContent);
                console.log(`${language}字幕解析结果(修复后): ${subtitles.length}条`);
                
                // 保存解析结果
                if (language === 'ja') {
                    this.core.jaSubtitles = subtitles;
                } else if (language === 'zh') {
                    this.core.zhSubtitles = subtitles;
                }
                
                return;
            }
            
            // 解析字幕内容
            const subtitles = SubtitleUtils.parseVTT(vttContent);
            console.log(`${language}字幕解析结果: ${subtitles.length}条`);
            
            // 保存解析结果
            if (language === 'ja') {
                this.core.jaSubtitles = subtitles;
            } else if (language === 'zh') {
                this.core.zhSubtitles = subtitles;
            }
            
        } catch (error) {
            console.error(`加载${language}字幕失败:`, error);
            throw error;
        }
    }
    
    // 处理转录错误
    handleTranscriptionError(errorMessage) {
        this.isTranscribing = false;
        
        // 隐藏进度条
        const progressContainer = document.getElementById('transcribe-progress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
        
        // 显示错误通知
        if (this.core.ui) {
            this.core.ui.showNotification(`转录失败: ${errorMessage}`, 'error');
        }
        console.error('转录失败:', errorMessage);
    }

    // 处理Premium权限错误
    handlePremiumRequired(featureName) {
        this.isTranscribing = false;
        
        // 隐藏进度条
        const progressContainer = document.getElementById('transcribe-progress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
        
        // 显示错误通知
        if (this.core.ui) {
            this.core.ui.showNotification(`${featureName}需要Premium权限，请升级您的订阅。`, 'error');
        }
        console.error(`${featureName}需要Premium权限，请升级您的订阅。`);
    }

    // 处理认证权限错误
    handleAuthRequired() {
        this.isTranscribing = false;
        
        // 隐藏进度条
        const progressContainer = document.getElementById('transcribe-progress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
        
        // 显示错误通知
        if (this.core.ui) {
            this.core.ui.showNotification('您的订阅已过期或无效，请重新登录。', 'error');
        }
        console.error('您的订阅已过期或无效，请重新登录。');
    }

    // 设置转录语言
    setTranscriptionLanguage(language) {
        this.transcriptionLanguage = language;
        console.log('转录语言设置为:', language);
    }

    // 清理转录管理器
    cleanup() {
        console.log('开始清理字幕转录管理器');

        this.isTranscribing = false;
        this.transcriptionProgress = 0;
        this.transcriptionStartTime = null;

        // 隐藏进度条
        const progressContainer = document.getElementById('transcribe-progress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }

        console.log('字幕转录管理器清理完成');
    }
}

// 注册到全局对象
window.SubtitleTranscription = SubtitleTranscription; 