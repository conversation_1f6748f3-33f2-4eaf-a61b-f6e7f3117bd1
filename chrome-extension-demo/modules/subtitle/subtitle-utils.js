// Subtitle Utils Module - 字幕工具函数模块
class SubtitleUtils {
    // 解析VTT时间格式 - 增强版
    static parseVttTime(timeStr) {
        try {
            if (!timeStr || typeof timeStr !== 'string') {
                return null;
            }
            
            // 标准化格式，将逗号替换为点
            timeStr = timeStr.replace(/,/g, '.');
            
            // 移除可能的额外空格
            timeStr = timeStr.trim();
            
            // 处理可能的毫秒省略
            if (!timeStr.includes('.')) {
                timeStr += '.000';
            }
            
            // 处理可能的小时省略
            const parts = timeStr.split(':');
            let seconds;
            
            if (parts.length === 3) {
                // 完整格式 HH:MM:SS.mmm
                const hours = parseInt(parts[0], 10);
                const minutes = parseInt(parts[1], 10);
                const secondsPart = parseFloat(parts[2]);
                          
                if (isNaN(hours) || isNaN(minutes) || isNaN(secondsPart)) {
                    console.warn('时间格式无效:', timeStr);
                    return null;
                }
                
                seconds = hours * 3600 + minutes * 60 + secondsPart;
            } else if (parts.length === 2) {
                // 简化格式 MM:SS.mmm
                const minutes = parseInt(parts[0], 10);
                const secondsPart = parseFloat(parts[1]);
                          
                if (isNaN(minutes) || isNaN(secondsPart)) {
                    console.warn('时间格式无效:', timeStr);
                    return null;
                }
                
                seconds = minutes * 60 + secondsPart;
            } else {
                // 尝试直接解析为秒数
                seconds = parseFloat(timeStr);
                if (isNaN(seconds)) {
                    console.warn('无法解析时间格式:', timeStr);
                    return null;
                }
            }
            
            // 验证时间范围的合理性
            if (seconds < 0 || seconds > 86400) { // 24小时的秒数
                console.warn('解析的时间值超出合理范围:', seconds, '原始字符串:', timeStr);
                // 如果时间超出范围但不是极端值，仍然返回
                if (seconds < 0 || seconds > 86400 * 7) { // 一周的秒数
                    return null;
                }
            }
            
            return seconds;
        } catch (e) {
            console.error('解析VTT时间时出错:', e, '时间字符串:', timeStr);
            return null;
        }
    }

    // 格式化时间显示
    static formatTime(seconds) {
        if (isNaN(seconds)) return '0:00';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    // Base64转Blob
    static base64ToBlob(base64Data) {
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        return new Blob([byteArray], { type: 'audio/mpeg' });
    }

    // 解析VTT文件 - 增强版
    static parseVTT(vttContent) {
        if (!vttContent || typeof vttContent !== 'string' || vttContent.trim() === '') {
            console.warn('VTT内容为空或无效');
            return [];
        }
        
        // 确保内容以WEBVTT开头
        if (!vttContent.includes('WEBVTT')) {
            console.warn('VTT内容不是标准格式，添加WEBVTT头');
            vttContent = 'WEBVTT\n\n' + vttContent;
        }
        
        // 处理可能的BOM标记
        vttContent = vttContent.replace(/^\uFEFF/, '');
        
        // 标准化行尾
        vttContent = vttContent.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
        
        const lines = vttContent.trim().split(/\n/);
        const subtitles = [];
        let i = 0;

        // 跳过头部信息
        while (i < lines.length && (lines[i].trim() === '' || lines[i].startsWith('WEBVTT') || lines[i].startsWith('Kind:') || lines[i].startsWith('Language:'))) {
            i++;
        }

        while (i < lines.length) {
            try {
                if (lines[i].trim() === '' || lines[i].startsWith('NOTE')) {
                    i++;
                    continue;
                }

                let timeLine = lines[i];
                let textStartIndex = i + 1;

                // 查找包含时间标记的行
                if (!timeLine.includes('-->')) {
                    let foundTimeLine = false;
                    for (let j = i + 1; j < Math.min(i + 5, lines.length); j++) {
                        if (lines[j].includes('-->')) {
                            timeLine = lines[j];
                            textStartIndex = j + 1;
                            foundTimeLine = true;
                            i = j; // 更新索引
                            break;
                        }
                    }
                    
                    if (!foundTimeLine) {
                        i++;
                        continue;
                    }
                }
                
                const timeParts = timeLine.split('-->');
                if (timeParts.length !== 2) {
                    i = textStartIndex;
                    continue;
                }

                let startTimeStr = timeParts[0].trim();
                let endTimeStr = timeParts[1].trim().split(' ')[0];
                
                // 确保时间格式正确
                if (!startTimeStr.includes('.')) startTimeStr += '.000';
                if (!endTimeStr.includes('.')) endTimeStr += '.000';
                
                if (startTimeStr.split(':').length === 2) startTimeStr = '00:' + startTimeStr;
                if (endTimeStr.split(':').length === 2) endTimeStr = '00:' + endTimeStr;

                const start = SubtitleUtils.parseVttTime(startTimeStr);
                const end = SubtitleUtils.parseVttTime(endTimeStr);

                if (start === null || end === null) {
                    console.warn(`时间解析失败: ${startTimeStr} --> ${endTimeStr}`);
                    i = textStartIndex;
                    while (i < lines.length && lines[i].trim() !== '') {
                        i++;
                    }
                    i++;
                    continue;
                }

                let textLines = [];
                i = textStartIndex;
                while (i < lines.length && lines[i].trim() !== '') {
                    textLines.push(lines[i].trim());
                    i++;
                }

                if (textLines.length > 0) {
                    const cleanedText = textLines.join(' ')
                                         .replace(/<[^>]*>/g, '')
                                         .trim();
                    if (cleanedText) {
                         subtitles.push({ start, end, text: cleanedText });
                    }
                }
                if (i < lines.length && lines[i].trim() === '') {
                    i++;
                }
            } catch (error) {
                console.error('解析VTT时出错:', error, '在行:', i, lines[i]);
                i++;
            }
        }
        
        // 过滤无效字幕并按开始时间排序
        const validSubtitles = subtitles.filter(sub => 
            typeof sub.start === 'number' && 
            typeof sub.end === 'number' && 
            !isNaN(sub.start) && 
            !isNaN(sub.end) &&
            sub.text && 
            sub.text.trim() !== ''
        ).sort((a, b) => a.start - b.start);
        
        console.log(`解析VTT完成: 总共${validSubtitles.length}条有效字幕`);
        return validSubtitles;
    }

    // 解析JSON3格式字幕数据
    static parseJSON3(json3Data) {
        if (!json3Data || !json3Data.events || !Array.isArray(json3Data.events)) {
            console.warn('JSON3数据格式无效');
            return [];
        }

        const subtitles = [];

        for (const event of json3Data.events) {
            try {
                // 检查必要字段
                if (typeof event.tStartMs !== 'number' || typeof event.dDurationMs !== 'number') {
                    continue;
                }

                // 提取文本内容
                let text = '';
                if (event.segs && Array.isArray(event.segs)) {
                    text = event.segs
                        .filter(seg => seg.utf8 && typeof seg.utf8 === 'string')
                        .map(seg => seg.utf8.trim())
                        .join('')
                        .trim();
                }

                // 跳过空文本
                if (!text) {
                    continue;
                }

                // 转换时间（毫秒 → 秒）
                const start = event.tStartMs / 1000;
                const end = (event.tStartMs + event.dDurationMs) / 1000;

                subtitles.push({
                    start: start,
                    end: end,
                    text: text
                });

            } catch (error) {
                console.error('解析JSON3事件时出错:', error, event);
            }
        }

        // 按开始时间排序
        return subtitles.sort((a, b) => a.start - b.start);
    }

    // 将JSON3数据转换为VTT格式
    static json3ToVTT(json3Data) {
        const subtitles = this.parseJSON3(json3Data);

        if (subtitles.length === 0) {
            return 'WEBVTT\n\n';
        }

        let vttContent = 'WEBVTT\n\n';

        for (let i = 0; i < subtitles.length; i++) {
            const subtitle = subtitles[i];

            // 格式化时间
            const startTime = this.formatVttTime(subtitle.start);
            const endTime = this.formatVttTime(subtitle.end);

            // 添加字幕条目
            vttContent += `${i + 1}\n`;
            vttContent += `${startTime} --> ${endTime}\n`;
            vttContent += `${subtitle.text}\n\n`;
        }

        return vttContent;
    }

    // 格式化时间为VTT格式 (HH:MM:SS.mmm)
    static formatVttTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        const h = hours.toString().padStart(2, '0');
        const m = minutes.toString().padStart(2, '0');
        const s = Math.floor(secs).toString().padStart(2, '0');
        const ms = Math.floor((secs - Math.floor(secs)) * 1000).toString().padStart(3, '0');

        return `${h}:${m}:${s}.${ms}`;
    }

    // 根据时间查找当前字幕索引
    static findCurrentSubtitleIndex(subtitles, currentTime) {
        if (!subtitles || subtitles.length === 0) return -1;
        
        for (let i = 0; i < subtitles.length; i++) {
            const subtitle = subtitles[i];
            if (currentTime >= subtitle.start && currentTime <= subtitle.end) {
                return i;
            }
        }
        
        // 如果当前时间不在任何字幕范围内，找最近的下一个字幕
        for (let i = 0; i < subtitles.length; i++) {
            if (subtitles[i].start > currentTime) {
                return i;
            }
        }
        
        return subtitles.length - 1; // 返回最后一个字幕
    }

    // 根据时间查找字幕
    static findSubtitleByTime(subtitles, time) {
        if (!subtitles || subtitles.length === 0) return { text: '' };
        
        const exactMatch = subtitles.find(s => s.start <= time && time <= s.end);
        if (exactMatch) return exactMatch;
        
        const upcomingSubtitles = subtitles.filter(s => s.start > time);
        if (upcomingSubtitles.length > 0) {
            const nextSubtitle = upcomingSubtitles.reduce((prev, curr) => 
                prev.start < curr.start ? prev : curr);
            
            if (nextSubtitle.start - time < 0.5) {
                return nextSubtitle;
            }
        }
        
        return { text: '' };
    }
}

// 注册到全局对象
window.SubtitleUtils = SubtitleUtils; 