// Subtitle UI Module - 字幕UI管理模块
class SubtitleUI {
    constructor(core) {
        this.core = core;
        this.subtitleContainer = null;
        this.isDarkMode = false;
        
        console.log('字幕UI管理器初始化完成');
    }

    // 创建字幕UI
    createSubtitleUI() {
        if (this.subtitleContainer) {
            return; // 已存在
        }

        this.subtitleContainer = document.createElement('div');
        this.subtitleContainer.id = 'extension-subtitle-container';
        
        this.subtitleContainer.innerHTML = `
            <div class="subtitle-header">
                <!-- 移除控制按钮区域，按钮将移动到进度条下方 -->
            </div>
            <div class="transcribe-progress" id="transcribe-progress" style="display: none;">
                <div class="progress-bar-container">
                    <div class="progress-bar" id="transcribe-progress-bar"></div>
                </div>
            </div>
            <div class="subtitle-loading-hint" id="extension-loading-hint">
                <div class="loading-content">
                    <div class="loading-text">正在获取字幕...</div>
                </div>
            </div>
            <div class="subtitle-content-wrapper">
                <div class="subtitle-display">
                    <div class="subtitle-ja" id="extension-subtitle-ja"></div>
                    <div class="subtitle-zh" id="extension-subtitle-zh"></div>
                </div>
            </div>
        `;

        const overlay = document.getElementById('study-mode-overlay');
        if (overlay) {
            // 在学习模式下，将字幕容器添加到遮罩层中
            overlay.appendChild(this.subtitleContainer);
        } else {
            // 否则，作为备选方案添加到body中
            document.body.appendChild(this.subtitleContainer);
        }
        
        // 检测是否为暗色模式
        this.isDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        if (this.isDarkMode) {
            this.subtitleContainer.classList.add('dark-mode');
        }
        
        // 添加媒体查询以监听暗色模式变化
        if (window.matchMedia) {
            const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            darkModeMediaQuery.addListener((e) => {
                this.isDarkMode = e.matches;
                if (this.isDarkMode) {
                    this.subtitleContainer.classList.add('dark-mode');
                } else {
                    this.subtitleContainer.classList.remove('dark-mode');
                }
            });
        }
        
        // 添加统一的样式
        this.addStyles();
        this.createSummaryModal();
        this.setupKeyboardListeners();

        console.log('字幕UI创建完成');
    }

    // 添加样式（简化版，包含关键样式）
    addStyles() {
        const styleElement = document.createElement('style');
        styleElement.textContent = `
            .subtitle-controls {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                padding: 10px 0;
            }
            
            .control-btn {
                background-color: #f0f7ff;
                border: none;
                border-radius: 20px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: 500;
                color: #3b82f6;
                cursor: pointer;
                transition: all 0.2s ease;
                height: 36px;
                box-sizing: border-box;
            }
            
            .control-btn:hover {
                background-color: #dbeafe;
            }
            
            .control-btn.active {
                background-color: #3b82f6;
                color: white;
            }
            
            /* 摘要模态对话框样式 */
            #extension-summary-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10005;
                display: none;
            }
            
            .summary-modal-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                backdrop-filter: blur(4px);
                display: flex;
                align-items: flex-start;
                justify-content: center;
                padding: 20px;
                padding-top: 80px;
            }
            
            .summary-modal-content {
                background: white;
                border-radius: 16px;
                max-width: 700px;
                width: 90%;
                max-height: 70vh;
                overflow: hidden;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
                display: flex;
                flex-direction: column;
            }
            
            .summary-modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px 24px;
                border-bottom: 1px solid #e5e7eb;
                background: #f9fafb;
                border-radius: 16px 16px 0 0;
            }
            
            .summary-modal-header h3 {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                color: #111827;
            }
            
            .summary-modal-close {
                background: none;
                border: none;
                font-size: 24px;
                color: #6b7280;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                transition: all 0.2s ease;
                width: 32px;
                height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .summary-modal-close:hover {
                background: #f3f4f6;
                color: #374151;
            }
            
            .summary-modal-body {
                padding: 24px;
                overflow-y: auto;
                flex: 1;
            }
            
            .summary-loading {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 40px 20px;
                text-align: center;
            }
            
            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 3px solid #f3f4f6;
                border-top: 3px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-bottom: 16px;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .loading-text {
                font-size: 16px;
                color: #6b7280;
                font-weight: 500;
            }
            
            .summary-result {
                line-height: 1.6;
            }
            
            .summary-video-title {
                font-size: 20px;
                font-weight: 600;
                color: #111827;
                margin-bottom: 20px;
                padding-bottom: 12px;
                border-bottom: 2px solid #e5e7eb;
            }
            
            .summary-content {
                font-size: 15px;
                color: #374151;
                line-height: 1.7;
                white-space: pre-wrap;
            }
            
            /* 深色模式适配 */
            body.dark-mode .summary-modal-content {
                background: #1f2937;
                color: #e5e7eb;
            }
            
            body.dark-mode .summary-modal-header {
                background: #111827;
                border-bottom-color: #374151;
            }
            
            body.dark-mode .summary-modal-header h3 {
                color: #f9fafb;
            }
            
            body.dark-mode .summary-modal-close {
                color: #9ca3af;
            }
            
            body.dark-mode .summary-modal-close:hover {
                background: #374151;
                color: #e5e7eb;
            }
            
            body.dark-mode .summary-video-title {
                color: #f9fafb;
                border-bottom-color: #374151;
            }
            
            body.dark-mode .summary-content {
                color: #d1d5db;
            }
            
            body.dark-mode .loading-text {
                color: #9ca3af;
            }
            
            body.dark-mode .loading-spinner {
                border-color: #374151;
                border-top-color: #60a5fa;
            }
        `;
        document.head.appendChild(styleElement);
    }

    // 设置键盘事件监听
    setupKeyboardListeners() {
        document.addEventListener('keydown', (event) => {
            if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
                console.log(`用户按下${event.key === 'ArrowLeft' ? '左' : '右'}方向键，跳转5秒`);
                
                const videoElement = this.core.playerContainer.querySelector('video');
                if (videoElement) {
                    if (event.key === 'ArrowLeft') {
                        videoElement.currentTime = Math.max(0, videoElement.currentTime - 5);
                    } else {
                        videoElement.currentTime = Math.min(videoElement.duration, videoElement.currentTime + 5);
                    }
                }
                event.preventDefault();
            }
        });
    }

    // 创建摘要模态对话框
    createSummaryModal() {
        if (document.getElementById('extension-summary-modal')) {
            return;
        }
        
        const summaryModal = document.createElement('div');
        summaryModal.id = 'extension-summary-modal';
        
        summaryModal.innerHTML = `
            <div class="summary-modal-overlay">
                <div class="summary-modal-content">
                    <div class="summary-modal-header">
                        <h3>视频摘要</h3>
                        <button class="summary-modal-close">&times;</button>
                    </div>
                    <div class="summary-modal-body">
                        <div class="summary-loading">
                            <div class="loading-spinner"></div>
                            <div class="loading-text">正在生成视频摘要...</div>
                        </div>
                        <div class="summary-result" style="display: none;">
                            <div class="summary-video-title"></div>
                            <div class="summary-content"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(summaryModal);
        
        const closeButton = summaryModal.querySelector('.summary-modal-close');
        closeButton.addEventListener('click', () => {
            summaryModal.style.display = 'none';
        });
    }

    // 显示通知
    showNotification(message, type = 'info') {
        let notification = document.getElementById('extension-subtitle-notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'extension-subtitle-notification';
            document.body.appendChild(notification);
        }

        const colors = {
            info: '#3b82f6',
            success: '#10b981',
            warning: '#f59e0b',
            error: '#ef4444'
        };

        notification.style.backgroundColor = colors[type] || colors.info;
        notification.style.color = 'white';
        notification.textContent = message;
        notification.style.display = 'block';

        setTimeout(() => {
            notification.style.display = 'none';
        }, 3000);
    }

    // 清理UI
    cleanup() {
        if (this.subtitleContainer) {
            this.subtitleContainer.remove();
            this.subtitleContainer = null;
        }

        ['extension-analysis-modal', 'extension-summary-modal', 'extension-subtitle-notification'].forEach(id => {
            const element = document.getElementById(id);
            if (element) element.remove();
        });
    }
}

// 注册到全局对象
window.SubtitleUI = SubtitleUI; 