(function() {
  if (window.__playerResponseHookInjected) return;
  window.__playerResponseHookInjected = true;

  console.log('[Hook] PlayerResponse hook已注入，开始监听网络请求');

  function isPlayerApi(url) {
    const playerApiPatterns = [
      '/youtubei/v1/player',
      'get_video_info',
      '/watch?',
      '/api/stats/watchtime',
      '/youtubei/v1/next',
      '/youtubei/v1/browse'
    ];
    return playerApiPatterns.some(pattern => url.includes(pattern));
  }

  // 检查页面是否已有playerResponse数据
  function checkExistingPlayerResponse() {
    console.log('[Hook] 检查页面是否已有playerResponse数据...');

    // 检查全局变量
    if (typeof ytInitialPlayerResponse !== 'undefined' && ytInitialPlayerResponse.storyboards) {
      console.log('[Hook] 发现ytInitialPlayerResponse数据');
      processPlayerResponse(JSON.stringify(ytInitialPlayerResponse), 'ytInitialPlayerResponse');
      return true;
    }

    if (typeof ytplayer !== 'undefined' && ytplayer.config && ytplayer.config.args && ytplayer.config.args.raw_player_response) {
      console.log('[Hook] 发现ytplayer数据');
      processPlayerResponse(ytplayer.config.args.raw_player_response, 'ytplayer');
      return true;
    }

    console.log('[Hook] 未发现现有数据，等待网络请求...');
    return false;
  }

  function processPlayerResponse(responseText, source) {
    try {
      if (typeof responseText === 'string' && responseText.includes('storyboards')) {
        let json = null;
        try {
          json = JSON.parse(responseText);
        } catch (e) {
          console.warn('[Hook] JSON解析失败:', e);
          return;
        }

        if (json && json.storyboards) {
          // 获取当前页面的视频ID
          const currentUrl = window.location.href;
          const currentVideoId = currentUrl.match(/[?&]v=([^&]+)/)?.[1];
          const responseVideoId = json.videoDetails ? json.videoDetails.videoId : null;

          console.log(`[Hook] 捕获到playerResponse (${source}), 视频ID: ${responseVideoId}`);

          // 验证视频ID匹配
          if (currentVideoId && responseVideoId && currentVideoId !== responseVideoId) {
            console.warn(`[Hook] 视频ID不匹配，跳过: 当前=${currentVideoId}, 响应=${responseVideoId}`);
            return;
          }

          // 检查spec是否可用
          const spec = json.storyboards.playerStoryboardSpecRenderer?.spec;
          if (!spec) {
            console.warn('[Hook] spec不可用，跳过');
            return;
          }

          console.log('[Hook] ✅ 数据有效，触发事件');

          // 更新全局变量
          window.__latestPlayerResponse = json;

          // 触发事件
          const eventData = {
            playerResponse: json,
            storyboards: json.storyboards,
            videoId: responseVideoId,
            spec: spec,
            source: source,
            timestamp: Date.now()
          };

          window.dispatchEvent(new CustomEvent('playerResponseUpdated', {
            detail: eventData
          }));
        }
      }
    } catch (e) {
      console.warn(`[Hook] 处理${source}响应时出错:`, e);
    }
  }

  // Hook XMLHttpRequest
  const origOpen = XMLHttpRequest.prototype.open;
  const origSend = XMLHttpRequest.prototype.send;
  XMLHttpRequest.prototype.open = function(method, url, ...rest) {
    if (url) {
      this.__isPlayerApi = isPlayerApi(url.toString());
    }
    return origOpen.apply(this, [method, url, ...rest]);
  };

  XMLHttpRequest.prototype.send = function(...args) {
    if (this.__isPlayerApi) {
      this.addEventListener('readystatechange', function() {
        if (this.readyState === 4 && this.status === 200) {
          processPlayerResponse(this.responseText, 'XHR');
        }
      });
    }
    return origSend.apply(this, args);
  };

  // Hook fetch
  const origFetch = window.fetch;
  window.fetch = function(input, init) {
    let url = (typeof input === 'string') ? input : (input && input.url ? input.url : '');
    const isPlayer = isPlayerApi(url);

    return origFetch.apply(this, arguments).then(async resp => {
      if (isPlayer && resp && resp.clone) {
        try {
          const cloned = resp.clone();
          const text = await cloned.text();
          processPlayerResponse(text, 'fetch');
        } catch (e) {
          console.warn('[Hook] fetch响应处理失败:', e);
        }
      }
      return resp;
    });
  };

  // 页面加载完成后检查现有数据
  if (document.readyState === 'complete') {
    setTimeout(checkExistingPlayerResponse, 1000);
  } else {
    window.addEventListener('load', () => {
      setTimeout(checkExistingPlayerResponse, 1000);
    });
  }

})();