// Study Mode Utils Module - 工具函数模块
console.log('Study Mode Utils module loaded');

/**
 * 通知系统
 */

// 显示基础通知
function showNotification(title, message) {
  // Create notification element if it doesn't exist
  let notificationElement = document.getElementById('study-mode-notification');
  if (!notificationElement) {
    notificationElement = document.createElement('div');
    notificationElement.id = 'study-mode-notification';
    document.body.appendChild(notificationElement);
  }

  notificationElement.innerHTML = `
    <div class="notification-title">${title}</div>
    <div class="notification-message">${message}</div>
  `;
  
  // Show notification
  notificationElement.style.display = 'block';
  
  // Hide notification after 3 seconds
  setTimeout(() => {
    notificationElement.style.display = 'none';
  }, 3000);
}

// 显示增强型通知
function showEnhancedNotification(title, message) {
  // 检查是否存在通知元素
  let notification = document.getElementById('study-mode-notification');
  if (!notification) {
    notification = document.createElement('div');
    notification.id = 'study-mode-notification';
    document.body.appendChild(notification);
  }
  
  // 设置通知内容
  notification.innerHTML = `
    <div class="notification-title">${title}</div>
    <div class="notification-message">${message}</div>
  `;
  
  // 显示通知
  notification.style.display = 'block';
  notification.classList.add('notification-enter');
  
  // 添加动画效果
  setTimeout(() => {
    notification.classList.remove('notification-enter');
  }, 50);
  
  // 设置自动隐藏
  setTimeout(() => {
    notification.classList.add('notification-exit');
    
    // 动画结束后隐藏
    setTimeout(() => {
      notification.style.display = 'none';
      notification.classList.remove('notification-exit');
    }, 300);
  }, 3000);
}

/**
 * 页面内容管理
 */

// 隐藏页面内容（除播放器外）
function hidePageContent(playerContainer) {
  console.log('Hiding page content, preserving player:', playerContainer);
  
  // 更精确的选择器，避免影响播放器
  const selectorsToHide = [
    '#masthead', // YouTube header
    '#secondary', // Right sidebar
    '#comments', // Comments section
    'ytd-watch-metadata', // Video metadata
    'ytd-video-secondary-info-renderer', // Video description
    'ytd-merch-shelf-renderer', // Merchandise shelf
    '#related', // Related videos (old layout)
    '.ytp-chrome-top:not(.ytp-chrome-controls)', // Player overlay (but not controls)
  ];
  
  selectorsToHide.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(el => {
      // 确保不隐藏播放器容器或其子元素
      if (el && !playerContainer.contains(el) && !el.contains(playerContainer)) {
        el.style.display = 'none';
        el.setAttribute('data-study-hidden', 'true');
      }
    });
  });
  
  // 特别处理primary区域，只隐藏非播放器的直接子元素
  const primaryInner = document.querySelector('#primary-inner');
  if (primaryInner) {
    const children = Array.from(primaryInner.children);
    children.forEach(child => {
      // 只隐藏不包含播放器的子元素
      if (child.id !== 'movie_player' && 
          !child.contains(playerContainer) && 
          !playerContainer.contains(child)) {
        child.style.display = 'none';
        child.setAttribute('data-study-hidden', 'true');
      }
    });
  }
  
  console.log('Page content hidden, player should remain visible');
}

// 显示页面内容
function showPageContent() {
  console.log('Showing page content');
  const hiddenElements = document.querySelectorAll('[data-study-hidden="true"]');
  console.log('Found hidden elements:', hiddenElements.length);
  
  hiddenElements.forEach(el => {
    el.style.display = '';
    el.removeAttribute('data-study-hidden');
  });
}

/**
 * 滚动管理
 */

// 确保页面滚动正常工作
function ensureScrollingWorks() {
  // 检查是否不在学习模式中但滚动仍被禁用
  const overlay = document.getElementById('study-mode-overlay');
  const isActuallyInStudyMode = overlay && overlay.style.display === 'block';
  
  if (!isActuallyInStudyMode) {
    // 如果不在学习模式中但滚动被禁用，恢复滚动
    if (document.body.style.overflow === 'hidden' || document.documentElement.style.overflow === 'hidden') {
      console.log('检测到滚动异常，恢复滚动状态');
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
    }
  }
}

/**
 * 时间格式化
 */

// 格式化时间
function formatTime(seconds) {
  if (isNaN(seconds)) return '0:00';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
}

/**
 * URL 工具函数
 */

// 从URL提取视频ID
function extractVideoId(url) {
  const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/;
  const match = url.match(regex);
  return match ? match[1] : null;
}

/**
 * DOM 工具函数
 */

// 强制视频播放器获得焦点
function focusVideoPlayer() {
  const videoElement = document.querySelector('video.html5-main-video');
  if (videoElement) {
    videoElement.focus();
    console.log('视频播放器已获得焦点');
  }
}

// 获取当前字幕文件ID
function getCurrentSubtitleFileId() {
  // 首先尝试从全局变量获取
  if (window.currentSubtitleFileId) {
    return window.currentSubtitleFileId;
  }
  
  // 尝试从字幕管理器实例获取
  if (window.subtitleManager && window.subtitleManager.subtitleFileId) {
    return window.subtitleManager.subtitleFileId;
  }
  
  // 如果没有，尝试从URL获取视频ID
  const videoId = extractVideoId(window.location.href);
  if (videoId) {
    return videoId; // 使用视频ID作为fallback
  }
  
  return null;
}

/**
 * 样式管理
 */

// 加载学习模式的CSS样式
function loadStudyModeStyles() {
  // 检查是否已经加载了样式
  if (!document.getElementById('study-mode-subtitle-styles')) {
    const styleLink = document.createElement('link');
    styleLink.id = 'study-mode-subtitle-styles';
    styleLink.rel = 'stylesheet';
    styleLink.href = chrome.runtime.getURL('modules/study-mode/study-mode-subtitle.css');
    document.head.appendChild(styleLink);
    console.log('学习模式字幕样式已加载');
  }
}

// 加载弹幕模块的CSS样式
function loadDanmakuStyles() {
  // 检查是否已经加载了弹幕样式
  if (!document.getElementById('study-mode-danmaku-styles')) {
    const styleLink = document.createElement('link');
    styleLink.id = 'study-mode-danmaku-styles';
    styleLink.rel = 'stylesheet';
    styleLink.href = chrome.runtime.getURL('modules/study-mode/study-mode-danmaku.css');
    document.head.appendChild(styleLink);
    console.log('弹幕模块样式已加载');
  }
}

// 应用深色模式
function applyDarkMode(isDarkMode) {
  // 应用深色模式到当前页面
  if (isDarkMode) {
    document.body.classList.add('dark-mode');
  } else {
    document.body.classList.remove('dark-mode');
  }
  
  // 检查是否已初始化分析面板
  const analysisModal = document.getElementById('extension-analysis-modal');
  if (!analysisModal) {
    // 如果分析面板还没有初始化，创建一个空的分析面板容器
    // 这样在用户点击AI解析按钮时，就会使用已经应用了深色模式的容器
    createEmptyAnalysisModal();
  }
}

// 创建空的分析面板容器
function createEmptyAnalysisModal() {
  // 检查是否已经存在
  if (document.getElementById('extension-analysis-modal')) {
    return;
  }
  
  // 创建分析模态框容器
  const analysisModal = document.createElement('div');
  analysisModal.id = 'extension-analysis-modal';
  analysisModal.style.display = 'none';
  
  // 创建模态框内容
  const modalOverlay = document.createElement('div');
  modalOverlay.className = 'modal-overlay';
  
  const modalContent = document.createElement('div');
  modalContent.className = 'modal-content';
  
  const modalHeader = document.createElement('div');
  modalHeader.className = 'modal-header';
  modalHeader.innerHTML = '<h3>字幕分析</h3><button class="modal-close">&times;</button>';
  
  const modalBody = document.createElement('div');
  modalBody.className = 'modal-body';
  
  // 组装模态框
  modalContent.appendChild(modalHeader);
  modalContent.appendChild(modalBody);
  modalOverlay.appendChild(modalContent);
  analysisModal.appendChild(modalOverlay);
  
  // 添加到文档中
  document.body.appendChild(analysisModal);
  
  // 添加关闭按钮事件
  const closeButton = modalHeader.querySelector('.modal-close');
  closeButton.addEventListener('click', function() {
    analysisModal.style.display = 'none';
  });
}

/**
 * 文件清理工具
 */

// 清理字幕文件
function cleanupSubtitleFiles(subtitleFileId) {
  if (!subtitleFileId) {
    console.log('无字幕文件ID，跳过清理');
    return;
  }
  
  console.log('请求清理字幕文件:', subtitleFileId);
  chrome.runtime.sendMessage({
    action: 'cleanupExtensionSubtitles',
    subtitleFileId: subtitleFileId
  }, (response) => {
    if (chrome.runtime.lastError) {
      console.error('清理字幕文件失败:', chrome.runtime.lastError);
    } else {
      console.log('字幕文件清理结果:', response);
    }
  });
}

/**
 * 复制功能
 */

// 复制到剪贴板功能
function copyToClipboard(text, button) {
  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard.writeText(text).then(() => {
      showCopySuccess(button);
    }).catch(err => {
      console.error('复制失败:', err);
      fallbackCopyToClipboard(text, button);
    });
  } else {
    fallbackCopyToClipboard(text, button);
  }
}

function fallbackCopyToClipboard(text, button) {
  const textArea = document.createElement('textarea');
  textArea.value = text;
  
  // 放置在屏幕外，使其不可见
  textArea.style.position = 'fixed';
  textArea.style.left = '-999999px';
  textArea.style.top = '-999999px';
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  
  try {
    const successful = document.execCommand('copy');
    if (successful) {
      showCopySuccess(button);
    } else {
      console.error('复制命令执行失败');
    }
  } catch (err) {
    console.error('复制失败:', err);
  } finally {
    document.body.removeChild(textArea);
  }
}

// 显示复制成功反馈
function showCopySuccess(button) {
  const originalContent = button.innerHTML;
  const originalClass = button.className;
  
  button.innerHTML = '✓';
  button.className = originalClass + ' copied';
  
  setTimeout(() => {
    button.innerHTML = originalContent;
    button.className = originalClass;
  }, 2000);
}

// 导出工具函数，供其他模块使用
window.StudyModeUtils = {
  // 通知系统
  showNotification,
  showEnhancedNotification,
  
  // 页面内容管理
  hidePageContent,
  showPageContent,
  
  // 滚动管理
  ensureScrollingWorks,
  
  // 时间格式化
  formatTime,
  
  // URL工具
  extractVideoId,
  
  // DOM工具
  focusVideoPlayer,
  getCurrentSubtitleFileId,
  
  // 样式管理
  loadStudyModeStyles,
  loadDanmakuStyles,
  applyDarkMode,
  createEmptyAnalysisModal,
  
  // 文件清理
  cleanupSubtitleFiles,
  
  // 复制功能
  copyToClipboard,
  fallbackCopyToClipboard,
  showCopySuccess
}; 