/* 学习模式下的字幕样式 - 参考player页面布局 */

/* 学习模式下的字幕容器位置调整 */
.study-mode-active #extension-subtitle-container,
#study-mode-overlay #extension-subtitle-container {
    bottom: auto;
    top: calc(50% + 155px);
    left: 50%;
    transform: translateX(-50%);
    width: 160%; /* 增加宽度到原来的两倍 */
    max-width: 1600px; /* 增加最大宽度到原来的两倍 */
    margin: 0;
    z-index: 10001; /* 确保在视频播放器之上，但在进度条和缩略图之下 */
    transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1), opacity 0.5s ease;

    /* 移除所有视觉装饰，提供沉浸式体验 */
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    backdrop-filter: none !important;
    padding: 20px; /* 保留内边距以确保内容不贴边 */

    /* 确保所有子元素居中显示 */
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* 全屏模式下的字幕容器位置调整 */
:fullscreen .study-mode-active #extension-subtitle-container,
:fullscreen #study-mode-overlay #extension-subtitle-container,
:-webkit-full-screen .study-mode-active #extension-subtitle-container,
:-webkit-full-screen #study-mode-overlay #extension-subtitle-container,
:-moz-full-screen .study-mode-active #extension-subtitle-container,
:-moz-full-screen #study-mode-overlay #extension-subtitle-container,
:-ms-fullscreen .study-mode-active #extension-subtitle-container,
:-ms-fullscreen #study-mode-overlay #extension-subtitle-container {
    top: calc(50% + 280px) !important; /* 全屏模式下增加更多间距，确保与播放器有足够距离 */
}



/* 进入动画 */
.study-mode-active #extension-subtitle-container.entering,
#study-mode-overlay.entering #extension-subtitle-container {
    opacity: 0;
    transform: translateY(30px) translateX(-50%);
}

/* 确保字幕容器内所有子元素居中显示 */
.study-mode-active #extension-subtitle-container > *,
#study-mode-overlay #extension-subtitle-container > * {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
}

/* 字幕内容区域居中 */
.study-mode-active .subtitle-content-wrapper,
#study-mode-overlay .subtitle-content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.study-mode-active .subtitle-display,
#study-mode-overlay .subtitle-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    text-align: center;
}

/* loading提示居中 */
.study-mode-active .subtitle-loading-hint,
#study-mode-overlay .subtitle-loading-hint {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    text-align: center !important;
}

/* 隐藏状态 - 使用更高优先级确保能够隐藏 */
.study-mode-active .subtitle-loading-hint.hidden,
#study-mode-overlay .subtitle-loading-hint.hidden {
    display: none !important;
}

.study-mode-active .loading-content,
#study-mode-overlay .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

/* 转录进度条居中 */
.study-mode-active .transcribe-progress,
#study-mode-overlay .transcribe-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

/* 字幕内容样式优化 */
.study-mode-active .subtitle-ja {
  font-size: 22px !important; /* 更大字体 */
  line-height: 1.7 !important;
  margin-bottom: 10px !important;
  font-weight: 600 !important; /* 更粗字体 */
  color: #1a202c !important; /* 更深色 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important; /* 轻微文字阴影 */
  position: relative !important;
}

/* 添加装饰性元素 */
.study-mode-active .subtitle-ja::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  height: 70%;
  width: 3px;
  background: linear-gradient(to bottom, #3b82f6, #6366f1); /* 改为蓝色到淡紫色的渐变，更柔和 */
  transform: translateY(-50%);
  border-radius: 3px;
  opacity: 0.7; /* 降低透明度 */
}

.study-mode-active .subtitle-zh {
  font-size: 18px !important; /* 更大字体 */
  line-height: 1.6 !important;
  color: #4b5563 !important;
  font-weight: 500 !important; /* 更粗字体 */
  letter-spacing: 0.3px !important; /* 增加字母间距 */
}

/* 分析按钮样式 */
.study-mode-active .analyze-btn {
  background: linear-gradient(135deg, #4f46e5, #6366f1) !important; /* 改为蓝紫色系渐变 */
  color: white !important;
  padding: 10px 20px !important; /* 更大的按钮 */
  border-radius: 12px !important; /* 更大的圆角 */
  box-shadow: 0 8px 16px rgba(79, 70, 229, 0.25) !important; /* 更协调的阴影 */
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important; /* 弹性过渡 */
  border: none !important;
  cursor: pointer !important;
  font-weight: 600 !important; /* 更粗字体 */
  font-size: 15px !important; /* 更大字体 */
  letter-spacing: 0.5px !important; /* 增加字母间距 */
  position: relative !important; /* 为光效准备 */
  overflow: hidden !important; /* 为光效准备 */
}

/* 添加光效 */
.study-mode-active .analyze-btn::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.3), rgba(255,255,255,0));
  transform: rotate(30deg);
  animation: buttonShimmer 3s infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.study-mode-active .analyze-btn:hover::after {
  opacity: 1;
}

@keyframes buttonShimmer {
  0% { transform: translateX(-100%) rotate(30deg); }
  100% { transform: translateX(100%) rotate(30deg); }
}

.study-mode-active .analyze-btn:hover {
  background: linear-gradient(135deg, #4338ca, #4f46e5) !important; /* 悬停时的渐变色 */
  box-shadow: 0 10px 20px rgba(79, 70, 229, 0.35) !important; /* 悬停时更强的阴影 */
  transform: translateY(-3px) !important; /* 更明显的上浮效果 */
}

.study-mode-active .analyze-btn:active {
  transform: translateY(0) scale(0.98) !important; /* 点击时缩小效果 */
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.2) !important; /* 点击时减弱阴影 */
}

/* 添加图标动画 */
.study-mode-active .analyze-btn i {
  margin-right: 8px !important;
  transition: transform 0.3s ease !important;
}

.study-mode-active .analyze-btn:hover i {
  transform: rotate(20deg) !important; /* 图标旋转效果 */
}

/* 响应式调整 */
@media (max-width: 768px) {
  .study-mode-active #extension-subtitle-container {
    width: 140%; /* 中等屏幕稍微减少宽度，但仍比原来大 */
    top: calc(50% + 155px); /* 向下调整10px，增加与弹幕功能区域的间距 */
  }

  .study-mode-active .subtitle-ja {
    font-size: 20px !important; /* 中等屏幕字体稍小 */
  }

  .study-mode-active .subtitle-zh {
    font-size: 16px !important; /* 中等屏幕字体稍小 */
  }
}

@media (max-width: 480px) {
  .study-mode-active #extension-subtitle-container {
    width: 120%; /* 小屏幕进一步减少宽度，但仍比原来大 */
    top: calc(50% + 125px); /* 向下调整10px，增加与弹幕功能区域的间距 */
  }

  .study-mode-active .subtitle-ja {
    font-size: 18px !important; /* 小屏幕字体更小 */
  }

  .study-mode-active .subtitle-zh {
    font-size: 15px !important; /* 小屏幕字体更小 */
  }

  .study-mode-active .analyze-btn {
    padding: 8px 16px !important; /* 小屏幕按钮更小 */
    font-size: 14px !important; /* 小屏幕按钮字体更小 */
  }
}

/* 进度条和控制按钮样式 */
.study-mode-progress-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: transparent;
  backdrop-filter: blur(8px);
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  z-index: 10003;
  padding: 16px 24px;
  transition: all 0.3s ease;
}

/* 进度条区域 */
.video-progress-section {
  margin-bottom: 16px;
}

.progress-bar-container {
  position: relative;
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
}

.progress-bar-track {
  position: relative;
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  cursor: pointer;
  transition: height 0.2s ease;
}

.progress-bar-track:hover {
  height: 6px;
}

.progress-bar-loaded {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 2px;
  width: 0%;
  transition: width 0.1s ease;
}

.progress-bar-played {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 2px;
  width: 0%;
  transition: width 0.1s ease;
}

.progress-bar-thumb {
  position: absolute;
  top: 50%;
  left: 0%;
  width: 12px;
  height: 12px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.progress-bar-track:hover .progress-bar-thumb {
  opacity: 1;
}

.progress-preview {
  position: absolute;
  bottom: 25px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  pointer-events: none;
  transform: translateX(-50%);
  white-space: nowrap;
  z-index: 9999;
  display: none;
}

.preview-thumbnail {
  display: none;
}

.preview-chapter {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
  font-weight: 400;
  text-align: center;
  width: 100%; /* 使用全宽度确保居中对齐 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: none; /* 默认隐藏，有章节信息时显示 */
}

.preview-time {
  font-weight: 500;
  text-align: center; /* 确保时间戳也居中对齐 */
  width: 100%; /* 使用全宽度确保居中对齐 */
}

/* 视频控制区域 - 恢复原来的布局 */
.video-controls-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  position: relative;
}

/* 字幕控制按钮区域样式 - 恢复绝对定位 */
.subtitle-controls-section {
  position: absolute;
  right: 0; /* 贴右边 */
  display: flex;
  align-items: center;
  gap: 12px;
}

.subtitle-control-button {
  background: rgba(255, 255, 255, 0.35) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px !important;
  padding: 0 !important; /* 移除padding，使用固定尺寸 */
  margin: 0 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  color: rgba(255, 255, 255, 0.98) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 36px !important;
  width: 36px !important; /* 固定宽度 */
  height: 36px !important;
  position: relative !important;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.subtitle-control-button svg {
  width: 18px !important;
  height: 18px !important;
  display: block !important;
  margin: 0 !important; /* 确保SVG没有外边距 */
}

.subtitle-control-button:hover {
  background: rgba(255, 255, 255, 0.45) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
  transform: translateY(-1px) !important;
}

.subtitle-control-button:active {
  background: rgba(255, 255, 255, 0.5) !important;
  transform: translateY(0) !important;
}

.subtitle-control-button.active {
  background: rgba(74, 144, 226, 0.8) !important;
  border-color: rgba(74, 144, 226, 0.9) !important;
  color: white !important;
}

.subtitle-control-button.selected {
  background: rgba(34, 197, 94, 0.8) !important;
  border-color: rgba(34, 197, 94, 0.9) !important;
  color: white !important;
}

/* 播放控制按钮样式 */
.control-button {
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.25);
  color: rgba(255, 255, 255, 0.95);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  backdrop-filter: blur(8px);
}

.control-button:not(.play-pause-button) svg {
  width: 20px !important;
  height: 20px !important;
  display: block !important;
  margin: 0 !important;
}

/* 播放暂停按钮的图标切换控制 */
.play-pause-button .play-icon,
.play-pause-button .pause-icon {
  width: 20px !important;
  height: 20px !important;
  margin: 0 !important;
  /* 不设置display，让JavaScript控制 */
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.35);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.control-button:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.3);
}

.play-pause-button {
  width: 56px;
  height: 56px;
  background: rgba(255, 255, 255, 0.3);
}

.play-pause-button svg {
  width: 20px !important;
  height: 20px !important;
}

.play-pause-button:hover {
  background: rgba(255, 255, 255, 0.4);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
}

/* 暗色模式适配 */
.dark-mode .study-mode-progress-container {
  background: transparent;
  border-top: 1px solid rgba(255, 255, 255, 0.02);
}

.dark-mode .progress-bar-track {
  background: rgba(255, 255, 255, 0.1);
}

.dark-mode .progress-bar-loaded {
  background: rgba(255, 255, 255, 0.2);
}

.dark-mode .progress-bar-played {
  background: rgba(255, 255, 255, 0.7);
}

.dark-mode .progress-bar-thumb {
  background: rgba(255, 255, 255, 0.8);
}

.dark-mode .control-button {
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
}

.dark-mode .control-button:hover {
  background: rgba(255, 255, 255, 0.25);
}

.dark-mode .play-pause-button {
  background: rgba(255, 255, 255, 0.2);
}

.dark-mode .play-pause-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.dark-mode .preview-chapter {
  color: rgba(255, 255, 255, 0.7);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .study-mode-progress-container {
    padding: 12px 16px;
  }
  
  .control-button {
    width: 44px;
    height: 44px;
  }
  
  .play-pause-button {
    width: 52px;
    height: 52px;
  }
  
  .video-controls-section {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .study-mode-progress-container {
    padding: 10px 12px;
  }
  
  .control-button {
    width: 40px;
    height: 40px;
  }
  
  .play-pause-button {
    width: 48px;
    height: 48px;
  }
  
  .video-controls-section {
    gap: 10px;
  }
  
  .progress-preview {
    font-size: 11px;
    padding: 6px 10px;
  }

  .preview-chapter {
    font-size: 10px;
    width: 100%; /* 小屏幕上也使用全宽度 */
  }
}

/* 语言选择器容器 */
.language-selector-container {
  position: relative;
}

.language-selector-btn.selected {
  background: rgba(59, 130, 246, 0.3);
  color: #3b82f6;
  box-shadow: 0 0 12px rgba(59, 130, 246, 0.4);
}

/* 语言下拉菜单 */
.language-dropdown {
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(30, 30, 30, 0.95);
  backdrop-filter: blur(12px);
  border-radius: 8px;
  padding: 8px 0;
  min-width: 100px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 10004;
}

.language-option {
  padding: 10px 16px;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.language-option:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
}



/* 暗色模式适配 - 跟随登录面板的黑暗模式切换 */
body.dark-mode .subtitle-controls-section {
  border-top-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .language-dropdown {
  background: rgba(20, 20, 20, 0.95);
  border-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .language-option {
  color: rgba(255, 255, 255, 0.8);
}

body.dark-mode .language-option:hover {
  background: rgba(59, 130, 246, 0.15);
  color: #60a5fa;
}

/* 响应式设计 - 智能避免重叠 */
@media (max-width: 1024px) {
  .subtitle-controls-section {
    right: 20px; /* 在中等屏幕上稍微向左移动 */
    gap: 10px;
  }
  
  .subtitle-control-button {
    width: 34px;
    height: 34px;
  }
  
  .subtitle-control-button svg {
    width: 16px !important;
    height: 16px !important;
  }
}

@media (max-width: 768px) {
  .video-controls-section {
    gap: 12px;
  }
  
  .subtitle-controls-section {
    right: 40px; /* 进一步向左移动避免重叠 */
    gap: 8px;
  }
  
  .subtitle-control-button {
    width: 32px;
    height: 32px;
  }
  
  .subtitle-control-button svg {
    width: 14px !important;
    height: 14px !important;
  }
  
  .control-button {
    width: 44px;
    height: 44px;
  }
  
  .control-button:not(.play-pause-button) svg {
    width: 18px !important;
    height: 18px !important;
  }
  
  .play-pause-button {
    width: 52px;
    height: 52px;
  }
  
  .play-pause-button .play-icon,
  .play-pause-button .pause-icon {
    width: 18px !important;
    height: 18px !important;
    margin: 0 !important;
  }
}

@media (max-width: 480px) {
  .video-controls-section {
    gap: 8px;
    flex-direction: column; /* 在小屏幕上垂直排列 */
    align-items: center;
  }
  
  .subtitle-controls-section {
    position: static; /* 取消绝对定位 */
    gap: 6px;
    margin-top: 8px; /* 添加上边距 */
    justify-content: center; /* 居中对齐 */
  }
  
  .subtitle-control-button {
    width: 32px;
    height: 32px;
  }
  
  .subtitle-control-button svg {
    width: 14px !important;
    height: 14px !important;
  }
  
  .control-button {
    width: 40px;
    height: 40px;
  }
  
  .control-button:not(.play-pause-button) svg {
    width: 16px !important;
    height: 16px !important;
  }
  
  .play-pause-button {
    width: 48px;
    height: 48px;
  }
  
  .play-pause-button .play-icon,
  .play-pause-button .pause-icon {
    width: 16px !important;
    height: 16px !important;
    margin: 0 !important;
  }
  
  .language-dropdown {
    min-width: 80px;
  }
  
  .language-option {
    padding: 8px 12px;
    font-size: 13px;
  }
}

/* 暗色模式下的按钮样式 - 柔和协调的浅灰色调 */
body.dark-mode .subtitle-control-button {
  background: rgba(255, 255, 255, 0.08) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

body.dark-mode .subtitle-control-button:hover {
  background: rgba(255, 255, 255, 0.12) !important;
  border-color: rgba(255, 255, 255, 0.15) !important;
}

body.dark-mode .subtitle-control-button:active {
  background: rgba(255, 255, 255, 0.16) !important;
}

body.dark-mode .control-button {
  background: rgba(255, 255, 255, 0.08) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

body.dark-mode .control-button:hover {
  background: rgba(255, 255, 255, 0.12) !important;
  border-color: rgba(255, 255, 255, 0.15) !important;
}

body.dark-mode .control-button:active {
  background: rgba(255, 255, 255, 0.16) !important;
}

body.dark-mode .play-pause-button {
  background: rgba(255, 255, 255, 0.1) !important;
}

body.dark-mode .play-pause-button:hover {
  background: rgba(255, 255, 255, 0.14) !important;
}

/* 自定义tooltip样式 */
.custom-tooltip {
  position: fixed;
  background: rgba(30, 30, 30, 0.95);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  z-index: 10005;
  pointer-events: none;
  opacity: 0;
  transform: translateY(5px);
  transition: opacity 0.15s ease, transform 0.15s ease;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.custom-tooltip.show {
  opacity: 1;
  transform: translateY(0);
}

.custom-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: rgba(30, 30, 30, 0.95);
}

body.dark-mode .custom-tooltip {
  background: rgba(20, 20, 20, 0.95);
  border-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .custom-tooltip::after {
  border-top-color: rgba(20, 20, 20, 0.95);
}
