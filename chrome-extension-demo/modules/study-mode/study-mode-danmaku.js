// Study Mode Danmaku Module - 弹幕功能模块
console.log('Study Mode Danmaku module loaded');

/**
 * 弹幕管理器
 */
window.StudyModeDanmaku = {
    // 弹幕配置
    config: {
        maxDanmakuOnScreen: 50,      // 屏幕上最多显示的弹幕数量
        danmakuSpeed: 8,             // 弹幕移动速度（秒）
        danmakuHeight: 25,           // 弹幕高度（像素）
        danmakuMargin: 2,            // 弹幕间距（像素）
        colors: [                    // 可选颜色
            '#FFFFFF', '#FF0000', '#00FF00', '#0000FF', 
            '#FFFF00', '#FF00FF', '#00FFFF', '#FFA500'
        ],
        maxLength: 100,              // 弹幕最大长度
        sendCooldown: 2000          // 发送冷却时间（毫秒）
    },
    
    // 状态变量
    state: {
        enabled: true,               // 弹幕开关状态
        inputEnabled: true,          // 输入框启用状态
        danmakuContainer: null,      // 弹幕容器
        controlPanel: null,          // 控制面板
        activeDanmaku: [],          // 活跃弹幕列表
        lastSendTime: 0,            // 上次发送时间
        currentVideoId: null,        // 当前视频ID
        playerContainer: null,       // 播放器容器
        danmakuCache: new Map(),     // 弹幕缓存
        tracks: [],                   // 弹幕轨道
        recentlyDisplayedIds: new Set(), // 最近显示过的弹幕ID
        displayCooldown: 500,        // 同一弹幕的显示冷却时间（毫秒）
        lastCheckTime: 0             // 上次检查弹幕的时间（用于防抖）
    },
    
    /**
     * 初始化弹幕系统
     */
    init(playerContainer) {
        console.log('初始化弹幕系统');
        
        this.state.playerContainer = playerContainer;
        this.state.currentVideoId = this.extractVideoId(window.location.href);
        
        // 创建弹幕容器
        this.createDanmakuContainer();
        
        // 创建控制面板
        this.createControlPanel();
        
        // 初始化弹幕轨道
        this.initTracks();
        
        // 加载已有弹幕
        this.loadDanmaku();
        
        // 监听视频播放事件
        this.setupVideoListeners();
        
        console.log('弹幕系统初始化完成');
    },
    
    /**
     * 创建弹幕容器
     */
    createDanmakuContainer() {
        // 移除已存在的容器
        const existingContainer = document.getElementById('study-danmaku-container');
        if (existingContainer) {
            existingContainer.remove();
        }
        
        // 创建新容器
        const container = document.createElement('div');
        container.id = 'study-danmaku-container';
        container.className = 'study-danmaku-container';
        
        // 将容器添加到播放器容器中
        this.state.playerContainer.appendChild(container);
        this.state.danmakuContainer = container;
        
        // 获取容器尺寸并记录
        const rect = container.getBoundingClientRect();
        console.log('弹幕容器已创建，尺寸:', rect.width + 'x' + rect.height);
        
        // 确保容器有正确的定位
        container.style.position = 'absolute';
        container.style.top = '0';
        container.style.left = '0';
        container.style.width = '100%';
        container.style.height = '100%';
        container.style.pointerEvents = 'none';
        container.style.overflow = 'hidden';
        container.style.zIndex = '999';
    },
    
    /**
     * 创建弹幕控制面板
     */
    createControlPanel() {
        // 移除已存在的面板
        const existingPanel = document.getElementById('study-danmaku-panel');
        if (existingPanel) {
            existingPanel.remove();
        }
        
        const panel = document.createElement('div');
        panel.id = 'study-danmaku-panel';
        panel.className = 'study-danmaku-panel';
        
        panel.innerHTML = `
            <div class="danmaku-controls">
                <div class="study-danmaku-switch">
                    <input class="study-danmaku-switch-input" id="danmaku-toggle" type="checkbox" checked>
                    <label class="study-danmaku-switch-label" for="danmaku-toggle" title="开启/关闭弹幕">
                        <span class="study-danmaku-switch-on">
                            <svg xmlns="http://www.w3.org/2000/svg" data-pointer="none" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="M11.989 4.828c-.47 0-.975.004-1.515.012l-1.71-2.566a1.008 1.008 0 0 0-1.678 1.118l.999 1.5c-.681.018-1.403.04-2.164.068a4.013 4.013 0 0 0-3.83 3.44c-.165 1.15-.245 2.545-.245 4.185 0 1.965.115 3.67.35 5.116a4.012 4.012 0 0 0 3.763 3.363l.906.046c1.205.063 1.808.095 3.607.095a.988.988 0 0 0 0-1.975c-1.758 0-2.339-.03-3.501-.092l-.915-.047a2.037 2.037 0 0 1-1.91-1.708c-.216-1.324-.325-2.924-.325-4.798 0-1.563.076-2.864.225-3.904.14-.977.96-1.713 1.945-1.747 2.444-.087 4.465-.13 6.063-.131 1.598 0 3.62.044 6.064.13.96.034 1.71.81 1.855 1.814.075.524.113 1.962.141 3.065v.002c.01.342.017.65.025.88a.987.987 0 1 0 1.974-.068c-.008-.226-.016-.523-.025-.856v-.027c-.03-1.118-.073-2.663-.16-3.276-.273-1.906-1.783-3.438-3.74-3.507-.9-.032-1.743-.058-2.531-.078l1.05-1.46a1.008 1.008 0 0 0-1.638-1.177l-1.862 2.59c-.38-.004-.744-.007-1.088-.007h-.13Zm.521 4.775h-1.32v4.631h2.222v.847h-2.618v1.078h2.618l.003.678c.36.026.714.163 1.01.407h.11v-1.085h2.694v-1.078h-2.695v-.847H16.8v-4.63h-1.276a8.59 8.59 0 0 0 .748-1.42L15.183 7.8a14.232 14.232 0 0 1-.814 1.804h-1.518l.693-.308a8.862 8.862 0 0 0-.814-1.408l-1.045.352c.297.396.572.847.825 1.364Zm-4.18 3.564.154-1.485h1.98V8.294h-3.2v.98H9.33v1.43H7.472l-.308 3.453h2.277c0 1.166-.044 1.925-.12 2.277-.078.352-.386.528-.936.528-.308 0-.616-.022-.902-.055l.297 1.067.062.005c.285.02.551.04.818.04 1.001-.067 1.562-.419 1.694-1.057.11-.638.176-1.903.176-3.795h-2.2Zm7.458.11v-.858h-1.254v.858h1.254Zm-2.376-.858v.858h-1.199v-.858h1.2Zm-1.199-.946h1.2v-.902h-1.2v.902Zm2.321 0v-.902h1.254v.902h-1.254Z" clip-rule="evenodd"></path>
                                <path fill="#00AEEC" fill-rule="evenodd" d="M22.846 14.627a1 1 0 0 0-1.412.075l-5.091 5.703-2.216-2.275-.097-.086-.008-.005a1 1 0 0 0-1.322 1.493l2.963 *************.007.005c.407.315 1 .27 1.354-.124l5.81-6.505.08-.102.005-.008a1 1 0 0 0-.166-1.295Z" clip-rule="evenodd"></path>
                            </svg>
                        </span>
                        <span class="study-danmaku-switch-off">
                            <svg xmlns="http://www.w3.org/2000/svg" data-pointer="none" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="m8.085 4.891-.999-1.499a1.008 1.008 0 0 1 1.679-1.118l1.709 2.566c.54-.008 1.045-.012 1.515-.012h.13c.345 0 .707.003 1.088.007l1.862-2.59a1.008 1.008 0 0 1 1.637 1.177l-1.049 1.46c.788.02 1.631.046 2.53.078 1.958.069 3.468 1.6 3.74 3.507.088.613.13 2.158.16 3.276l.001.027c.01.333.017.63.025.856a.987.987 0 0 1-1.974.069c-.008-.23-.016-.539-.025-.881v-.002c-.028-1.103-.066-2.541-.142-3.065-.143-1.004-.895-1.78-1.854-1.813-2.444-.087-4.466-.13-6.064-.131-1.598 0-3.619.044-6.063.13a2.037 2.037 0 0 0-1.945 1.748c-.15 1.04-.225 2.341-.225 3.904 0 1.874.11 3.474.325 4.798.154.949.95 1.66 1.91 1.708a97.58 97.58 0 0 0 5.416.139.988.988 0 0 1 0 1.975c-2.196 0-3.61-.047-5.513-.141A4.012 4.012 0 0 1 2.197 17.7c-.236-1.446-.351-3.151-.351-5.116 0-1.64.08-3.035.245-4.184A4.013 4.013 0 0 1 5.92 4.96c.761-.027 1.483-.05 2.164-.069Zm4.436 4.707h-1.32v4.63h2.222v.848h-2.618v1.078h2.431a5.01 5.01 0 0 1 3.575-3.115V9.598h-1.276a8.59 8.59 0 0 0 .748-1.42l-1.089-.384a14.232 14.232 0 0 1-.814 1.804h-1.518l.693-.308a8.862 8.862 0 0 0-.814-1.408l-1.045.352c.297.396.572.847.825 1.364Zm-4.18 3.564.154-1.485h1.98V8.289h-3.2v.979h2.067v1.43H7.483l-.308 3.454h2.277c0 1.166-.044 1.925-.12 2.277-.078.352-.386.528-.936.528-.308 0-.616-.022-.902-.055l.297 1.067.062.004c.285.02.551.04.818.04 1.001-.066 1.562-.418 1.694-1.056.11-.638.176-1.903.176-3.795h-2.2Zm7.458.11v-.858h-1.254v.858H15.8Zm-2.376-.858v.858h-1.199v-.858h1.2Zm-1.199-.946h1.2v-.902h-1.2v.902Zm2.321 0v-.902H15.8v.902h-1.254Zm3.517 10.594a4 4 0 1 0 0-8 4 4 0 0 0 0 8Zm-.002-1.502a2.5 2.5 0 0 1-2.217-3.657l3.326 3.398a2.49 2.49 0 0 1-1.109.259Zm2.5-2.5c0 .42-.103.815-.286 1.162l-3.328-3.401a2.5 2.5 0 0 1 3.614 2.239Z" clip-rule="evenodd"></path>
                            </svg>
                        </span>
                    </label>
                </div>
                <div class="danmaku-input-group">
                    <input type="text" id="danmaku-input" class="danmaku-input" 
                           placeholder="发送弹幕..." maxlength="${this.config.maxLength}">
                    <button id="danmaku-send" class="danmaku-send-btn" title="发送弹幕">
                        发送
                    </button>
                </div>
            </div>
        `;
        
        // 将面板添加到播放器容器内部
        const studyPlayerContainer = document.getElementById('study-mode-player-container');
        if (studyPlayerContainer) {
            studyPlayerContainer.appendChild(panel);
        }
        
        this.state.controlPanel = panel;
        
        // 绑定事件
        this.bindControlEvents();
        
        console.log('弹幕控制面板已创建');
    },
    
    /**
     * 绑定控制面板事件
     */
    bindControlEvents() {
        const toggleCheckbox = document.getElementById('danmaku-toggle');
        const input = document.getElementById('danmaku-input');
        const sendBtn = document.getElementById('danmaku-send');
        
        // 弹幕开关
        toggleCheckbox.addEventListener('change', () => {
            this.toggleDanmaku();
        });
        
        // 发送弹幕
        sendBtn.addEventListener('click', () => {
            this.sendDanmaku();
        });
        
        // 回车发送
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendDanmaku();
            }
        });
        
        // 输入长度限制提示
        input.addEventListener('input', (e) => {
            const remaining = this.config.maxLength - e.target.value.length;
            if (remaining < 20) {
                input.style.color = remaining < 10 ? '#ff4444' : '#ffaa44';
            } else {
                input.style.color = '';
            }
        });
    },
    
    /**
     * 初始化弹幕轨道
     */
    initTracks() {
        if (!this.state.danmakuContainer) return;
        
        const containerHeight = this.state.danmakuContainer.offsetHeight;
        const trackHeight = this.config.danmakuHeight + this.config.danmakuMargin;
        const trackCount = Math.floor(containerHeight / trackHeight);
        
        this.state.tracks = [];
        for (let i = 0; i < trackCount; i++) {
            this.state.tracks.push({
                index: i,
                y: i * trackHeight,
                occupied: false,
                lastEndTime: 0
            });
        }
        
        console.log(`初始化了 ${trackCount} 条弹幕轨道`);
    },
    
    /**
     * 切换弹幕开关
     */
    toggleDanmaku() {
        const toggleCheckbox = document.getElementById('danmaku-toggle');
        const input = document.getElementById('danmaku-input');
        const sendBtn = document.getElementById('danmaku-send');
        
        this.state.enabled = toggleCheckbox.checked;
        
        if (this.state.enabled) {
            // 开启弹幕
            input.disabled = false;
            sendBtn.disabled = false;
            this.state.danmakuContainer.style.display = 'block';
            
            // 重新加载弹幕
            this.loadDanmaku();
        } else {
            // 关闭弹幕
            input.disabled = true;
            sendBtn.disabled = true;
            this.state.danmakuContainer.style.display = 'none';
            
            // 清除所有弹幕
            this.clearAllDanmaku();
        }
        
        console.log('弹幕状态:', this.state.enabled ? '开启' : '关闭');
    },
    
    /**
     * 发送弹幕
     */
    async sendDanmaku() {
        const input = document.getElementById('danmaku-input');
        const sendBtn = document.getElementById('danmaku-send');
        
        const content = input.value.trim();
        if (!content) {
            this.showMessage('请输入弹幕内容', 'warning');
            return;
        }
        
        // 冷却时间检查
        const now = Date.now();
        if (now - this.state.lastSendTime < this.config.sendCooldown) {
            const remaining = Math.ceil((this.config.sendCooldown - (now - this.state.lastSendTime)) / 1000);
            this.showMessage(`请等待 ${remaining} 秒后再发送`, 'warning');
            return;
        }
        
        // 获取当前视频时间
        const videoElement = document.querySelector('video.html5-main-video');
        if (!videoElement) {
            this.showMessage('找不到视频播放器', 'error');
            return;
        }
        
        const videoTimestamp = videoElement.currentTime;
        const color = '#FFFFFF'; // 默认使用白色
        
        // 禁用发送按钮
        sendBtn.disabled = true;
        sendBtn.textContent = '发送中...';
        
        try {
            // 发送到后端
            const response = await chrome.runtime.sendMessage({
                action: 'sendDanmaku',
                videoId: this.state.currentVideoId,
                content: content,
                videoTimestamp: videoTimestamp,
                color: color,
                type: 1 // 滚动弹幕
            });
            
            if (response.success) {
                // 清空输入框
                input.value = '';
                input.style.color = '';
                
                // 创建弹幕对象
                const newDanmaku = {
                    id: response.data.danmaku.id,
                    content: content,
                    videoTimestamp: videoTimestamp,
                    color: color,
                    type: 1
                };
                
                // 添加到缓存中
                this.state.danmakuCache.set(newDanmaku.id, newDanmaku);
                
                // 通过网关请求显示弹幕
                this.requestDisplayDanmaku(newDanmaku);
                
                this.showMessage('弹幕发送成功', 'success');
                this.state.lastSendTime = now;
            } else {
                this.showMessage(response.error || '弹幕发送失败', 'error');
            }
        } catch (error) {
            console.error('发送弹幕失败:', error);
            this.showMessage('弹幕发送失败: ' + error.message, 'error');
        } finally {
            // 恢复发送按钮
            sendBtn.disabled = false;
            sendBtn.textContent = '发送';
        }
    },
    
    /**
     * 加载弹幕数据
     */
    async loadDanmaku() {
        if (!this.state.enabled || !this.state.currentVideoId) return;
        
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'getDanmakuList',
                videoId: this.state.currentVideoId,
                page: 0,
                size: 500
            });
            
            if (response.success && response.data.danmaku) {
                // 清空现有弹幕
                this.clearAllDanmaku();
                
                // 缓存弹幕数据
                this.state.danmakuCache.clear();
                response.data.danmaku.forEach(danmaku => {
                    this.state.danmakuCache.set(danmaku.id, danmaku);
                });
                
                console.log(`加载了 ${response.data.danmaku.length} 条弹幕`, response.data.danmaku);
                
                // 立即检查当前时间是否有弹幕需要显示
                const videoElement = document.querySelector('video.html5-main-video');
                if (videoElement) {
                    this.checkAndDisplayTimedDanmaku(videoElement.currentTime);
                }
            }
        } catch (error) {
            console.error('加载弹幕失败:', error);
        }
    },
    
    /**
     * 显示弹幕
     */
    displayDanmaku(danmaku) {
        if (!this.state.enabled || !this.state.danmakuContainer) {
            console.log('弹幕系统未启用或容器不存在');
            return;
        }
        
        // 检查屏幕上弹幕数量
        if (this.state.activeDanmaku.length >= this.config.maxDanmakuOnScreen) {
            console.log('屏幕弹幕数量已达上限');
            return;
        }
        
        // 查找可用轨道
        const track = this.findAvailableTrack();
        if (!track) {
            console.log('没有可用的弹幕轨道');
            return;
        }
        
        // 创建弹幕元素
        const danmakuElement = document.createElement('div');
        danmakuElement.className = 'study-danmaku-item';
        danmakuElement.textContent = danmaku.content;
        
        // 设置初始样式
        danmakuElement.style.cssText = `
            position: absolute;
            top: ${track.y}px;
            left: 0;
            white-space: nowrap;
            color: ${danmaku.color || '#FFFFFF'};
            font-size: 16px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
            pointer-events: none;
            user-select: none;
            z-index: 1000;
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            will-change: transform;
        `;
        
        this.state.danmakuContainer.appendChild(danmakuElement);

        // 使用CSS Keyframes触发动画
        const duration = this.config.danmakuSpeed;
        danmakuElement.style.animation = `danmaku-scroll ${duration}s linear forwards`;
        console.log(`开始弹幕动画: "${danmaku.content}"，时长: ${duration}s`);
        
        // 标记轨道占用
        track.occupied = true;
        // 更精确地估算轨道释放时间
        track.lastEndTime = Date.now() + (duration * 1000 * 0.4); // 40%的时间后释放轨道
        
        // 添加到活跃列表
        const danmakuData = {
            element: danmakuElement,
            track: track,
            id: danmaku.id,
            timestamp: danmaku.videoTimestamp,
            startTime: Date.now(),
            duration: duration * 1000 + 1000 // 总生命周期
        };
        
        // 设置清理定时器
        danmakuData.removalTimeoutId = setTimeout(() => {
            this.removeDanmaku(danmakuData);
        }, danmakuData.duration);
        
        this.state.activeDanmaku.push(danmakuData);
        
        // 当弹幕部分进入屏幕后，释放轨道给下一条弹幕
        setTimeout(() => {
            track.occupied = false;
            console.log(`轨道 ${track.index} 已释放`);
        }, duration * 1000 * 0.25); // 25%的时间后释放轨道，让弹幕更密集
    },
    
    /**
     * 查找可用轨道
     */
    findAvailableTrack() {
        const now = Date.now();
        
        // 查找完全空闲的轨道
        for (let track of this.state.tracks) {
            if (!track.occupied && now > track.lastEndTime) {
                return track;
            }
        }
        
        // 如果没有完全空闲的，查找最早可用的
        let earliestTrack = null;
        let earliestTime = Infinity;
        
        for (let track of this.state.tracks) {
            if (track.lastEndTime < earliestTime) {
                earliestTime = track.lastEndTime;
                earliestTrack = track;
            }
        }
        
        return earliestTrack;
    },
    
    /**
     * 移除弹幕
     */
    removeDanmaku(danmakuData) {
        if (danmakuData.element && danmakuData.element.parentNode) {
            danmakuData.element.remove();
        }
        
        // 从活跃列表中移除
        const index = this.state.activeDanmaku.indexOf(danmakuData);
        if (index > -1) {
            this.state.activeDanmaku.splice(index, 1);
        }
    },
    
    /**
     * 清除所有弹幕
     */
    clearAllDanmaku() {
        // 移除所有弹幕元素
        this.state.activeDanmaku.forEach(danmaku => {
            if (danmaku.element && danmaku.element.parentNode) {
                danmaku.element.remove();
            }
        });
        
        // 清空列表
        this.state.activeDanmaku = [];
        
        // 重置轨道状态
        this.state.tracks.forEach(track => {
            track.occupied = false;
            track.lastEndTime = 0;
        });
    },
    
    /**
     * 设置视频播放监听器
     */
    setupVideoListeners() {
        const videoElement = document.querySelector('video.html5-main-video');
        if (!videoElement) return;
        
        // 监听播放时间更新
        videoElement.addEventListener('timeupdate', () => {
            if (this.state.enabled) {
                this.checkAndDisplayTimedDanmaku(videoElement.currentTime);
            }
        });
        
        // 监听暂停/播放
        videoElement.addEventListener('pause', () => {
            this.pauseAllDanmaku();
        });
        
        videoElement.addEventListener('play', () => {
            this.resumeAllDanmaku();
        });
        
        // 监听跳转
        videoElement.addEventListener('seeked', () => {
            console.log('视频跳转到:', videoElement.currentTime);
            this.clearAllDanmaku();
            this.state.recentlyDisplayedIds.clear(); // 清除最近显示记录
            
            // 跳转后立即检查当前时间的弹幕
            setTimeout(() => {
                console.log('跳转后开始检查弹幕...');
                this.checkAndDisplayTimedDanmaku(videoElement.currentTime);
                // 也显示稍微之前的弹幕，模拟用户可能错过的弹幕
                this.displayRecentDanmaku(videoElement.currentTime);
            }, 200); // 增加延迟确保DOM更新完成
        });
    },
    
    /**
     * 检查并显示时间相关的弹幕
     */
    checkAndDisplayTimedDanmaku(currentTime) {
        // 防抖：防止在短时间内重复执行
        const now = Date.now();
        if (now - this.state.lastCheckTime < 100) {
            return;
        }
        this.state.lastCheckTime = now;

        // 从缓存中查找当前时间附近的弹幕
        const timeWindow = 0.5; // 0.5秒的时间窗口，平衡精确度和容错性
        
        this.state.danmakuCache.forEach(danmaku => {
            // 检查弹幕是否应该在当前时间显示
            // 弹幕时间应该小于等于当前时间，且在时间窗口内
            const timeDiff = currentTime - danmaku.videoTimestamp;
            
            if (timeDiff >= 0 && timeDiff <= timeWindow) {
                this.requestDisplayDanmaku(danmaku);
            }
        });
    },
    
    /**
     * 显示最近的弹幕（用于跳转后显示）
     */
    displayRecentDanmaku(currentTime) {
        const recentWindow = 5; // 显示过去5秒内的弹幕
        let delayCounter = 0;
        
        // 先收集符合条件的弹幕，按时间排序
        const recentDanmaku = [];
        this.state.danmakuCache.forEach(danmaku => {
            const timeDiff = currentTime - danmaku.videoTimestamp;
            
            // 显示过去几秒内的弹幕
            if (timeDiff > 0.5 && timeDiff <= recentWindow) { // 避免与当前时间弹幕重叠
                const isAlreadyActive = this.state.activeDanmaku.some(d => d.id === danmaku.id);
                const wasRecentlyDisplayed = this.state.recentlyDisplayedIds.has(danmaku.id);

                if (!isAlreadyActive && !wasRecentlyDisplayed) {
                    recentDanmaku.push({ danmaku, timeDiff });
                }
            }
        });
        
        // 按时间倒序排列（最近的先显示）
        recentDanmaku.sort((a, b) => a.timeDiff - b.timeDiff);
        
        // 分批显示，避免同时出现太多弹幕
        recentDanmaku.forEach(({ danmaku, timeDiff }, index) => {
            const delay = index * 200; // 每200ms显示一条
            setTimeout(() => {
                console.log(`显示历史弹幕: "${danmaku.content}" (${timeDiff.toFixed(1)}秒前)`);
                this.requestDisplayDanmaku(danmaku);
            }, delay);
        });
        
        console.log(`准备显示 ${recentDanmaku.length} 条历史弹幕`);
    },

    /**
     * 暂停所有弹幕
     */
    pauseAllDanmaku() {
        console.log('暂停所有弹幕');
        this.state.activeDanmaku.forEach(danmaku => {
            if (danmaku.element) {
                danmaku.element.style.animationPlayState = 'paused';
            }
            // 清除移除计时器，并计算剩余时间
            clearTimeout(danmaku.removalTimeoutId);
            const elapsedTime = Date.now() - danmaku.startTime;
            danmaku.remainingTime = danmaku.duration - elapsedTime;
        });
    },
    
    /**
     * 恢复所有弹幕
     */
    resumeAllDanmaku() {
        console.log('恢复所有弹幕');
        const danmakuToRemove = [];
        
        this.state.activeDanmaku.forEach(danmaku => {
            if (danmaku.element) {
                danmaku.element.style.animationPlayState = 'running';
            }
            // 重置计时器
            danmaku.startTime = Date.now(); // 更新开始时间
            danmaku.duration = danmaku.remainingTime; // 更新总时长为剩余时长
            if (danmaku.duration > 0) {
                danmaku.removalTimeoutId = setTimeout(() => {
                    this.removeDanmaku(danmaku);
                }, danmaku.duration);
            } else {
                // 如果剩余时间已到，收集起来稍后移除
                danmakuToRemove.push(danmaku);
            }
        });

        // 在循环结束后安全地移除弹幕
        if (danmakuToRemove.length > 0) {
            console.log(`移除 ${danmakuToRemove.length} 个已结束的弹幕`);
            danmakuToRemove.forEach(d => this.removeDanmaku(d));
        }
    },
    
    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info') {
        // 使用学习模式的通知系统
        if (window.StudyModeUtils && window.StudyModeUtils.showNotification) {
            window.StudyModeUtils.showNotification(message, type);
        } else {
            console.log(`[弹幕] ${message}`);
        }
    },
    
    /**
     * 提取视频ID
     */
    extractVideoId(url) {
        const match = url.match(/[?&]v=([^&]+)/);
        return match ? match[1] : null;
    },
    
    /**
     * 调整弹幕容器大小
     */
    resizeDanmakuContainer() {
        if (!this.state.danmakuContainer || !this.state.playerContainer) return;
        
        // 重新初始化轨道
        this.initTracks();
        
        console.log('弹幕容器大小已调整');
    },
    
    /**
     * 测试弹幕显示（调试用）
     */
    testDanmaku() {
        console.log('开始弹幕测试');
        
        // 创建多条测试弹幕
        const testDanmakus = [
            {
                id: 'test-1-' + Date.now(),
                content: '测试弹幕1 - 完整穿越播放器',
                videoTimestamp: 0,
                color: '#00FF00',
                type: 1
            },
            {
                id: 'test-2-' + Date.now(),
                content: '测试弹幕2 - 检查移动距离',
                videoTimestamp: 0,
                color: '#FF0000',
                type: 1
            },
            {
                id: 'test-3-' + Date.now(),
                content: '测试弹幕3 - 验证动画效果',
                videoTimestamp: 0,
                color: '#0000FF',
                type: 1
            }
        ];
        
        // 分别显示测试弹幕
        testDanmakus.forEach((danmaku, index) => {
            setTimeout(() => {
                this.displayDanmaku(danmaku);
                console.log(`测试弹幕 ${index + 1} 已发送`);
            }, index * 1000);
        });
        
        console.log('测试弹幕序列已启动');
    },
    
    /**
     * 获取弹幕系统状态（调试用）
     */
    getStatus() {
        return {
            enabled: this.state.enabled,
            containerExists: !!this.state.danmakuContainer,
            containerSize: this.state.danmakuContainer ? {
                width: this.state.danmakuContainer.offsetWidth,
                height: this.state.danmakuContainer.offsetHeight
            } : null,
            tracksCount: this.state.tracks.length,
            activeDanmakuCount: this.state.activeDanmaku.length,
            cachedDanmakuCount: this.state.danmakuCache.size,
            currentVideoId: this.state.currentVideoId
        };
    },
    
    /**
     * 清理弹幕系统
     */
    cleanup() {
        console.log('清理弹幕系统');
        
        // 清除所有弹幕
        this.clearAllDanmaku();
        
        // 移除容器
        if (this.state.danmakuContainer) {
            this.state.danmakuContainer.remove();
            this.state.danmakuContainer = null;
        }
        
        // 移除控制面板
        if (this.state.controlPanel) {
            this.state.controlPanel.remove();
            this.state.controlPanel = null;
        }
        
        // 清空状态
        this.state.danmakuCache.clear();
        this.state.activeDanmaku = [];
        this.state.tracks = [];
    },
    
    /**
     * 请求显示弹幕（防重叠的唯一入口）
     */
    requestDisplayDanmaku(danmaku) {
        // 唯一的防重叠检查点
        const isAlreadyActive = this.state.activeDanmaku.some(d => d.id === danmaku.id);
        if (isAlreadyActive) {
            return; // 已经在屏幕上，忽略
        }

        const wasRecentlyDisplayed = this.state.recentlyDisplayedIds.has(danmaku.id);
        if (wasRecentlyDisplayed) {
            return; // 最近刚显示过，冷却中，忽略
        }

        // 通过所有检查，准予显示
        this.state.recentlyDisplayedIds.add(danmaku.id);
        setTimeout(() => {
            this.state.recentlyDisplayedIds.delete(danmaku.id);
        }, this.state.displayCooldown);

        this.displayDanmaku(danmaku);
    }
}; 