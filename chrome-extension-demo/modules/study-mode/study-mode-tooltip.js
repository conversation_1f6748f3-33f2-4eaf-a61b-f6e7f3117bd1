// Study Mode Tooltip Module - 词组Tooltip模块
console.log('Study Mode Tooltip module loaded');

// 全局变量
let favoritedWords = new Set(); // 保存已收藏词组的集合
let tooltipHideTimer = null; // 隐藏tooltip的计时器
let tooltipShowTimer = null; // 显示tooltip的计时器
let currentActiveSpan = null; // 当前触发tooltip的span元素
let eventsInitialized = false; // 标记事件是否已初始化，防止重复绑定
let tooltipShowDelay = 250; // 鼠标停留多久后显示tooltip（毫秒）

// 权限缓存（与SubtitleCore共享）
function getPermissionCache() {
    return window.SubtitleCore?.permissionCache || {
        isPlusOrPremium: false,
        lastCheckTime: 0,
        cacheValidDuration: 5 * 60 * 1000
    };
}

// 检查Plus/Premium权限（使用缓存）
async function checkPlusOrPremiumPermissionCached() {
    const now = Date.now();
    const cache = getPermissionCache();
    
    // 如果缓存仍然有效，直接返回缓存结果
    if (cache.lastCheckTime && (now - cache.lastCheckTime) < cache.cacheValidDuration) {
        console.log('[Tooltip] 使用缓存的权限结果:', cache.isPlusOrPremium);
        return cache.isPlusOrPremium;
    }
    
    console.log('[Tooltip] 权限缓存已过期或不存在，重新检查权限');
    
    try {
        // 通过background.js检查权限
        const permissionResult = await new Promise((resolve) => {
            chrome.runtime.sendMessage({
                action: 'checkPlusOrPremiumPermission'
            }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('[Tooltip] 检查Plus/Premium权限失败:', chrome.runtime.lastError.message);
                    resolve({
                        success: false,
                        isPlusOrPremium: false
                    });
                } else {
                    resolve(response);
                }
            });
        });
        
        // 更新缓存
        cache.isPlusOrPremium = permissionResult.success && permissionResult.isPlusOrPremium;
        cache.lastCheckTime = now;
        
        console.log('[Tooltip] 权限检查完成，结果:', cache.isPlusOrPremium);
        return cache.isPlusOrPremium;
        
    } catch (error) {
        console.error('[Tooltip] 权限检查失败:', error);
        // 出错时默认为false，但不更新缓存时间，下次会重试
        return false;
    }
}

/**
 * 词组Tooltip初始化
 */

// 初始化词组tooltip功能
function initializeWordTooltip() {
  console.log('初始化词组tooltip功能');
  
  // 创建tooltip元素
  const tooltipElement = document.createElement('div');
  tooltipElement.id = 'extension-word-tooltip';
  tooltipElement.innerHTML = `
    <div id="extension-tooltip-text"></div>
    <i id="extension-favorite-icon" class="far fa-heart"></i>
  `;
  // 初始时将tooltip设置为隐藏状态
  tooltipElement.classList.add('hidden');
  document.body.appendChild(tooltipElement);
  
  // 加载tooltip样式
  const tooltipStyle = document.createElement('link');
  tooltipStyle.rel = 'stylesheet';
  tooltipStyle.href = chrome.runtime.getURL('modules/study-mode/word-tooltip.css');
  document.head.appendChild(tooltipStyle);

  // Load Font Awesome for icons
  const fontAwesomeLink = document.createElement('link');
  fontAwesomeLink.rel = 'stylesheet';
  fontAwesomeLink.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css';
  document.head.appendChild(fontAwesomeLink);
  
  // 监听字幕区域的变化，为新的字幕添加事件监听
  const observer = new MutationObserver(function(mutations) {
    let hasNewSubtitles = false;
    
    mutations.forEach(function(mutation) {
      // 检查是否有新的字幕元素被添加
      if (mutation.addedNodes.length) {
        for (let i = 0; i < mutation.addedNodes.length; i++) {
          const node = mutation.addedNodes[i];
          if (node.id === 'extension-subtitle-ja' || 
              (node.nodeType === 1 && node.querySelector('#extension-subtitle-ja'))) {
            hasNewSubtitles = true;
            break;
          }
        }
      }
    });
    
    // 只有当确实有新的字幕元素时才重新设置事件
    if (hasNewSubtitles) {
      console.log('检测到新的字幕元素，重新设置事件监听器');
      // 首先移除所有旧的事件监听器
      removeSubtitleHoverEvents();
      // 然后设置新的事件监听器
      setupSubtitleHoverEvents();
    }
  });
  
  // 开始观察字幕区域的变化
  const subtitleContainer = document.getElementById('extension-subtitle-container');
  if (subtitleContainer) {
    observer.observe(subtitleContainer, { childList: true, subtree: true });
  }
  
  // 初始设置字幕hover事件
  setupSubtitleHoverEvents();
}

/**
 * 事件管理
 */

// 移除字幕hover事件，防止重复绑定
function removeSubtitleHoverEvents() {
  const tooltip = document.getElementById('extension-word-tooltip');
  const favoriteIcon = document.getElementById('extension-favorite-icon');
  const subtitleJa = document.getElementById('extension-subtitle-ja');
  
  if (subtitleJa) {
    // 使用命名函数的克隆，移除所有事件监听器
    subtitleJa.replaceWith(subtitleJa.cloneNode(true));
    console.log('已移除字幕元素的所有事件监听器');
  }
  
  if (tooltip) {
    // 移除tooltip的事件监听器
    tooltip.replaceWith(tooltip.cloneNode(true));
    console.log('已移除tooltip的所有事件监听器');
  }
  
  // 重置事件初始化标记
  eventsInitialized = false;
}

// 设置字幕hover事件
function setupSubtitleHoverEvents() {
  // 如果事件已经初始化，不再重复绑定
  if (eventsInitialized) {
    console.log('事件监听器已经初始化，跳过重复绑定');
    return;
  }
  
  const tooltip = document.getElementById('extension-word-tooltip');
  const tooltipText = document.getElementById('extension-tooltip-text');
  const favoriteIcon = document.getElementById('extension-favorite-icon');
  const subtitleJa = document.getElementById('extension-subtitle-ja');

  if (!subtitleJa || !tooltip || !tooltipText || !favoriteIcon) {
    console.warn('Required elements for tooltip hover not found.');
    return;
  }
  
  console.log('设置字幕hover事件监听器...');
  let isTooltipVisible = false; // 本地变量，用于当前函数范围内

  // 为日语字幕添加鼠标事件
  subtitleJa.addEventListener('mouseover', async function(e) {
    // 确保目标是带有pos-类的span元素（只有Plus/Premium用户才有这些元素）
    if (e.target.tagName === 'SPAN' && e.target.className.includes('pos-')) {
      // 检查Plus/Premium权限
      const hasAdvancedPermission = await checkPlusOrPremiumPermissionCached();
      
      if (!hasAdvancedPermission) {
        console.log('[Tooltip] 用户无Plus/Premium权限，不显示tooltip');
        return;
      }
      
      // 清除可能存在的隐藏定时器
      if (tooltipHideTimer) {
        clearTimeout(tooltipHideTimer);
        tooltipHideTimer = null;
      }
      
      // 如果已经有显示定时器，但是鼠标移动到了另一个词组，则取消之前的定时器
      if (tooltipShowTimer && currentActiveSpan !== e.target) {
        clearTimeout(tooltipShowTimer);
        tooltipShowTimer = null;
      }
      
      // 如果不存在显示定时器，创建一个新的
      if (!tooltipShowTimer) {
        currentActiveSpan = e.target; // 设置当前活动的span
        
        console.log('创建延时显示定时器，在', tooltipShowDelay, 'ms后显示tooltip');
        // 创建延时显示定时器
        tooltipShowTimer = setTimeout(function() {
          // 只有在鼠标仍然在该元素上时才显示tooltip
          if (currentActiveSpan && currentActiveSpan.matches(':hover')) {
            console.log('延时显示定时器触发，显示tooltip');
            
            // 暂停视频
            const videoElement = document.querySelector('video.html5-main-video');
            if (videoElement && !videoElement.paused) {
              videoElement.pause();
            }
            
            // 获取词组文本
            const currentJapaneseWord = currentActiveSpan.textContent.trim();
            
            // 先设置tooltip可见但隐藏，以便获取正确的偏移高度
            tooltip.style.visibility = 'hidden'; // 隐藏以便计算尺寸
            tooltip.classList.remove('hidden');   // 移除 display:none 样式
            
            // 获取元素位置并计算tooltip位置
            const rect = currentActiveSpan.getBoundingClientRect();
            const tooltipHeight = tooltip.offsetHeight;
            const tooltipWidth = tooltip.offsetWidth;
            
            // 计算tooltip的位置（在词组上方居中，并紧贴词组）
            tooltip.style.left = `${rect.left + (rect.width / 2) - (tooltipWidth / 2)}px`;
            tooltip.style.top = `${rect.top - tooltipHeight - 0}px`; // 将间距调整为0，使其紧贴词组
            
            // 设置低注背景色与词组背景色相同
            const computedStyle = window.getComputedStyle(currentActiveSpan);
            const bgImage = computedStyle.backgroundImage;
            
            if (bgImage && bgImage !== 'none') {
              const color = getColorFromGradient(bgImage);
              tooltip.style.backgroundColor = color;
              tooltip.style.opacity = '0.95';
              tooltip.style.borderColor = color;
              tooltip.style.color = '#333';
            } else {
              tooltip.style.backgroundColor = '#ffffff';
              tooltip.style.opacity = '0.95';
              tooltip.style.borderColor = '#e5e7eb';
            }
            
            // 立即显示加载提示并使tooltip可见
            tooltipText.textContent = '翻译中...';
            tooltip.style.visibility = 'visible';
            isTooltipVisible = true;
            
            // 设置收藏图标状态
            favoriteIcon.className = favoritedWords.has(currentJapaneseWord) ? 'fas fa-heart text-red-500' : 'far fa-heart';
            favoriteIcon.style.color = (bgImage && bgImage !== 'none') ? '#333' : '#9ca3af';
            
            // 调用翻译API
            handleWordTranslation(currentJapaneseWord, tooltipText);
          } else {
            console.log('鼠标已移出词组，取消显示tooltip');
          }
          
          // 清除定时器
          tooltipShowTimer = null;
        }, tooltipShowDelay); // 延时显示，默认 250ms
      }
    }
  });
  
  // 鼠标进入tooltip时的处理
  tooltip.addEventListener('mouseenter', function() {
    if (tooltipHideTimer) clearTimeout(tooltipHideTimer);
    tooltipHideTimer = null;
    isTooltipVisible = true;
  });
  
  // 处理字幕span元素的鼠标移出事件 - 不立即隐藏tooltip
  subtitleJa.addEventListener('mouseout', function(e) {
    // 确保目标是带有pos-类的span元素
    if (e.target.tagName === 'SPAN' && e.target.className.includes('pos-')) {
      // 如果鼠标移出了词组，但还没有显示tooltip，取消定时器
      if (tooltipShowTimer) {
        clearTimeout(tooltipShowTimer);
        tooltipShowTimer = null;
      }
      
      // 设置隐藏定时器
      if (tooltipHideTimer) clearTimeout(tooltipHideTimer);
      tooltipHideTimer = setTimeout(() => {
        if (!tooltip.matches(':hover') && !(currentActiveSpan && currentActiveSpan.matches(':hover'))) {
          console.log('Hiding tooltip (mouseout from subtitleJa)');
          tooltip.classList.add('hidden');
          isTooltipVisible = false;
          currentActiveSpan = null; // Clear active span
        }
      }, 300); // Delay before hiding
    }
  });
  
  // tooltip鼠标离开事件
  tooltip.addEventListener('mouseleave', function(e) {
    if (tooltipHideTimer) clearTimeout(tooltipHideTimer);
    tooltipHideTimer = setTimeout(() => {
      if (!(currentActiveSpan && currentActiveSpan.matches(':hover')) && !tooltip.matches(':hover')) {
        console.log('Hiding tooltip (mouseleave from tooltip)');
        tooltip.classList.add('hidden');
        isTooltipVisible = false;
        currentActiveSpan = null; // Clear active span
      }
    }, 300);
  });
  
  // 为整个文档添加点击事件，在点击其他区域时隐藏tooltip
  document.addEventListener('click', function(e) {
    // 如果点击的不是tooltip、tooltip内部元素、或字幕span，则隐藏tooltip
    if (!tooltip.contains(e.target) && 
        !(e.target.tagName === 'SPAN' && e.target.className.includes('pos-'))) {
      tooltip.classList.add('hidden');
      isTooltipVisible = false;
    }
  });
  
  // 收藏图标点击事件
  setupFavoriteIconEvents(favoriteIcon, tooltipText);
  
  // 标记事件已初始化
  eventsInitialized = true;
  console.log('所有事件监听器设置完成');
}

/**
 * 收藏功能
 */

// 设置收藏图标事件
function setupFavoriteIconEvents(favoriteIcon, tooltipText) {
  const onFavoriteClick = async function() {
    if (!currentActiveSpan) return;
    
    // 检查Plus/Premium权限
    const hasAdvancedPermission = await checkPlusOrPremiumPermissionCached();
    
    if (!hasAdvancedPermission) {
      console.log('[Tooltip] 用户无Plus/Premium权限，不允许收藏');
      tooltipText.textContent = '收藏功能需要Plus或Premium会员';
      setTimeout(() => {
        if (currentActiveSpan) {
          tooltipText.textContent = currentActiveSpan.textContent.trim();
        }
      }, 2000);
      return;
    }
    
    const currentWord = currentActiveSpan.textContent.trim();
    if (!currentWord) return;
    
    console.log(`收藏图标被点击，处理词组: ${currentWord}`);

    const isFavorited = favoritedWords.has(currentWord);
    
    try {
      // 获取当前选择的语言
      const selectedLanguage = await getSelectedLanguage();
      console.log(`当前选择的语言: ${selectedLanguage}`);
      
      let wordToProcess = currentWord;
      
      // 对所有语言都进行词形还原处理，与翻译功能保持一致
      const normalizeResponse = await new Promise((resolve) => {
        chrome.runtime.sendMessage({
          action: 'proxyFetch',
          url: 'http://localhost:8080/api/subtitle/normalize',
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            word: currentWord,
            language: selectedLanguage 
          })
        }, resolve);
      });
      
      if (normalizeResponse && normalizeResponse.success) {
        wordToProcess = normalizeResponse.data.success ? 
                        normalizeResponse.data.originalWord : 
                        currentWord;
        
        if (wordToProcess !== currentWord) {
          console.log(`收藏前词形还原 (${selectedLanguage}): ${currentWord} -> ${wordToProcess}`);
        }
      } else {
        console.error('词形还原失败:', normalizeResponse);
        tooltipText.textContent = `操作失败: 无法处理词组`;
        setTimeout(() => {
            tooltipText.textContent = currentWord;
        }, 2000);
        return;
      }
      
      // 使用正确的API路径
      const apiUrl = isFavorited ? 
                    'http://localhost:8080/api/subtitle/unfavorite' : 
                    'http://localhost:8080/api/subtitle/favorite';
      
      // 构建请求体，包含lang参数
      const requestBody = isFavorited ? 
                         { word: wordToProcess, lang: selectedLanguage } : 
                         { word: wordToProcess, lang: selectedLanguage };
      
      const response = await new Promise((resolve) => {
        chrome.runtime.sendMessage({
          action: 'proxyFetch',
          url: apiUrl,
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestBody)
        }, resolve);
      });
      
      if (response && response.success && response.data && response.data.success) {
        if (isFavorited) {
          favoritedWords.delete(currentWord);
          favoriteIcon.className = 'far fa-heart';
          console.log(`取消收藏成功: ${currentWord} (词形还原后: ${wordToProcess})`);
        } else {
          favoritedWords.add(currentWord);
          favoriteIcon.className = 'fas fa-heart text-red-500';
          console.log(`收藏成功: ${currentWord} -> ${wordToProcess} (语言: ${selectedLanguage})`);
        }
        // 根据背景色调整图标颜色
        const computedStyle = window.getComputedStyle(currentActiveSpan);
        const bgImage = computedStyle.backgroundImage;
        favoriteIcon.style.color = (bgImage && bgImage !== 'none') ? '#333' : (isFavorited ? '#9ca3af' : '#ef4444');
      } else {
        console.error(`${isFavorited ? '取消收藏' : '收藏'}请求失败:`, response);
        // 这里可以给用户一个更友好的提示，例如 tooltip 内容暂时变为 "操作失败"
        const originalTooltipText = tooltipText.textContent;
        tooltipText.textContent = `${isFavorited ? '取消收藏' : '收藏'}失败`;
        setTimeout(() => {
            tooltipText.textContent = originalTooltipText;
        }, 2000);
      }
    } catch (error) {
      console.error('收藏操作失败:', error);
      tooltipText.textContent = `操作失败: ${error.message}`;
      setTimeout(() => {
          tooltipText.textContent = currentWord;
      }, 2000);
    }
  };
  
  // 移除可能存在的旧事件监听器
  favoriteIcon.removeEventListener('click', onFavoriteClick);
  // 添加新的事件监听器
  favoriteIcon.addEventListener('click', onFavoriteClick);
}

/**
 * 翻译功能
 */

// 处理词组翻译
async function handleWordTranslation(currentJapaneseWord, tooltipText) {
  try {
    // 获取当前选择的语言
    const selectedLanguage = await getSelectedLanguage();
    
    let wordToTranslate = currentJapaneseWord;
    
    // 对所有语言都进行词形还原处理
    const normalizeResponse = await new Promise((resolve) => {
      chrome.runtime.sendMessage({
        action: 'proxyFetch',
        url: 'http://localhost:8080/api/subtitle/normalize',
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          word: currentJapaneseWord, 
          language: selectedLanguage 
        })
      }, resolve);
    });
    
    if (normalizeResponse && normalizeResponse.success) {
      wordToTranslate = normalizeResponse.data.success ? 
                       normalizeResponse.data.originalWord : 
                       currentJapaneseWord;
      
      if (wordToTranslate !== currentJapaneseWord) {
        console.log(`翻译前词形还原 (${selectedLanguage}): ${currentJapaneseWord} -> ${wordToTranslate}`);
      }
    } else {
      console.error('词形还原失败:', normalizeResponse);
    }
    
    // 调用翻译接口，传入语言参数
    chrome.runtime.sendMessage({
      action: 'proxyFetch',
      url: 'http://localhost:8080/api/translate/word',
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: `text=${encodeURIComponent(wordToTranslate)}&language=${encodeURIComponent(selectedLanguage)}`
    }, function(translateResponse) {
      if (translateResponse && translateResponse.success && 
          translateResponse.data && translateResponse.data.success) {
        // 显示翻译结果
        tooltipText.textContent = translateResponse.data.translation;
        console.log(`翻译成功: ${currentJapaneseWord} -> ${translateResponse.data.translation} (语言: ${selectedLanguage}, 源语言: ${translateResponse.data.fromLanguage})`);
      } else {
        tooltipText.textContent = currentJapaneseWord; // 回退到原文
        console.error('翻译失败:', translateResponse);
      }
    });
  } catch (error) {
    console.error('翻译请求失败:', error);
    tooltipText.textContent = currentJapaneseWord; // 回退到原文
  }
}

// 获取当前选择的语言
async function getSelectedLanguage() {
  return new Promise((resolve) => {
    chrome.storage.sync.get(['selectedVideoLanguage'], function(result) {
      const language = result.selectedVideoLanguage || 'japanese'; // 默认为日语
      resolve(language);
    });
  });
}

/**
 * 工具函数
 */

// 从渐变背景中提取颜色
function getColorFromGradient(gradientString) {
  // 提取颜色，格式为 linear-gradient(to top, COLOR 50%, transparent 50%)
  const match = gradientString.match(/#[a-fA-F0-9]{6}|#[a-fA-F0-9]{3}|rgba?\([^)]+\)|hsla?\([^)]+\)/);
  const color = match ? match[0] : '#e5e7eb'; // 默认浅灰色
  
  // 返回完全不透明的颜色
  if (color.startsWith('#')) {
    // 如果是十六进制颜色，直接返回，不添加透明度
    return color;
  } else if (color.startsWith('rgba')) {
    // 如果是 RGBA，将透明度设为 1
    return color.replace(/rgba\([^,]+,[^,]+,[^,]+,[^)]+\)/, (match) => {
      const parts = match.match(/rgba\(([^,]+),([^,]+),([^,]+),([^)]+)\)/);
      if (parts) {
        return `rgba(${parts[1]},${parts[2]},${parts[3]},1)`;
      }
      return match;
    });
  } else if (color.startsWith('rgb')) {
    // 如果是 RGB，保持不变
    return color;
  }
  return color;
}

// 检查鼠标是否在tooltip上
function isMouseOverTooltip(e) {
  const tooltip = document.getElementById('extension-word-tooltip');
  if (!tooltip) return false;
  
  return tooltip === document.activeElement || 
         tooltip.matches(':hover') || 
         tooltip.contains(document.activeElement);
}

/**
 * 清理功能
 */

// 清理tooltip相关功能
function cleanupTooltip() {
  // 清除定时器
  if (tooltipHideTimer) {
    clearTimeout(tooltipHideTimer);
    tooltipHideTimer = null;
  }
  
  if (tooltipShowTimer) {
    clearTimeout(tooltipShowTimer);
    tooltipShowTimer = null;
  }
  
  // 移除事件监听器
  removeSubtitleHoverEvents();
  
  // 移除tooltip元素
  const tooltip = document.getElementById('extension-word-tooltip');
  if (tooltip) {
    tooltip.remove();
  }
  
  // 重置状态
  currentActiveSpan = null;
  eventsInitialized = false;
  favoritedWords.clear();
  
  console.log('Tooltip功能已清理');
}

// 导出Tooltip模块功能，供其他模块使用
window.StudyModeTooltip = {
  // 初始化
  initializeWordTooltip,
  
  // 事件管理
  setupSubtitleHoverEvents,
  removeSubtitleHoverEvents,
  
  // 收藏功能
  setupFavoriteIconEvents,
  
  // 翻译功能
  handleWordTranslation,
  
  // 工具函数
  getColorFromGradient,
  isMouseOverTooltip,
  
  // 清理功能
  cleanupTooltip,
  
  // 状态访问
  getFavoritedWords: () => favoritedWords,
  getCurrentActiveSpan: () => currentActiveSpan
}; 