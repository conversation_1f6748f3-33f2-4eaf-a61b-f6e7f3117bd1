// Study Mode Session Module - 会话管理模块
console.log('Study Mode Session module loaded');

// 记录上次验证时间，避免频繁验证
let lastSessionCheckTime = 0;
const SESSION_CHECK_INTERVAL = 300000; // 5分钟检查一次，增加间隔时间

/**
 * 会话验证功能
 */

// 验证会话状态的函数 - 简化版，不再执行实际验证
function validateSession() {
  return new Promise((resolve) => {
    // 简化认证机制，默认会话总是有效
    console.log('会话验证已简化，默认为有效');
    resolve(true);
  });
}

// 检查用户认证状态
async function checkUserAuthentication() {
  try {
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        action: 'validateSession'
      }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
    
    return response.isValid;
  } catch (error) {
    console.error('检查用户认证状态失败:', error);
    return false;
  }
}

/**
 * 会话ID管理
 */

// 生成或获取会话ID（与视频绑定）
function getOrCreateSessionId() {
  // 获取当前视频ID
  const currentVideoId = window.StudyModeUtils.extractVideoId(window.location.href);
  if (!currentVideoId) {
    console.warn('无法获取当前视频ID，使用通用会话');
    return 'session_unknown_' + Date.now();
  }
  
  // 使用视频ID作为会话键的一部分
  const storageKey = `youtube_chat_session_${currentVideoId}`;
  let sessionId = sessionStorage.getItem(storageKey);
  
  if (!sessionId) {
    // 生成新的会话ID，包含视频ID
    sessionId = `session_${currentVideoId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem(storageKey, sessionId);
    console.log(`为视频 ${currentVideoId} 创建新的聊天会话: ${sessionId}`);
  } else {
    console.log(`使用视频 ${currentVideoId} 的现有会话: ${sessionId}`);
  }
  
  return sessionId;
}

// 清除聊天会话
async function clearChatSession() {
  try {
    const sessionId = getOrCreateSessionId();
    
    // 使用background.js代理请求，避免跨域问题
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        action: 'chatClear',
        sessionId: sessionId
      }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
    
    if (response.success) {
      // 清除当前视频的本地会话ID
      const currentVideoId = window.StudyModeUtils.extractVideoId(window.location.href);
      if (currentVideoId) {
        const storageKey = `youtube_chat_session_${currentVideoId}`;
        sessionStorage.removeItem(storageKey);
        console.log(`视频 ${currentVideoId} 的聊天会话已清除`);
      }
    }
  } catch (error) {
    console.error('清除聊天会话失败:', error);
  }
}

/**
 * 会话状态检查
 */

// 检查学习模式和会话状态
async function checkStudyModeAndSession() {
  try {
    // 检查当前时间，避免频繁检查
    const currentTime = Date.now();
    if (currentTime - lastSessionCheckTime < SESSION_CHECK_INTERVAL) {
      console.log('距离上次会话检查时间过短，跳过检查');
      return;
    }
    
    lastSessionCheckTime = currentTime;
    
    // 简化会话检查，默认会话有效
    console.log('定期会话检查：会话状态有效');
    
  } catch (error) {
    console.error('检查学习模式和会话状态时出错:', error);
  }
}

/**
 * 监听视频播放状态变化
 */

// 监听视频播放状态变化 - 简化版，不再检查会话
function setupVideoPlaybackMonitoring() {
  const videoElements = document.querySelectorAll('video');
  videoElements.forEach(video => {
    video.addEventListener('play', function() {
      // 移除会话验证检查
      console.log('视频开始播放');
    });
  });
}

// 在页面加载和DOM变化时设置视频监听
function setupVideoListeners() {
  setupVideoPlaybackMonitoring();
  
  // 使用MutationObserver监听DOM变化，以便在新视频加载时添加事件监听器
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.addedNodes.length) {
        setupVideoPlaybackMonitoring();
      }
    });
  });
  
  observer.observe(document.body, { childList: true, subtree: true });
}

/**
 * 页面卸载事件监听
 */

// 设置页面卸载事件监听器
function setupPageUnloadListener() {
  window.addEventListener('beforeunload', function() {
    // 在页面关闭前清理字幕文件
    if (window.studyModeActive && window.subtitleManager && window.subtitleManager.subtitleFileId) {
      console.log('页面关闭前清理字幕文件');
      
      // 使用同步方式发送消息，确保在页面关闭前处理
      navigator.sendBeacon(
        'http://localhost:8080/api/extension/subtitles/cleanup', 
        JSON.stringify({subtitleFileId: window.subtitleManager.subtitleFileId})
      );
      
      // 也向background.js发送清理请求，以防sendBeacon失败
      try {
        chrome.runtime.sendMessage({
          action: 'cleanupExtensionSubtitles',
          subtitleFileId: window.subtitleManager.subtitleFileId,
          isCritical: true // 标记为关键请求
        });
      } catch (e) {
        console.error('向background发送清理请求失败:', e);
      }
    }
  });
}

// 导出会话管理函数，供其他模块使用
window.StudyModeSession = {
  // 会话验证
  validateSession,
  checkUserAuthentication,
  
  // 会话ID管理
  getOrCreateSessionId,
  clearChatSession,
  
  // 会话状态检查
  checkStudyModeAndSession,
  
  // 视频监听
  setupVideoPlaybackMonitoring,
  setupVideoListeners,
  
  // 页面卸载监听
  setupPageUnloadListener,
  
  // 常量
  SESSION_CHECK_INTERVAL
}; 