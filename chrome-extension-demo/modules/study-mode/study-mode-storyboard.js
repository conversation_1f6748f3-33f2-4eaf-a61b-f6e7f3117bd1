// Study Mode Storyboard Preview Module - YouTube视频缩略图预览模块
console.log('[Storyboard] 模块已加载');

// 全局变量
let isStoryboardEnabled = true; // 是否启用storyboard预览

/**
 * 章节管理器
 */
class ChapterManager {
  constructor() {
    this.chapters = [];
    this.lastFetchTime = 0;
    this.cacheTimeout = 30000; // 30秒缓存
  }

  /**
   * 获取章节信息
   */
  async getChapters() {
    const now = Date.now();

    // 如果缓存还有效，直接返回
    if (this.chapters.length > 0 && (now - this.lastFetchTime) < this.cacheTimeout) {
      return this.chapters;
    }

    try {
      // 使用用户提供的代码获取章节信息
      const uniqueChapters = new Map();

      document.querySelectorAll('ytd-macro-markers-list-renderer')
        .forEach(renderer => {
          renderer.querySelectorAll('ytd-macro-markers-list-item-renderer')
            .forEach(item => {
              const title = item.querySelector('h4[title]')?.getAttribute('title') || '';
              const time = item.querySelector('#time')?.textContent.trim() || '';
              const key = `${title}-${time}`;
              if (!uniqueChapters.has(key)) {
                uniqueChapters.set(key, { title, time });
              }
            });
        });

      const chapters = Array.from(uniqueChapters.values());

      // 转换时间格式并排序
      this.chapters = chapters.map(chapter => ({
        title: chapter.title,
        time: chapter.time,
        timeInSeconds: this.timeStringToSeconds(chapter.time)
      })).sort((a, b) => a.timeInSeconds - b.timeInSeconds);

      this.lastFetchTime = now;

      console.log('[Storyboard] 获取到章节信息:', this.chapters);
      return this.chapters;
    } catch (error) {
      console.warn('[Storyboard] 获取章节信息失败:', error);
      return [];
    }
  }

  /**
   * 将时间字符串转换为秒数
   */
  timeStringToSeconds(timeStr) {
    if (!timeStr) return 0;

    const parts = timeStr.split(':').map(part => parseInt(part, 10));
    if (parts.length === 2) {
      // MM:SS 格式
      return parts[0] * 60 + parts[1];
    } else if (parts.length === 3) {
      // HH:MM:SS 格式
      return parts[0] * 3600 + parts[1] * 60 + parts[2];
    }
    return 0;
  }

  /**
   * 根据时间获取对应的章节
   */
  async getChapterAtTime(timeInSeconds) {
    const chapters = await this.getChapters();

    if (chapters.length === 0) {
      return null;
    }

    // 找到当前时间对应的章节
    let currentChapter = chapters[0];

    for (let i = 0; i < chapters.length; i++) {
      if (timeInSeconds >= chapters[i].timeInSeconds) {
        currentChapter = chapters[i];
      } else {
        break;
      }
    }

    return currentChapter;
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.chapters = [];
    this.lastFetchTime = 0;
  }
}

/**
 * Storyboard预览管理器
 */
class StoryboardPreviewManager {
  constructor() {
    this.cache = new Map();
    this.config = null;
    this.canvas = null;
    this.ctx = null;
    this.previewImg = null;
    this.isLoading = false;
    this.lastRequestTime = 0;
    this.requestDelay = 200; // 增加防抖延迟到200毫秒，减少频闪
    this.lastTimeRequested = -1; // 记录上次请求的时间点
    this.chapterManager = new ChapterManager(); // 章节管理器
  }

  /**
   * 使用事件数据直接初始化（主要方法）
   */
  async initializeWithEventData(eventData) {
    try {
      console.log('[Storyboard] 使用事件数据初始化');

      if (!eventData.spec) {
        console.warn('[Storyboard] 事件数据中没有spec');
        return false;
      }

      // 解析spec
      this.config = this.parseStoryboardSpec(eventData.spec);

      if (this.config) {
        console.log('[Storyboard] 配置解析成功:', {
          分辨率: `${this.config.level.width}x${this.config.level.height}`,
          总缩略图数: this.config.level.totalThumbs,
          网格: `${this.config.level.rows}x${this.config.level.cols}`,
          持续时间: this.config.level.duration + 'ms'
        });
        this.createPreviewElements();
        return true;
      } else {
        console.warn('[Storyboard] 配置解析失败');
        return false;
      }
    } catch (error) {
      console.error('[Storyboard] 初始化失败:', error);
      return false;
    }
  }



  /**
   * 解析storyboard spec字符串
   */
  parseStoryboardSpec(spec) {
    try {
      const parts = spec.split('|');
      if (parts.length < 4) {
        console.warn('Storyboard spec格式不正确');
        return null;
      }

      const urlTemplate = parts[0];
      
      // 直接选择L2分辨率
      // spec格式: URL|L0数据|L1数据|L2数据|L3数据
      // 所以L2对应parts[3]（第4个元素）
      if (parts.length < 4 || !parts[3].includes('#')) {
        console.warn('L2分辨率数据不可用，parts长度:', parts.length);
        console.warn('parts[3]内容:', parts[3]);
        return null;
      }
      
      const params = parts[3].split('#');
      if (params.length < 8) {
        console.warn('L2分辨率参数不完整');
        return null;
      }
      
      const level = {
        width: parseInt(params[0]),
        height: parseInt(params[1]),
        totalThumbs: parseInt(params[2]),
        rows: parseInt(params[3]),
        cols: parseInt(params[4]),
        duration: parseInt(params[5]), // 每个缩略图的持续时间（毫秒）
        signature: params[7] ? params[7].replace('rs$', 'rs%24') : ''
      };
      
      console.log('L2分辨率配置:', level);

      return {
        urlTemplate: urlTemplate,
        level: level,
        levelIndex: 2 // 固定使用L2
      };
    } catch (error) {
      console.error('解析storyboard spec失败:', error);
      return null;
    }
  }

  /**
   * 创建预览相关的DOM元素
   */
  createPreviewElements() {
    // 如果旧的预览图片存在，先移除
    if (this.previewImg && this.previewImg.parentNode) {
      this.previewImg.parentNode.removeChild(this.previewImg);
      this.previewImg = null;
    }
    
    // 创建canvas用于图片裁剪
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d');
    
    // 设置canvas大小
    if (this.config && this.config.level) {
      this.canvas.width = this.config.level.width;
      this.canvas.height = this.config.level.height;
    }

    // 创建预览图片元素
    this.previewImg = document.createElement('img');
    this.previewImg.className = 'storyboard-preview-img';
    this.previewImg.style.cssText = `
      width: ${this.config.level.width}px;
      height: ${this.config.level.height}px;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      background: #000;
    `;

    console.log('Storyboard预览元素创建完成');
  }

  /**
   * 根据时间获取缩略图预览
   */
  async getPreviewAtTime(timeInSeconds, videoElement) {
    if (!this.config || !videoElement) {
      return null;
    }

    // 防抖处理
    const now = Date.now();
    if (now - this.lastRequestTime < this.requestDelay) {
      return null;
    }
    
    // 如果是相同的时间点，直接返回null避免重复请求
    const roundedTime = Math.floor(timeInSeconds * 2) / 2; // 0.5秒精度
    if (roundedTime === this.lastTimeRequested) {
      return null;
    }
    
    this.lastRequestTime = now;
    this.lastTimeRequested = roundedTime;

    try {
      const level = this.config.level;
      
      // 计算缩略图索引
      const thumbIndex = Math.floor((timeInSeconds * 1000) / level.duration);
      
      // 确保索引在有效范围内
      if (thumbIndex < 0 || thumbIndex >= level.totalThumbs) {
        return null;
      }

      // 计算在storyboard大图中的位置
      const thumbsPerPage = level.rows * level.cols;
      const pageIndex = Math.floor(thumbIndex / thumbsPerPage);
      const indexInPage = thumbIndex % thumbsPerPage;
      const row = Math.floor(indexInPage / level.cols);
      const col = indexInPage % level.cols;

      // 构建storyboard图片URL
      const storyboardUrl = this.buildStoryboardUrl(pageIndex);
      
      // 获取storyboard图片
      const storyboardImg = await this.loadStoryboardImage(storyboardUrl);
      
      if (!storyboardImg) {
        return null;
      }

      // 裁剪出特定的缩略图
      const thumbnailDataUrl = this.extractThumbnail(storyboardImg, row, col);
      
      return thumbnailDataUrl;
    } catch (error) {
      console.error('获取缩略图预览失败:', error);
      return null;
    }
  }

  /**
   * 构建storyboard图片URL
   */
  buildStoryboardUrl(pageIndex) {
    const config = this.config;
    
    // 固定使用L2分辨率，替换URL模板中的占位符
    let url = config.urlTemplate.replace('L$L/$N', `L2/M${pageIndex}`);
    
    // 添加L2对应的签名
    if (config.level.signature) {
      url += `&sigh=${config.level.signature}`;
    }
    
    console.log('构建的storyboard URL:', url);
    return url;
  }

  /**
   * 加载storyboard图片（带缓存）
   */
  async loadStoryboardImage(url) {
    // 检查缓存
    if (this.cache.has(url)) {
      return this.cache.get(url);
    }

    return new Promise((resolve) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        // 缓存图片
        this.cache.set(url, img);
        resolve(img);
      };
      
      img.onerror = () => {
        console.warn('加载storyboard图片失败:', url);
        resolve(null);
      };
      
      // 设置超时
      setTimeout(() => {
        if (!img.complete) {
          console.warn('加载storyboard图片超时:', url);
          resolve(null);
        }
      }, 5000);
      
      img.src = url;
    });
  }

  /**
   * 从storyboard大图中裁剪出特定的缩略图
   */
  extractThumbnail(storyboardImg, row, col) {
    try {
      const level = this.config.level;
      
      // 清空canvas
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      
      // 计算源区域坐标
      const sourceX = col * level.width;
      const sourceY = row * level.height;
      
      // 裁剪并绘制到canvas
      this.ctx.drawImage(
        storyboardImg,
        sourceX, sourceY, level.width, level.height, // 源区域
        0, 0, level.width, level.height // 目标区域
      );
      
      // 转换为dataURL
      return this.canvas.toDataURL('image/jpeg', 0.8);
    } catch (error) {
      console.error('裁剪缩略图失败:', error);
      return null;
    }
  }

  /**
   * 显示缩略图预览
   */
  async showPreview(thumbnailDataUrl, previewContainer, timeInSeconds = null) {
    if (!thumbnailDataUrl || !previewContainer || !this.previewImg) {
      return;
    }

    // 查找或创建缩略图容器
    let thumbnailContainer = previewContainer.querySelector('.preview-thumbnail-container');
    if (!thumbnailContainer) {
      thumbnailContainer = document.createElement('div');
      thumbnailContainer.className = 'preview-thumbnail-container';
      thumbnailContainer.style.cssText = `
        margin-bottom: 8px;
        display: flex;
        justify-content: center;
      `;
      previewContainer.insertBefore(thumbnailContainer, previewContainer.firstChild);
    }

    // 设置缩略图
    this.previewImg.src = thumbnailDataUrl;

    // 添加到容器（如果还没有）
    if (!thumbnailContainer.contains(this.previewImg)) {
      thumbnailContainer.appendChild(this.previewImg);
    }

    // 显示预览
    thumbnailContainer.style.display = 'flex';

    // 显示章节信息
    if (timeInSeconds !== null) {
      await this.showChapterInfo(previewContainer, timeInSeconds);
    }
  }

  /**
   * 显示章节信息
   */
  async showChapterInfo(previewContainer, timeInSeconds) {
    const chapterElement = previewContainer.querySelector('.preview-chapter');
    if (!chapterElement) {
      return;
    }

    try {
      const chapter = await this.chapterManager.getChapterAtTime(timeInSeconds);

      if (chapter && chapter.title) {
        chapterElement.textContent = chapter.title;
        chapterElement.style.display = 'block';
      } else {
        chapterElement.style.display = 'none';
      }
    } catch (error) {
      console.warn('[Storyboard] 获取章节信息失败:', error);
      chapterElement.style.display = 'none';
    }
  }

  /**
   * 隐藏缩略图预览
   */
  hidePreview(previewContainer) {
    if (!previewContainer) return;

    const thumbnailContainer = previewContainer.querySelector('.preview-thumbnail-container');
    if (thumbnailContainer) {
      thumbnailContainer.style.display = 'none';
    }

    // 隐藏章节信息
    const chapterElement = previewContainer.querySelector('.preview-chapter');
    if (chapterElement) {
      chapterElement.style.display = 'none';
    }
  }

  /**
   * 显示加载状态
   */
  showLoading(previewContainer) {
    // 不显示加载状态，避免闪烁
    // 直接隐藏缩略图容器
    this.hidePreview(previewContainer);
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.cache.clear();
    console.log('Storyboard缓存已清理');
  }

  /**
   * 测试URL构建
   */
  testUrlBuilding() {
    if (!this.config) {
      console.log('配置未初始化，无法测试URL构建');
      return;
    }
    
    console.log('=== URL构建测试 ===');
    console.log('URL模板:', this.config.urlTemplate);
    console.log('Level索引:', this.config.levelIndex);
    console.log('L2签名:', this.config.level.signature);
    
    // 测试前3页的URL
    for (let i = 0; i < 3; i++) {
      const url = this.buildStoryboardUrl(i);
      console.log(`页面 ${i} URL:`, url);
    }
    console.log('==================');
  }





  /**
   * 清理UI元素（退出学习模式时使用，保留配置和缓存）
   */
  cleanupUI() {
    console.log('[Storyboard] 🧹 清理UI元素，保留配置和缓存');

    // 只清理UI相关的元素，保留配置和缓存
    this.canvas = null;
    this.ctx = null;
    this.isLoading = false;
    this.lastRequestTime = 0;
    this.lastTimeRequested = -1;

    // 确保预览图片元素也被移除
    if (this.previewImg && this.previewImg.parentNode) {
      this.previewImg.parentNode.removeChild(this.previewImg);
    }
    this.previewImg = null;
  }

  /**
   * 销毁管理器（完全清理，视频切换时使用）
   */
  destroy() {
    console.log('[Storyboard] 🧹 销毁管理器，清理所有状态');
    this.clearCache();
    this.config = null;
    this.canvas = null;
    this.ctx = null;
    this.isLoading = false;
    this.lastRequestTime = 0;
    this.lastTimeRequested = -1;

    // 清理章节管理器
    if (this.chapterManager) {
      this.chapterManager.clearCache();
    }

    // 确保预览图片元素也被移除
    if (this.previewImg && this.previewImg.parentNode) {
      this.previewImg.parentNode.removeChild(this.previewImg);
    }
    this.previewImg = null;
  }
}

// 创建全局实例
const storyboardManager = new StoryboardPreviewManager();

// 监听 playerResponse 更新事件
window.addEventListener('playerResponseUpdated', async function(event) {
  console.log('[Storyboard] 收到playerResponse更新事件');

  const eventData = event.detail;
  if (!eventData || !eventData.spec) {
    console.warn('[Storyboard] 事件数据无效，跳过');
    return;
  }

  // 获取当前视频ID用于验证
  const currentVideoId = window.StudyModeUtils ? window.StudyModeUtils.extractVideoId(window.location.href) : null;

  // 验证视频ID是否匹配
  if (currentVideoId && eventData.videoId && currentVideoId !== eventData.videoId) {
    console.warn(`[Storyboard] 视频ID不匹配，跳过: 当前=${currentVideoId}, 事件=${eventData.videoId}`);
    return;
  }

  console.log(`[Storyboard] 开始初始化，视频ID: ${eventData.videoId}`);

  // 销毁旧状态并重新初始化
  storyboardManager.destroy();
  const success = await storyboardManager.initializeWithEventData(eventData);

  if (success) {
    console.log('[Storyboard] ✅ 初始化成功');
  } else {
    console.warn('[Storyboard] ❌ 初始化失败');
  }
});

// 导出storyboard预览功能
window.StudyModeStoryboard = {
  // 主要功能
  initialize: async () => {
    console.log('[Storyboard] 尝试手动初始化...');

    // 检查是否有最新的hook数据
    if (window.__latestPlayerResponse && window.__latestPlayerResponse.storyboards) {
      const eventData = {
        playerResponse: window.__latestPlayerResponse,
        storyboards: window.__latestPlayerResponse.storyboards,
        videoId: window.__latestPlayerResponse.videoDetails ? window.__latestPlayerResponse.videoDetails.videoId : null,
        spec: window.__latestPlayerResponse.storyboards.playerStoryboardSpecRenderer?.spec,
        source: 'manual_initialize',
        timestamp: Date.now()
      };

      if (eventData.spec) {
        console.log('[Storyboard] 使用hook数据初始化');
        storyboardManager.destroy();
        return await storyboardManager.initializeWithEventData(eventData);
      }
    }

    console.warn('[Storyboard] 未找到有效数据，等待hook事件...');
    return false;
  },

  getPreviewAtTime: (time, videoElement) => storyboardManager.getPreviewAtTime(time, videoElement),
  showPreview: (thumbnailDataUrl, container, timeInSeconds) => storyboardManager.showPreview(thumbnailDataUrl, container, timeInSeconds),
  hidePreview: (container) => storyboardManager.hidePreview(container),
  showLoading: (container) => storyboardManager.showLoading(container),

  // 配置和状态
  isEnabled: () => isStoryboardEnabled,
  setEnabled: (enabled) => { isStoryboardEnabled = enabled; },
  hasConfig: () => storyboardManager.config !== null,

  // 管理功能
  clearCache: () => storyboardManager.clearCache(),
  cleanupUI: () => storyboardManager.cleanupUI(), // 新增：只清理UI，保留配置
  destroy: () => storyboardManager.destroy(),
  forceRefresh: async () => {
    console.log('[Storyboard] 强制刷新配置');
    storyboardManager.clearCache();
    storyboardManager.config = null;

    // 使用hook数据重新初始化
    if (window.__latestPlayerResponse && window.__latestPlayerResponse.storyboards) {
      const eventData = {
        playerResponse: window.__latestPlayerResponse,
        storyboards: window.__latestPlayerResponse.storyboards,
        videoId: window.__latestPlayerResponse.videoDetails ? window.__latestPlayerResponse.videoDetails.videoId : null,
        spec: window.__latestPlayerResponse.storyboards.playerStoryboardSpecRenderer?.spec,
        source: 'force_refresh',
        timestamp: Date.now()
      };

      if (eventData.spec) {
        return await storyboardManager.initializeWithEventData(eventData);
      }
    }

    console.warn('[Storyboard] 强制刷新失败，未找到有效数据');
    return false;
  },

  // 调试功能
  testUrlBuilding: () => storyboardManager.testUrlBuilding(),
  getCurrentSpec: () => storyboardManager.config ? storyboardManager.config.urlTemplate : null,

  // 内部访问（用于调试）
  _manager: storyboardManager
};