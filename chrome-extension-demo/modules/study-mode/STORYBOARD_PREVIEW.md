# YouTube 缩略图预览功能

## 功能概述

在学习模式下，当用户鼠标悬浮在进度条上时，会显示该时刻的 YouTube 视频缩略图预览，类似于 YouTube 原生的预览功能。

## 技术实现

### 核心组件

1. **study-mode-storyboard.js** - 缩略图预览核心模块
2. **study-mode-controls.js** - 进度条控制和预览集成
3. **study-mode.js** - 主模块集成和生命周期管理

### 工作原理

1. **获取 Storyboard 数据**
   - 从 `ytplayer.config.args.raw_player_response.storyboards` 获取配置
   - 解析 storyboard spec 字符串，提取 URL 模板和参数

2. **计算缩略图位置**
   - 根据悬浮时间计算缩略图索引
   - 计算该缩略图在 storyboard 大图中的行、列、页码

3. **下载和缓存**
   - 构建 storyboard 图片 URL
   - 下载大图并缓存到内存

4. **图片裁剪**
   - 使用 Canvas API 从大图中裁剪出特定的小图
   - 转换为 dataURL 格式

5. **UI 展示**
   - 在进度条预览区域显示缩略图
   - 添加加载状态和错误处理

### Storyboard Spec 格式解析

YouTube 的 storyboard spec 格式示例：
```
https://i.ytimg.com/sb/zsxfVZYNaGk/storyboard3_L$L/$N.jpg?sqp=...|48#27#100#10#10#0#default#rs$AOn4CLAEFV2XMdVjI1jfMZdYGqOTIu487Q|80#45#155#10#10#5000#M$M#rs$AOn4CLD2dYtIzATAHzNEc5eFXXf4YOwcgg|160#90#155#5#5#5000#M$M#rs$AOn4CLA_tLNEW1zwwVEmbpEu5K2XjcH18g|320#180#155#3#3#5000#M$M#rs$AOn4CLBO5HfuhEvl0-c5aQN079UlBE4URA
```

参数解析（以 L2 为例）：
- `80`: 小图宽度 (px)
- `45`: 小图高度 (px)  
- `155`: 总缩略图数量
- `10`: 每张大图的行数
- `10`: 每张大图的列数
- `5000`: 每个缩略图的持续时间 (ms)
- `rs$...`: 签名参数

### 缓存策略

- **图片缓存**: 已下载的 storyboard 大图缓存到 Map 中
- **防抖机制**: 100ms 内的重复请求会被忽略
- **自动清理**: 视频切换时清理旧视频的缓存

## API 接口

### StudyModeStoryboard 模块

```javascript
// 初始化 storyboard 配置
await StudyModeStoryboard.initialize()

// 获取指定时间的缩略图预览
const thumbnailDataUrl = await StudyModeStoryboard.getPreviewAtTime(timeInSeconds, videoElement)

// 显示缩略图预览
StudyModeStoryboard.showPreview(thumbnailDataUrl, previewContainer)

// 隐藏缩略图预览
StudyModeStoryboard.hidePreview(previewContainer)

// 显示加载状态
StudyModeStoryboard.showLoading(previewContainer)

// 清理缓存
StudyModeStoryboard.clearCache()
```

### 配置选项

```javascript
// 启用/禁用缩略图预览
StudyModeStoryboard.setEnabled(true/false)

// 检查是否已配置
StudyModeStoryboard.hasConfig()

// 检查是否启用
StudyModeStoryboard.isEnabled()
```

## 使用方法

1. **进入学习模式**
   - 点击插件面板的"进入学习模式"按钮
   - 系统会自动初始化缩略图预览功能

2. **查看缩略图预览**
   - 将鼠标悬浮在进度条上
   - 系统会显示该时刻的视频缩略图
   - 移动鼠标可以实时预览不同时刻的画面

3. **功能特性**
   - 自动适配不同分辨率的 storyboard
   - 智能缓存，提升加载速度
   - 视频切换时自动重新初始化
   - 支持暗色主题

## 错误处理

- **Storyboard 不可用**: 降级为仅显示时间
- **网络加载失败**: 显示加载失败提示
- **解析错误**: 记录日志并跳过预览
- **Canvas 错误**: 静默处理，不影响其他功能

## 兼容性

- 支持所有现代浏览器（Chrome, Firefox, Safari, Edge）
- 兼容不同的 YouTube 页面结构
- 适配移动端和桌面端
- 支持 YouTube 的各种视频格式

## 性能优化

- **防抖处理**: 避免频繁的预览请求
- **图片缓存**: 减少重复下载
- **异步加载**: 不阻塞 UI 操作
- **内存管理**: 视频切换时清理缓存
- **错误恢复**: 失败时不影响其他功能

## 调试信息

开启浏览器控制台可以看到详细的调试信息：
- Storyboard 配置解析过程
- 缩略图计算和加载状态
- 缓存命中情况
- 错误和警告信息

## 已知限制

1. 依赖 YouTube 的 storyboard 数据，某些视频可能不提供
2. 需要 CORS 支持，某些网络环境可能受限
3. 缓存大小会随使用时间增长，但会在视频切换时清理
4. 首次加载可能有轻微延迟

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始实现缩略图预览功能
- 支持 storyboard spec 解析
- 实现图片下载和缓存
- 添加 Canvas 裁剪功能
- 集成到进度条预览系统 