/* 词组悬停tooltip样式 */
#extension-word-tooltip {
    position: absolute;
    min-width: 80px; /* 增加最小宽度 */
    max-width: 160px; /* 增加最大宽度 */
    font-size: 1.2rem; /* 显著增大字体大小 */
    transition: opacity 0.2s ease, transform 0.2s ease; /* 过渡效果 */
    border-radius: 8px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    padding: 4px 8px; /* 缩小内边距 */
    background-color: white;
    opacity: 0.95; /* 显式设置不透明度为95% */
    z-index: 10005;
    display: flex; /* 改为flex布局 */
    flex-direction: row; /* 水平排列 */
    align-items: center; /* 垂直居中对齐 */
    justify-content: space-between; /* 两端对齐，图标会在最右边 */
    text-align: left; /* 文本左对齐 */
    border: 1px solid #e5e7eb;
    pointer-events: auto; /* 确保tooltip可以接收鼠标事件 */
    height: auto; /* 自适应高度 */
    line-height: 1.2; /* 减小行高 */
}

#extension-tooltip-text {
    margin-bottom: 0; /* 移除之前的下边距 */
    margin-right: 10px; /* 在文本和图标之间添加更多的右边距 */
    font-weight: 500;
    font-size: 16px; /* 显式设置字体大小 */
    line-height: 1.4; /* 增加行高以提高可读性 */
    white-space: normal; /* 允许文本换行 */
    word-break: break-all; /* 强制长单词换行 */
}

#extension-favorite-icon {
    transition: color 0.2s ease, transform 0.2s ease; /* 添加transform过渡 */
    margin-top: 0; /* 移除之前的上边距 */
    cursor: pointer;
    font-size: 1.1rem;
    color: #9ca3af;
    flex-shrink: 0; /* 防止图标被压缩 */
}

#extension-favorite-icon.fas.text-red-500 {
    color: #ef4444 !important;
    animation: heartBeat 0.3s ease-in-out;
}

.hidden {
    display: none !important;
}

@keyframes heartBeat {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

/* 黑暗模式下的tooltip样式 */
body.dark-mode #extension-word-tooltip {
    background-color: #1f2937;
    border-color: #374151;
    color: #e5e7eb;
}

body.dark-mode #extension-tooltip-text {
    color: #e5e7eb;
}
