# YouTube热力图功能文档

## 功能概述

为LingTube学习模式的进度条添加了YouTube观众观看热力图显示功能，当用户鼠标悬浮在进度条上时，会在进度条上方显示YouTube的"most replayed"热力图，帮助用户了解视频中最受欢迎的片段。

## 功能特性

### 1. 自动数据获取
- 从YouTube页面自动抓取热力图数据（`.ytp-heat-map-svg`元素）
- 数据缓存机制，避免重复获取
- 支持完整的SVG热力图，包括渐变、路径等所有视觉元素

### 2. 智能显示控制
- **鼠标悬浮显示**：当鼠标悬浮在进度条上时自动显示热力图
- **移开消失**：鼠标离开进度条时自动隐藏热力图
- **平滑过渡**：支持透明度过渡动画，提供流畅的用户体验

### 3. 布局设计
- **位置优化**：热力图显示在进度条正上方，不与缩略图预览重叠
- **层级管理**：合理的z-index设置，确保显示层级正确
- **响应式设计**：热力图宽度自动适配进度条宽度

### 4. 视觉效果
- **半透明背景**：热力图容器具有半透明背景和模糊效果
- **暗色主题支持**：针对暗色主题优化了显示效果
- **圆角设计**：与整体UI风格保持一致

## 技术实现

### 核心文件修改

1. **study-mode-controls.js**
   - 添加了热力图相关的全局变量和函数
   - 集成到现有的进度条事件处理逻辑中
   - 提供了完整的生命周期管理

2. **study-mode.css**
   - 添加了热力图容器和内容的样式定义
   - 确保与现有UI元素的兼容性
   - 支持暗色主题

### 关键函数

```javascript
// 获取YouTube热力图数据
getYouTubeHeatMapData()

// 显示热力图
showHeatMap()

// 隐藏热力图
hideHeatMap()
```

### HTML结构

```html
<div class="progress-heatmap-container" id="progress-heatmap">
  <div class="heatmap-content"></div>
</div>
```

## 使用方式

### 用户操作
1. 在YouTube视频页面启动LingTube学习模式
2. 将鼠标悬浮在进度条上
3. 自动显示热力图，显示观众观看热度分布
4. 鼠标移开时热力图自动隐藏

### 开发者集成
热力图功能已完全集成到现有的学习模式控制模块中，无需额外配置。功能会在以下情况下自动工作：

- 学习模式激活时
- YouTube页面存在热力图数据时
- 用户与进度条交互时

## 兼容性

### 浏览器支持
- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

### YouTube页面兼容性
- 支持所有包含热力图数据的YouTube视频
- 对于没有热力图数据的视频，功能会静默跳过
- 不影响现有的缩略图预览和进度条功能

## 错误处理

### 数据获取失败
- 当无法找到YouTube热力图数据时，功能会静默跳过
- 不会影响其他学习模式功能的正常使用
- 在控制台提供调试信息

### 显示异常
- 包含完整的错误捕获和处理机制
- 确保即使出现异常也不会影响用户体验
- 提供降级方案

## 测试

### 测试文件
- `test-heatmap.html` - 独立的功能测试页面
- 包含模拟YouTube热力图数据
- 提供交互式测试界面

### 测试用例
1. 热力图数据获取测试
2. 显示/隐藏功能测试
3. 与现有功能的兼容性测试
4. 错误处理测试

## 性能优化

### 数据缓存
- 热力图数据只获取一次，后续使用缓存
- 避免重复的DOM查询和克隆操作

### 事件优化
- 复用现有的鼠标事件处理机制
- 最小化DOM操作次数

### 内存管理
- 在学习模式清理时自动清理热力图缓存
- 避免内存泄漏

## 未来扩展

### 可能的增强功能
1. **热力图交互**：点击热力图跳转到对应时间点
2. **自定义样式**：允许用户自定义热力图显示样式
3. **数据分析**：基于热力图数据提供学习建议
4. **多语言支持**：热力图相关提示文本的国际化

### API扩展
```javascript
// 未来可能的API扩展
StudyModeControls.setHeatMapStyle(customStyles)
StudyModeControls.onHeatMapClick(callback)
StudyModeControls.getHeatMapAnalytics()
```

## 更新日志

### v1.3.0 (2024-01-XX)
- 🎯 **重大更新**：支持YouTube章节视频的热力图
- ✅ 自动检测视频是否包含章节结构
- ✅ 解析所有章节的热力图数据并合并
- ✅ 正确映射章节时间到全局时间轴
- ✅ 增强调试功能，支持章节数据分析
- ✅ 兼容无章节视频的原有功能

### v1.2.0 (2024-01-XX)
- 🔧 修复数据解析逻辑，正确识别x坐标为5的倍数的点
- ✅ 移除柱状图，只保留平滑曲线和填充区域
- ✅ 移除灰色背景，使热力图容器透明
- ✅ 改进时间坐标计算公式：(x-5)/995
- ✅ 增加解析的数据点数量，提升热力图精度
- ✅ 添加测试函数验证解析逻辑

### v1.1.0 (2024-01-XX)
- 🔧 修复热力图数据解析问题
- ✅ 实现正确的SVG路径解析算法
- ✅ 根据GitHub规则提取有效数据点
- ✅ 改进热力图渲染，使用条形图和平滑曲线
- ✅ 添加调试功能，便于问题排查
- ✅ 优化热力图视觉效果和透明度

### v1.0.0 (2024-01-XX)
- ✅ 基础热力图显示功能
- ✅ 鼠标悬浮交互
- ✅ 与缩略图预览的布局兼容
- ✅ 暗色主题支持
- ✅ 错误处理和降级方案
- ✅ 测试页面和文档

## 技术细节

### 数据解析算法

根据GitHub社区的研究，YouTube热力图数据的解析规则如下：

1. **SVG路径格式**：YouTube使用贝塞尔曲线定义热力图轮廓
2. **数据提取规则**：
   - 每个`C`命令后跟着3对坐标（6个数字）
   - 第3对坐标中，x坐标以`.5`或`5.0`结尾的点是有效数据点
   - x坐标表示时间百分比：`time = x / 1000`
   - y坐标表示热度值：`heat = (100 - y) / 100`

3. **降级策略**：
   - 如果严格规则解析的点数太少，使用采样策略
   - 从所有坐标点中均匀采样20个点作为热力图数据

### 渲染策略

1. **条形图渲染**：使用矩形条显示各时间段的热度
2. **平滑曲线**：添加连接线和边界线增强视觉效果
3. **透明度映射**：根据热度值动态调整透明度
4. **响应式设计**：自动适配不同的数据点密度

### 章节视频支持

#### 检测机制
- 自动检测页面中是否存在 `.ytp-heat-map-chapter` 元素
- 如果检测到章节，使用章节解析模式
- 如果未检测到章节，使用单一热力图解析模式

#### 章节数据处理
1. **位置解析**：从每个章节的 `style` 属性中提取 `width` 和 `left` 值
2. **时间映射**：
   ```javascript
   // 计算章节在全局时间轴中的位置
   chapterStartTime = chapterLeft / totalWidth
   chapterEndTime = (chapterLeft + chapterWidth) / totalWidth
   
   // 将章节内相对时间转换为全局时间
   globalTime = chapterStartTime + (localTime * chapterDuration)
   ```
3. **数据合并**：收集所有章节的数据点，按时间排序
4. **统一渲染**：将合并后的数据点创建为单一的热力图SVG

#### 章节示例
对于包含多个章节的视频：
- 章节1：`width: 24px; left: 0px;` → 时间范围 0% - 3.9%
- 章节2：`width: 20px; left: 26px;` → 时间范围 4.2% - 7.5%
- 章节3：`width: 28px; left: 48px;` → 时间范围 7.8% - 12.3%
- ...以此类推

#### 调试支持
使用 `StudyModeControls.debugHeatMapData()` 可以查看：
- 章节检测结果
- 每个章节的位置和数据点数量
- 合并后的总数据点统计
- 章节时间映射详情

---

*该功能完全兼容现有的LingTube学习模式，为用户提供更丰富的视频观看体验。* 