/* Study Mode Danmaku Styles - 弹幕样式 */

/* 弹幕容器 */
.study-danmaku-container {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: none !important;
    overflow: hidden !important;
    z-index: 999 !important;
    background: transparent !important;
}

/* 弹幕控制面板 */
.study-danmaku-panel {
    /* 默认白天模式：白色背景，无边框 */
    background: rgba(255, 255, 255, 0.85);
    border-radius: 8px;
    padding: 8px 12px;
    margin: 15px 0 0 0;
    border: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    position: relative;
    z-index: 1001;
    backdrop-filter: blur(5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.danmaku-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

/* 弹幕开关 */
.study-danmaku-switch {
    display: inline-block;
    position: relative;
}

.study-danmaku-switch-input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.study-danmaku-switch-label {
    display: inline-block;
    width: 36px;
    height: 36px;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.2s ease;
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.study-danmaku-switch-label:hover {
    background: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0.15);
}

.study-danmaku-switch-on,
.study-danmaku-switch-off {
    position: absolute;
    width: 20px;
    height: 20px;
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.study-danmaku-switch-on svg,
.study-danmaku-switch-off svg {
    width: 100%;
    height: 100%;
    fill: rgba(0, 0, 0, 0.7);
}

/* 默认状态（未选中）- 显示关闭图标 */
.study-danmaku-switch-input:not(:checked) + .study-danmaku-switch-label .study-danmaku-switch-on {
    opacity: 0;
    transform: scale(0.8);
}

.study-danmaku-switch-input:not(:checked) + .study-danmaku-switch-label .study-danmaku-switch-off {
    opacity: 1;
    transform: scale(1);
}

/* 选中状态 - 显示开启图标 */
.study-danmaku-switch-input:checked + .study-danmaku-switch-label .study-danmaku-switch-on {
    opacity: 1;
    transform: scale(1);
}

.study-danmaku-switch-input:checked + .study-danmaku-switch-label .study-danmaku-switch-off {
    opacity: 0;
    transform: scale(0.8);
}

/* 选中状态的特殊颜色 */
.study-danmaku-switch-input:checked + .study-danmaku-switch-label .study-danmaku-switch-on svg {
    fill: #7dd3fc;
}

/* 焦点状态 */
.study-danmaku-switch-input:focus-visible + .study-danmaku-switch-label {
    outline: 2px solid #7dd3fc;
    outline-offset: 2px;
}

/* 输入组 */
.danmaku-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
    max-width: none;
}

/* 弹幕输入框 */
.danmaku-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.8);
    color: #333;
    font-size: 14px;
    font-family: inherit;
    outline: none;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    min-width: 300px;
    height: 36px;
    box-sizing: border-box;
}

.danmaku-input::placeholder {
    color: rgba(0, 0, 0, 0.5);
}

.danmaku-input:focus {
    border-color: #7dd3fc;
    box-shadow: 0 0 0 3px rgba(125, 211, 252, 0.1);
}

.danmaku-input:disabled {
    background: rgba(0, 0, 0, 0.05);
    color: rgba(0, 0, 0, 0.4);
    cursor: not-allowed;
}



/* 发送按钮 */
.danmaku-send-btn {
    background: #93c5fd;
    border: none;
    border-radius: 6px;
    padding: 8px 20px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    flex-shrink: 0;
    min-width: 60px;
}

.danmaku-send-btn:hover {
    background: #7dd3fc;
}

.danmaku-send-btn:disabled {
    background: #94a3b8;
    cursor: not-allowed;
}

.danmaku-send-btn:active {
    background: #60a5fa;
}



/* 弹幕项样式 */
.study-danmaku-item {
    position: absolute !important;
    white-space: nowrap !important;
    font-size: 16px !important;
    font-weight: bold !important;
    font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif !important;
    text-shadow: 
        1px 1px 2px rgba(0, 0, 0, 0.9),
        -1px -1px 2px rgba(0, 0, 0, 0.9),
        1px -1px 2px rgba(0, 0, 0, 0.9),
        -1px 1px 2px rgba(0, 0, 0, 0.9) !important;
    pointer-events: none !important;
    user-select: none !important;
    z-index: 1000 !important;
    line-height: 1.2 !important;
}

@keyframes danmaku-scroll {
    from {
        transform: translateX(100vw);
    }
    to {
        transform: translateX(calc(-100% - 30px));
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .study-danmaku-panel {
        padding: 8px 12px;
        margin: 15px 0 0 0;
    }
    
    .danmaku-controls {
        gap: 10px;
    }
    
    .study-danmaku-switch-label {
        width: 32px;
        height: 32px;
    }
    
    .study-danmaku-switch-on,
    .study-danmaku-switch-off {
        width: 18px;
        height: 18px;
    }
    
    .danmaku-input-group {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .danmaku-input {
        min-width: 250px;
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    .danmaku-color-select {
        min-width: auto;
    }
    
    .study-danmaku-item {
        font-size: 14px !important;
    }
}

@media (max-width: 480px) {
    .danmaku-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .danmaku-input-group {
        order: 2;
    }
    
    .danmaku-input {
        min-width: auto;
    }
    
    .danmaku-stats {
        order: 3;
        text-align: center;
    }
    
    .study-danmaku-item {
        font-size: 12px !important;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .study-danmaku-panel {
        background: #000;
        border: 2px solid #fff;
    }
    
    .danmaku-input,
    .danmaku-color-select {
        background: #000;
        border: 2px solid #fff;
        color: #fff;
    }
    
    .study-danmaku-item {
        text-shadow: 
            2px 2px 4px #000,
            -2px -2px 4px #000,
            2px -2px 4px #000,
            -2px 2px 4px #000 !important;
    }
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
    .study-danmaku-switch-label,
    .study-danmaku-switch-on,
    .study-danmaku-switch-off,
    .danmaku-send-btn,
    .danmaku-input,
    .danmaku-color-select {
        transition: none;
    }
    
    .danmaku-send-btn:hover {
        transform: none;
    }
    
    .study-danmaku-item {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
    }
}

/* 黑暗模式样式 - 与播放器容器保持一致 */
body.dark-mode .study-danmaku-panel {
    background: rgba(26, 32, 44, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
}

body.dark-mode .danmaku-input {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.15);
    color: #e5e7eb;
}

body.dark-mode .danmaku-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

body.dark-mode .danmaku-input:focus {
    border-color: #7dd3fc;
    box-shadow: 0 0 0 3px rgba(125, 211, 252, 0.2);
}

body.dark-mode .danmaku-input:disabled {
    background: rgba(255, 255, 255, 0.02);
    color: rgba(255, 255, 255, 0.3);
}

body.dark-mode .study-danmaku-switch-label {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .study-danmaku-switch-label:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.15);
}

body.dark-mode .study-danmaku-switch-on svg,
body.dark-mode .study-danmaku-switch-off svg {
    fill: rgba(255, 255, 255, 0.8);
}

body.dark-mode .danmaku-input-counter {
    color: rgba(255, 255, 255, 0.5);
}

/* 黑暗模式下的发送按钮 */
body.dark-mode .danmaku-send-btn {
    background: #4b5563;
    color: #e5e7eb;
}

body.dark-mode .danmaku-send-btn:hover {
    background: #6b7280;
}

body.dark-mode .danmaku-send-btn:active {
    background: #9ca3af;
}

body.dark-mode .danmaku-send-btn:disabled {
    background: #1f2937;
    color: rgba(255, 255, 255, 0.3);
}

/* 加载状态 */
.danmaku-loading {
    position: relative;
    overflow: hidden;
}

.danmaku-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 错误状态 */
.danmaku-error {
    border-color: #ff4757 !important;
    box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1) !important;
}

/* 成功状态 */
.danmaku-success {
    border-color: #2ed573 !important;
    box-shadow: 0 0 0 3px rgba(46, 213, 115, 0.1) !important;
}

/* 焦点可见性增强 */
.danmaku-send-btn:focus-visible {
    outline: 2px solid #7dd3fc;
    outline-offset: 2px;
}

.danmaku-input:focus-visible,
.danmaku-color-select:focus-visible {
    outline: 2px solid #7dd3fc;
    outline-offset: -2px;
}

/* 按钮图标动画 */
.danmaku-send-btn i {
    transition: transform 0.2s ease;
}

.danmaku-send-btn:hover i {
    transform: translateX(2px);
}

/* 输入计数器 */
.danmaku-input-counter {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 11px;
    color: rgba(0, 0, 0, 0.5);
    pointer-events: none;
}

.danmaku-input-counter.warning {
    color: #ffaa00;
}

.danmaku-input-counter.danger {
    color: #ff4757;
}

/* 弹幕密度控制 */
.danmaku-density-low .study-danmaku-item:nth-child(3n) {
    display: none !important;
}

.danmaku-density-medium .study-danmaku-item:nth-child(2n) {
    display: none !important;
}

.danmaku-density-high .study-danmaku-item {
    display: block !important;
} 