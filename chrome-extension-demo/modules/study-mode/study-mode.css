/* Study Mode Styles */

/* 缩略图预览样式 */
.preview-thumbnail-container {
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 11001; /* 确保缩略图容器在最上层 */
}

.storyboard-preview-img {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  background: #000;
  object-fit: cover;
  transition: opacity 0.2s ease;
}

.preview-loading {
  background: #333;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  animation: loadingPulse 1.5s ease-in-out infinite;
}

@keyframes loadingPulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 进度条预览区域样式增强 */
.progress-preview {
  position: absolute;
  bottom: 100%;
  margin-bottom: 10px;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  pointer-events: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transform: translateX(-50%);
  z-index: 10004; /* 确保在所有元素之上 */
  min-width: 160px; /* 增加最小宽度，适应缩略图 */
  text-align: center;
}

.progress-preview::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.85);
  z-index: 10005; /* 确保在所有元素之上 */
}

/* 暗色主题下的缩略图预览样式 */
.dark-mode .preview-loading {
  background: #555;
  color: #ddd;
}

.dark-mode .progress-preview {
  background: rgba(40, 40, 40, 0.9);
  border-color: rgba(255, 255, 255, 0.15);
}

.dark-mode .progress-preview::after {
  border-top-color: rgba(40, 40, 40, 0.9);
}

/* YouTube热力图样式 */
.progress-heatmap-container {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  height: 25px;
  margin-bottom: 5px;
  display: none;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10002; /* 在进度条上方，但在缩略图预览下方 */
  pointer-events: none;
}

.heatmap-content {
  width: 100%;
  height: 100%;
  border-radius: 3px;
  overflow: hidden;
  background: transparent;
}

.study-mode-heatmap-svg {
  width: 100%;
  height: 100%;
  display: block;
  opacity: 0.8;
}

/* 确保热力图SVG内的元素正确显示 */
.study-mode-heatmap-svg rect {
  transition: opacity 0.2s ease;
}

.study-mode-heatmap-svg path {
  transition: opacity 0.2s ease;
}

/* 热力图容器悬浮效果 */
.progress-heatmap-container:hover .study-mode-heatmap-svg {
  opacity: 1;
}

/* 确保热力图不与缩略图预览重叠 */
.progress-preview {
  margin-bottom: 40px !important; /* 增加底部边距，为热力图留出空间 */
}

/* 暗色主题下的热力图样式 */
.dark-mode .heatmap-content {
  background: transparent;
}

.dark-mode .study-mode-heatmap-svg {
  opacity: 0.7;
}

#study-mode-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #e0e7ff, #dbeafe, #ede9fe); /* 调整渐变角度，更现代 */
  z-index: 9999;
  display: none;
  overflow: auto;
  padding: 20px 0;
  animation: fadeInBackground 0.6s ease-out; /* 添加渐入动画 */
}

@keyframes fadeInBackground {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 退出学习模式按钮 */
/* 播放器容器样式 - 恢复原始布局 */
.study-mode-player-container {
  max-width: 800px;
  width: 80%;
  margin: 30px auto 10px; /* 减少顶部边距，为弹幕面板留出空间 */
  background: rgba(255, 255, 255, 0.85);
  border-radius: 1.5rem;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15), 0 5px 15px rgba(0, 0, 0, 0.07); /* 增强阴影 */
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.7);
  padding: 1.5rem;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1); /* 更平滑的过渡 */
  position: relative;
  z-index: 10000;
  transform: translateY(0); /* 为动画准备 */
  animation: floatIn 0.7s cubic-bezier(0.23, 1, 0.32, 1); /* 添加浮入动画 */
  overflow: visible; /* 允许弹幕面板显示 */
}

/* 全屏模式下的播放器容器样式 */
:fullscreen .study-mode-player-container,
:-webkit-full-screen .study-mode-player-container,
:-moz-full-screen .study-mode-player-container,
:-ms-fullscreen .study-mode-player-container {
  width: 85% !important;
  max-width: 1000px !important;
  margin: 25px auto 100px !important; /* 增加下边距到100px，确保与字幕区域有50px间距 */
  transform: scale(1.05) !important;
  transition: all 0.3s ease !important;
}

/* 全屏按钮图标样式 */
#fullscreen-btn .enter-fullscreen-icon,
#fullscreen-btn .exit-fullscreen-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: opacity 0.2s ease;
}

#fullscreen-btn .exit-fullscreen-icon {
  display: none;
  opacity: 0;
}

#fullscreen-btn .enter-fullscreen-icon {
  display: block;
  opacity: 1;
}

/* 确保全屏按钮容器有相对定位 */
#fullscreen-btn {
  position: relative;
}

/* 聊天区域容器 - 固定定位在右上角，与播放器对齐 */
.study-mode-chat-container {
  position: fixed;
  top: 50px; /* 向上移动20px，与播放器和字幕区域保持协调 */
  right: calc(10vw - 20px); /* 向右移动50px，原来是-70px，现在是-20px */
  width: 325px;
  height: auto;
  min-height: 400px;
  /* max-height 将通过JavaScript动态设置 */
  background: rgba(255, 255, 255, 0.85); /* 保持与字幕区域一致的背景 */
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15), 0 5px 15px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 10002;
}

/* 添加与字幕区域一致的紫色光效边框 */
.study-mode-chat-container::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  z-index: -1;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(79, 70, 229, 0.2)); /* 与字幕区域相同的紫色渐变 */
  border-radius: 1.7rem;
  opacity: 0.6;
  filter: blur(10px);
}



/* 隐藏聊天区域头部 */
.chat-header {
  display: none;
}

.chat-title {
  display: none;
}

.chat-title::before {
  display: none;
}

/* 聊天消息区域 - 调整padding以补偿移除的header */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px 25px; /* 左右内边距增加到25px，增加呼吸感 */
  display: flex;
  flex-direction: column;
  gap: 16px;
  scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 聊天消息样式 */
.chat-message {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  animation: fadeInMessage 0.3s ease-out forwards;
}

@keyframes fadeInMessage {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes floatIn {
  from { 
    opacity: 0;
    transform: translateY(30px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-message.ai {
  /* 移除横向偏移 */
}

.chat-message.user {
  flex-direction: row-reverse;
  /* 移除横向偏移 */
}

/* 头像样式 - 修改为灰色背景，黑色文字 */
.chat-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 16px;
  font-weight: 500;
  color: #4b5563; /* 文字颜色改为深灰色 */
  position: relative;
}

.chat-avatar.ai {
  background: #e5e7eb; /* AI头像背景改为浅灰色 */
  box-shadow: 0 4px 12px rgba(229, 231, 235, 0.3); /* 调整阴影颜色 */
}

.chat-avatar.user {
  background: #e5e7eb; /* 用户头像背景改为浅灰色 */
  box-shadow: 0 4px 12px rgba(229, 231, 235, 0.3); /* 调整阴影颜色 */
}

.chat-avatar::after {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: inherit;
  z-index: -1;
  filter: blur(8px);
  opacity: 0.4;
}

/* 消息气泡样式 - 修改圆角和颜色 */
.chat-bubble {
  max-width: 225px; /* 再缩窄30px */
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.5;
  position: relative;
  word-wrap: break-word;
}

.chat-message.ai .chat-bubble {
  background: #dbeafe; /* 浅蓝色 */
  border: none; /* 取消边框 */
  color: #000000; /* 黑色文字 */
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  border-radius: 4px 18px 18px 18px; /* 左上角小圆角，其他大圆角 */
}

.chat-message.ai .chat-bubble:hover .copy-button {
  opacity: 1;
}

.chat-message.user .chat-bubble {
  background: #3b82f6; /* 蓝色 */
  border: none; /* 取消边框 */
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
  border-radius: 18px 4px 18px 18px; /* 右上角小圆角，其他大圆角 */
}

/* 聊天输入区域 - 调整样式以适应移除的header */
.chat-input-area {
  padding: 14px 20px 18px;
  border-top: 1px solid rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.6);
  border-radius: 0 0 1.5rem 1.5rem;
}

.chat-input-container {
  display: flex;
  gap: 8px; /* 减小间距以适应内嵌按钮 */
  align-items: flex-end;
  position: relative;
}

.chat-input {
  flex: 1;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  padding: 12px 50px 12px 16px; /* 右侧留出空间给发送按钮 */
  font-size: 14px;
  resize: none;
  outline: none;
  min-height: 20px;
  max-height: 100px;
  transition: all 0.2s ease;
  font-family: inherit;
}

.chat-input:focus {
  border-color: #9ca3af; /* 灰色边框 */
  box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.1);
}

.chat-input::placeholder {
  color: #64748b;
}

.chat-send-btn {
  position: absolute;
  right: 6px;
  bottom: 6px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: #9ca3af; /* 灰色 */
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.chat-send-btn:hover {
  background: #6b7280; /* 深一点的灰色 */
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(156, 163, 175, 0.4);
}

.chat-send-btn:active {
  transform: scale(0.95);
}

.chat-send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}



/* 播放器样式 */
.study-mode-player-wrapper {
  position: relative;
  padding-top: 56.25%; /* 16:9 宽高比 */
  overflow: hidden;
  border-radius: 0.8rem; /* 圆角播放器 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* 轻微阴影 */
  transition: transform 0.3s ease;
}

.study-mode-player-wrapper:hover {
  transform: scale(1.01); /* 悬停时轻微放大 */
}

/* 确保播放器完整显示 */
.in-study-mode {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  transform: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  overflow: visible !important;
  object-fit: contain !important;
  max-width: 100% !important;
  max-height: 100% !important;
  min-width: 100% !important;
  min-height: 100% !important;
  border-radius: 0.8rem !important; /* 保持圆角 */
}

/* 确保播放器内部元素也能完整显示 */
.in-study-mode video,
.in-study-mode iframe,
.in-study-mode .html5-video-container,
.in-study-mode .html5-video-player,
.in-study-mode .ytp-chrome-bottom,
.in-study-mode .ytp-chrome-top {
  width: 100% !important;
  height: 100% !important;
  transform: none !important;
  position: relative !important;
  object-fit: contain !important;
  max-width: 100% !important;
  max-height: 100% !important;
}

#exit-study-mode {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(30, 41, 59, 0.8); /* 更深的颜色 */
  color: white;
  border: none;
  border-radius: 12px; /* 更大的圆角 */
  padding: 10px 18px;
  font-size: 14px;
  font-weight: 500;
  font-family: 'Roboto', Arial, sans-serif;
  cursor: pointer;
  z-index: 10002; /* 确保在播放器之上 */
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* 弹性过渡 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px); /* 玻璃效果 */
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.5px;
  animation: slideInFromTop 0.5s ease; /* 从顶部滑入 */
}

@keyframes slideInFromTop {
  from { 
    opacity: 0;
    transform: translateY(-20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

#exit-study-mode i {
  margin-right: 8px;
  font-size: 16px;
  transition: transform 0.3s ease;
}

#exit-study-mode:hover {
  background: rgba(30, 41, 59, 0.9);
  transform: translateY(-2px) scale(1.03);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
}

#exit-study-mode:hover i {
  transform: rotate(90deg); /* 图标旋转效果 */
}

#exit-study-mode:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

#study-mode-notification {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(15, 23, 42, 0.85); /* 深色背景 */
  color: white;
  padding: 14px 24px;
  border-radius: 12px;
  font-family: 'Roboto', Arial, sans-serif;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  z-index: 10010; /* 增大z-index，确保显示在字幕区域上方 */
  display: none;
  text-align: center;
  min-width: 280px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: slideUpNotification 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* 从底部滑入 */
}

@keyframes slideUpNotification {
  from { 
    opacity: 0;
    transform: translate(-50%, 20px);
  }
  to { 
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

.notification-title {
  font-weight: 600; /* 更粗的字体 */
  font-size: 16px;
  margin-bottom: 6px;
  color: #ffffff;
  letter-spacing: 0.3px;
}

.notification-message {
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.5;
}

/* Study mode button in popup */
.study-mode-button {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background: linear-gradient(135deg, #4285f4, #3367d6); /* 渐变背景 */
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-family: 'Roboto', Arial, sans-serif;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-top: 12px;
  box-shadow: 0 4px 10px rgba(66, 133, 244, 0.25); /* 添加阴影 */
  position: relative;
  overflow: hidden; /* 用于光效 */
}

.study-mode-button::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.3), rgba(255,255,255,0));
  transform: rotate(30deg);
  animation: shimmer 3s infinite; /* 光效动画 */
  opacity: 0;
  transition: opacity 0.3s ease;
}

.study-mode-button:hover::after {
  opacity: 1;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) rotate(30deg); }
  100% { transform: translateX(100%) rotate(30deg); }
}

.study-mode-button:hover {
  background: linear-gradient(135deg, #3367d6, #2850a7);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(66, 133, 244, 0.35);
}

.study-mode-button i {
  margin-right: 10px;
  font-size: 16px;
  transition: transform 0.3s ease;
}

.study-mode-button:hover i {
  transform: scale(1.2);
}

/* Active state for the button */
.study-mode-button.active {
  background: linear-gradient(135deg, #34a853, #2d9249);
  box-shadow: 0 4px 10px rgba(52, 168, 83, 0.25);
}

.study-mode-button.active:hover {
  background: linear-gradient(135deg, #2d9249, #1e8e3e);
  box-shadow: 0 6px 15px rgba(52, 168, 83, 0.35);
}

/* Extension Subtitle Container Styles */
/* 字幕区域样式 - 参考player页面布局 */
#extension-subtitle-container {
  position: fixed;
  bottom: 50px; /* 从60px改为50px，向下移动10像素 */
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  max-width: 800px;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(15px);
  border-radius: 16px; /* 更大的圆角 */
  padding: 20px;
  z-index: 10001;
  color: #333;
  font-family: 'Microsoft YaHei', 'Hiragino Sans GB', 'WenQuanYi Micro Hei', sans-serif;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15), 0 5px 15px rgba(0, 0, 0, 0.08); /* 更强的阴影 */
  border: 1px solid rgba(255, 255, 255, 0.7);
  animation: fadeInUp 0.5s cubic-bezier(0.23, 1, 0.32, 1); /* 动画效果 */
  transition: all 0.3s ease;
}

@keyframes fadeInUp {
  from { 
    opacity: 0;
    transform: translate(-50%, 30px);
  }
  to { 
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

/* 光效边框 */
#extension-subtitle-container::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  z-index: -1;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(79, 70, 229, 0.2)); /* 蓝色到紫色的渐变，更柔和 */
  border-radius: 18px;
  opacity: 0.6;
  filter: blur(10px);
}

/* Loading Hint Styles */
.subtitle-loading-hint {
  display: flex;
  align-items: center;
  justify-content: flex-start; /* 改为左对齐，避免居中造成的空间浪费 */
  padding: 8px 12px !important; /* 从6px 8px适当增加到8px 12px */
  background: transparent !important; /* 移除背景颜色 */
  border-radius: 12px;
  margin: 0 0 10px 0 !important; /* 从8px增加到10px */
  border: none !important; /* 移除边框 */
  transition: all 0.3s ease;
  min-height: auto !important; /* 确保最小高度自适应 */
}

/* 字幕loading专用样式，避免与聊天loading冲突 */
.subtitle-loading-hint .loading-content {
  display: flex !important;
  align-items: center !important; /* 确保垂直居中对齐 */
  justify-content: flex-start !important; /* 左对齐 */
  gap: 10px !important; /* 从8px增加到10px，给图标和文字更多间距 */
  padding: 0 !important; /* 清除可能的padding干扰 */
  flex-direction: row !important; /* 确保水平排列：动画在前，文字在后 */
}

.subtitle-loading-hint .loading-spinner {
  width: 14px !important; /* 保持14px */
  height: 14px !important; /* 保持14px */
  border: 1.5px solid rgba(59, 130, 246, 0.2) !important;
  border-top: 1.5px solid #3b82f6 !important;
  border-radius: 50%;
  animation: spin 1.2s cubic-bezier(0.5, 0.1, 0.5, 1) infinite;
  flex-shrink: 0 !important; /* 防止图标被压缩 */
  margin: 0 !important; /* 清除可能的margin干扰 */
}

/* 成功图标样式 */
.subtitle-loading-hint .success-icon {
  width: 16px !important;
  height: 16px !important;
  background-color: #10b981 !important; /* 绿色背景 */
  color: white !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 12px !important;
  font-weight: bold !important;
  flex-shrink: 0 !important;
  animation: successPulse 0.6s ease-out !important;
}

@keyframes successPulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.subtitle-loading-hint .loading-text {
  color: #3b82f6;
  font-size: 15px !important; /* 从13px增加到15px，提高可读性 */
  font-weight: 500;
  letter-spacing: 0.3px;
  line-height: 1.4 !important; /* 从1.2增加到1.4，让文字更舒适 */
  margin: 0 !important; /* 确保没有额外的margin */
  padding: 0 !important; /* 清除可能的padding干扰 */
}

/* Subtitle Content Wrapper */
.subtitle-content-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Subtitle Display Styles */
.subtitle-display {
  width: 100%;
  transition: all 0.3s ease;
}

.subtitle-ja {
  font-size: 20px; /* 更大的字体 */
  line-height: 1.7;
  margin-bottom: 14px;
  min-height: 30px;
  font-weight: 500;
  letter-spacing: 0.5px;
  color: #1f2937; /* 更深的颜色 */
  transition: all 0.3s ease;
  padding: 2px 0;
  border-radius: 4px;
}

.subtitle-zh {
  font-size: 17px; /* 更大的字体 */
  color: #4b5563;
  line-height: 1.6;
  min-height: 25px;
  font-weight: 400;
  letter-spacing: 0.2px;
  transition: all 0.3s ease;
  padding: 2px 0;
  border-radius: 4px;
}

/* Part-of-Speech Tagging Styles */
/* 白天模式词性标记样式 */
.pos-noun {
  background-image: linear-gradient(to top, #fefcbf 50%, transparent 50%);
  padding: 0.1rem 0.3rem;
}

.pos-verb {
  background-image: linear-gradient(to top, #c6f6d5 50%, transparent 50%);
  padding: 0.1rem 0.3rem;
}

.pos-adjective {
  background-image: linear-gradient(to top, #bee3f8 50%, transparent 50%);
  padding: 0.1rem 0.3rem;
}

.pos-adverb {
  background-image: linear-gradient(to top, #fed7e2 50%, transparent 50%);
  padding: 0.1rem 0.3rem;
}

.pos-particle {
  background-image: linear-gradient(to top, #e9d8fd 50%, transparent 50%);
  padding: 0.1rem 0.3rem;
}

.pos-aux {
  background-image: linear-gradient(to top, #d1fae5 50%, transparent 50%);
  padding: 0.1rem 0.3rem;
}

.pos-conj {
  background-image: linear-gradient(to top, #fef3c7 50%, transparent 50%);
  padding: 0.1rem 0.3rem;
}

.pos-pron {
  background-image: linear-gradient(to top, #ffcccb 50%, transparent 50%);
  padding: 0.1rem 0.3rem;
}

.pos-other {
  background-image: linear-gradient(to top, #d3d3d3 50%, transparent 50%);
  padding: 0.1rem 0.3rem;
}

.pos-interjection {
  background-image: linear-gradient(to top, #ffaa66 50%, transparent 50%);
  padding: 0.1rem 0.3rem;
}

.pos-punctuation {
  padding: 0.1rem 0.3rem;
}

/* 深色模式词性标记样式 */
body.dark-mode .pos-noun {
  background-image: linear-gradient(to top, rgba(180, 160, 10, 0.5) 50%, transparent 50%);
}

body.dark-mode .pos-verb {
  background-image: linear-gradient(to top, rgba(20, 120, 60, 0.5) 50%, transparent 50%);
}

body.dark-mode .pos-adjective {
  background-image: linear-gradient(to top, rgba(30, 90, 150, 0.5) 50%, transparent 50%);
}

body.dark-mode .pos-adverb {
  background-image: linear-gradient(to top, rgba(150, 60, 90, 0.5) 50%, transparent 50%);
}

body.dark-mode .pos-particle {
  background-image: linear-gradient(to top, rgba(120, 70, 180, 0.5) 50%, transparent 50%);
}

body.dark-mode .pos-aux {
  background-image: linear-gradient(to top, rgba(40, 140, 100, 0.5) 50%, transparent 50%);
}

body.dark-mode .pos-conj {
  background-image: linear-gradient(to top, rgba(160, 130, 30, 0.5) 50%, transparent 50%);
}

body.dark-mode .pos-pron {
  background-image: linear-gradient(to top, rgba(160, 60, 60, 0.5) 50%, transparent 50%);
}

body.dark-mode .pos-other {
  background-image: linear-gradient(to top, rgba(100, 100, 100, 0.5) 50%, transparent 50%);
}

body.dark-mode .pos-interjection {
  background-image: linear-gradient(to top, rgba(180, 100, 40, 0.5) 50%, transparent 50%);
}

/* Ruby (Furigana) Styles */
ruby {
  ruby-align: center;
}

rt {
  font-size: 0.7em;
  color: #4b5563; /* 适应白色背景 */
  font-weight: normal;
}

/* Subtitle Controls */
.subtitle-controls {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}

.analyze-btn, .tts-btn {
  width: auto;
  min-width: 70px;
  padding: 5px 12px;
  text-align: center;
  box-sizing: border-box;
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: none;
  height: 28px;
  border-radius: 14px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  letter-spacing: 0.3px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px; /* 图标和文字之间的间距 */
}

/* 按钮图标样式 */
.analyze-btn::before, .tts-btn::before, .transcribe-btn::before, .summary-btn::before {
  width: 14px;
  height: 14px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 语法分析按钮图标 - 搜索/分析图标 */
.analyze-btn::before {
  content: "";
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%233b82f6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/><path d="M11 8a3 3 0 1 0 0 6"/></svg>');
}

/* TTS按钮图标 - 默认状态（音量图标） */
.tts-btn::before {
  content: "";
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%233b82f6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/><path d="M15.54 8.46a5 5 0 0 1 0 7.07"/><path d="M19.07 4.93a10 10 0 0 1 0 14.14"/></svg>');
}

/* TTS按钮激活状态图标（音乐图标） */
.tts-btn.active::before {
  content: "";
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18V5l12-2v13"/><circle cx="6" cy="18" r="3"/><circle cx="18" cy="16" r="3"/></svg>');
}

/* 字幕转录按钮图标 - 文档/转录图标 */
.transcribe-btn::before {
  content: "";
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%233b82f6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10,9 9,9 8,9"/></svg>');
}

/* 视频摘要按钮图标 - 列表/摘要图标 */
.summary-btn::before {
  content: "";
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%233b82f6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 6h13"/><path d="M8 12h13"/><path d="M8 18h13"/><path d="M3 6h.01"/><path d="M3 12h.01"/><path d="M3 18h.01"/></svg>');
}

/* 暗色模式下的图标颜色调整 */
body.dark-mode .analyze-btn::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%2360a5fa" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/><path d="M11 8a3 3 0 1 0 0 6"/></svg>');
}

body.dark-mode .tts-btn::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%2360a5fa" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/><path d="M15.54 8.46a5 5 0 0 1 0 7.07"/><path d="M19.07 4.93a10 10 0 0 1 0 14.14"/></svg>');
}

body.dark-mode .transcribe-btn::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%2360a5fa" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14,2 14,8 20,8"/><line x1="16" y1="13" x2="8" y2="13"/><line x1="16" y1="17" x2="8" y2="17"/><polyline points="10,9 9,9 8,9"/></svg>');
}

body.dark-mode .summary-btn::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%2360a5fa" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 6h13"/><path d="M8 12h13"/><path d="M8 18h13"/><path d="M3 6h.01"/><path d="M3 12h.01"/><path d="M3 18h.01"/></svg>');
}

.analyze-btn:hover, .tts-btn:hover {
  background-color: rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.analyze-btn:active, .tts-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* TTS Active State */
.tts-btn.active {
  background-color: rgba(16, 185, 129, 0.1); /* 使用绿色系代替红色 */
  color: #10b981;
}

.tts-btn.active:hover {
  background-color: rgba(16, 185, 129, 0.15);
}

/* Dark mode styles for buttons */
body.dark-mode .analyze-btn, 
body.dark-mode .tts-btn,
body.dark-mode .transcribe-btn,
body.dark-mode .summary-btn {
  background-color: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
}

body.dark-mode .analyze-btn:hover, 
body.dark-mode .tts-btn:hover,
body.dark-mode .transcribe-btn:hover,
body.dark-mode .summary-btn:hover {
  background-color: rgba(59, 130, 246, 0.25);
}

body.dark-mode .tts-btn.active {
  background-color: rgba(16, 185, 129, 0.2); /* 深色模式也使用绿色 */
  color: #34d399;
}

body.dark-mode .tts-btn.active:hover {
  background-color: rgba(16, 185, 129, 0.25);
}

/* Analysis Modal Styles */
#extension-analysis-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10004;
  display: none;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f9fafb;
}

.modal-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #e5e7eb;
  color: #374151;
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(80vh - 120px);
  color: #1f2937;
  line-height: 1.6;
}

.modal-body h1 {
  color: #1f2937;
  font-size: 20px;
  margin-bottom: 16px;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
}

.modal-body h2 {
  color: #374151;
  font-size: 18px;
  margin: 20px 0 12px 0;
}

.modal-body h3 {
  color: #4b5563;
  font-size: 16px;
  margin: 16px 0 8px 0;
}

.modal-body li {
  margin-bottom: 8px;
  list-style-type: disc;
  margin-left: 20px;
}

.modal-body strong {
  color: #1f2937;
  font-weight: 600;
}

.modal-body em {
  color: #6b7280;
  font-style: italic;
}

/* Custom Checkbox Styles for Analysis */
.custom-checkbox {
  margin-right: 6px;
  transform: scale(1.2);
}

/* Notification Styles */
#extension-subtitle-notification {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 20px;
  border-radius: 8px;
  font-family: 'Roboto', Arial, sans-serif;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 10003;
  text-align: center;
  min-width: 250px;
  font-weight: 500;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Responsive Design */


/* 深色模式样式 */
body.dark-mode #study-mode-overlay {
  background: linear-gradient(to bottom right, #1a202c, #2d3748, #4a5568);
}

/* 深色模式下的播放器容器 */
body.dark-mode .study-mode-player-container {
  background: rgba(26, 32, 44, 0.9);
  border-color: rgba(74, 85, 104, 0.5);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* 深色模式下的字幕区域 */
body.dark-mode #extension-subtitle-container {
  background: rgba(30, 41, 59, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #e5e7eb;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
}

/* 学习模式下强制字幕容器透明 - 最高优先级 */
.study-mode-active #extension-subtitle-container,
#study-mode-overlay #extension-subtitle-container,
body.dark-mode.study-mode-active #extension-subtitle-container,
body.dark-mode #study-mode-overlay #extension-subtitle-container {
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  backdrop-filter: none !important;
}

/* 移除光效边框在学习模式下 */
.study-mode-active #extension-subtitle-container::before,
#study-mode-overlay #extension-subtitle-container::before {
  display: none !important;
}

/* 深色模式下的日文字幕 */
body.dark-mode .subtitle-ja {
  color: #e5e7eb;
}

/* 深色模式下的中文字幕 */
body.dark-mode .subtitle-zh {
  color: #9ca3af;
}

/* 深色模式下的注音 */
body.dark-mode rt {
  color: #9ca3af;
}

/* 深色模式下的模态对话框 */
body.dark-mode .modal-content {
  background: #1f2937;
}

body.dark-mode .modal-header {
  background: #111827;
  border-bottom: 1px solid #374151;
}

body.dark-mode .modal-header h3 {
  color: #f9fafb;
}

body.dark-mode .modal-close {
  color: #9ca3af;
}

body.dark-mode .modal-close:hover {
  background: #374151;
  color: #e5e7eb;
}

body.dark-mode .modal-body {
  color: #e5e7eb;
}

body.dark-mode .modal-body h1 {
  color: #f9fafb;
  border-bottom: 2px solid #374151;
}

body.dark-mode .modal-body h2 {
  color: #e5e7eb;
}

body.dark-mode .modal-body h3 {
  color: #d1d5db;
}

body.dark-mode .modal-body strong {
  color: #f9fafb;
}

body.dark-mode .modal-body em {
  color: #9ca3af;
}

body.dark-mode #exit-study-mode {
  background-color: rgba(160, 174, 192, 0.8);
  color: #1a202c;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

body.dark-mode #exit-study-mode:hover {
  background-color: rgba(203, 213, 224, 0.9);
}

/* 新增标题栏样式 */
.subtitle-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 12px;
}

/* 添加进入和退出动画的CSS */

/* 整体元素进入动画 */
.entering {
  opacity: 0 !important;
  transform: scale(0.95) !important;
  transition: opacity 0.4s cubic-bezier(0.19, 1, 0.22, 1), transform 0.4s cubic-bezier(0.19, 1, 0.22, 1) !important;
}

/* 整体元素退出动画 */
.exiting {
  opacity: 0 !important;
  transform: scale(0.95) translateY(20px) !important;
  transition: opacity 0.5s cubic-bezier(0.19, 1, 0.22, 1), transform 0.5s cubic-bezier(0.19, 1, 0.22, 1) !important;
  pointer-events: none !important;
}

/* 通知动画 */
.notification-enter {
  opacity: 0;
  transform: translateY(20px) translateX(-50%);
  transition: opacity 0.4s cubic-bezier(0.19, 1, 0.22, 1), transform 0.4s cubic-bezier(0.19, 1, 0.22, 1);
}

.notification-exit {
  opacity: 0;
  transform: translateY(-20px) translateX(-50%);
  transition: opacity 0.3s cubic-bezier(0.19, 1, 0.22, 1), transform 0.3s cubic-bezier(0.19, 1, 0.22, 1);
}

/* 字幕进入动画 */
#extension-subtitle-container.entering {
  opacity: 0 !important;
  transform: translateY(30px) translateX(-50%) !important;
}

/* 字幕退出动画 */
#extension-subtitle-container.exiting {
  opacity: 0 !important;
  transform: translateY(30px) translateX(-50%) !important;
  transition: opacity 0.4s cubic-bezier(0.19, 1, 0.22, 1), transform 0.4s cubic-bezier(0.19, 1, 0.22, 1) !important;
}

body.dark-mode #exit-study-mode:hover {
  background: rgba(15, 23, 42, 0.95);
}

/* 深色模式下的聊天区域样式 */
body.dark-mode .study-mode-chat-container {
  background: rgba(30, 41, 59, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 深色模式下聊天区域的紫色光效边框 - 与字幕区域保持一致 */
body.dark-mode .study-mode-chat-container::before {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(79, 70, 229, 0.2)); /* 与字幕区域相同的紫色渐变 */
}

body.dark-mode .chat-header {
  display: none;
}

body.dark-mode .chat-title {
  display: none;
}

body.dark-mode .chat-input-area {
  background: rgba(30, 41, 59, 0.7);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

body.dark-mode .chat-input {
  background: rgba(51, 65, 85, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #e5e7eb;
}

body.dark-mode .chat-input::placeholder {
  color: #9ca3af;
}

body.dark-mode .chat-input:focus {
  border-color: #9ca3af; /* 保持灰色边框 */
  background: rgba(51, 65, 85, 0.95);
}

/* 深色模式下的头像保持灰色背景，白色文字 */
body.dark-mode .chat-avatar.ai {
  background: #4b5563; /* 深色模式下稍浅的灰色背景 */
  box-shadow: 0 4px 12px rgba(75, 85, 99, 0.3);
  color: #d1d5db; /* 深色模式下头像文字改为浅灰色 */
}

body.dark-mode .chat-avatar.user {
  background: #4b5563; /* 深色模式下稍浅的灰色背景 */
  box-shadow: 0 4px 12px rgba(75, 85, 99, 0.3);
  color: #d1d5db; /* 深色模式下头像文字改为浅灰色 */
}

/* 深色模式下的对话气泡 */
body.dark-mode .chat-message.ai .chat-bubble {
  background: #1f2937; /* 深色模式下AI气泡使用深灰色 */
  color: #ffffff; /* 深色模式下AI文字使用白色 */
  border-radius: 4px 18px 18px 18px; /* 左上角小圆角，其他大圆角 */
}

body.dark-mode .chat-message.user .chat-bubble {
  background: #2563eb; /* 蓝色 */
  border: none; /* 取消边框 */
  color: white;
  border-radius: 18px 4px 18px 18px; /* 右上角小圆角，其他大圆角 */
}

/* 深色模式下的发送按钮 */
body.dark-mode .chat-send-btn {
  background: #6b7280; /* 灰色 */
}

body.dark-mode .chat-send-btn:hover {
  background: #4b5563; /* 深一点的灰色 */
}

body.dark-mode .chat-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

body.dark-mode .chat-messages::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

body.dark-mode .chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Loading消息样式 */
.loading-message {
  opacity: 1;
}

.loading-bubble {
  background: #dbeafe !important; /* 与AI消息相同的浅蓝色背景 */
  border: none !important; /* 与AI消息一致，无边框 */
  color: #6b7280 !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1) !important; /* 与AI消息相同的阴影 */
  border-radius: 4px 18px 18px 18px !important; /* 与AI消息相同的圆角 */
}

/* 聊天loading专用样式 */
.loading-bubble .loading-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 0;
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #6b7280;
  animation: loadingDots 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 聊天loading文字样式 */
.loading-bubble .loading-text {
  font-size: 14px;
  color: #6b7280;
  font-style: italic;
}

/* 深色模式下的loading样式 */
body.dark-mode .loading-bubble {
  background: #1e3a8a !important; /* 与深色模式AI消息相同的深蓝色背景 */
  border: none !important; /* 与深色模式AI消息一致，无边框 */
  color: #9ca3af !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1) !important; /* 与AI消息相同的阴影 */
  border-radius: 4px 18px 18px 18px !important; /* 与AI消息相同的圆角 */
}

body.dark-mode .loading-dots span {
  background: #9ca3af;
}

body.dark-mode .loading-bubble .loading-text {
  color: #9ca3af;
}

/* 复制按钮样式 - 显示在内容末尾 */
.copy-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  cursor: pointer;
  margin-left: 8px;
  vertical-align: middle;
  transition: all 0.2s ease;
  font-size: 12px;
  color: #6b7280;
  flex-shrink: 0;
}

.copy-button:hover {
  background: rgba(0, 0, 0, 0.2);
  color: #374151;
  transform: scale(1.1);
}

.copy-button.copied {
  background: #10b981;
  color: white;
}

/* 深色模式下的复制按钮 */
body.dark-mode .copy-button {
  background: rgba(255, 255, 255, 0.15);
  color: #9ca3af;
}

body.dark-mode .copy-button:hover {
  background: rgba(255, 255, 255, 0.25);
  color: #d1d5db;
  transform: scale(1.1);
}

body.dark-mode .copy-button.copied {
  background: #10b981;
  color: white;
}

/* 聊天气泡样式 */
.study-mode-chat-bubble {
  position: fixed;
  top: 50%;
  right: 30px;
  transform: translateY(-50%);
  width: 100px;
  height: 50px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10005;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
  user-select: none;
}

.study-mode-chat-bubble:hover {
  transform: translateY(-50%) scale(1.02);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.study-mode-chat-bubble.active {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);
}

.chat-bubble-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6px;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.chat-bubble-icon svg {
  width: 18px;
  height: 18px;
}

.study-mode-chat-bubble:hover .chat-bubble-icon {
  color: rgba(255, 255, 255, 0.9);
}

.study-mode-chat-bubble.active .chat-bubble-icon {
  color: rgba(59, 130, 246, 0.9);
}

.chat-bubble-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
}

.study-mode-chat-bubble:hover .chat-bubble-text {
  color: rgba(255, 255, 255, 0.9);
}

.study-mode-chat-bubble.active .chat-bubble-text {
  color: rgba(59, 130, 246, 0.9);
}

/* 聊天容器动画状态 */
.study-mode-chat-container.hidden {
  opacity: 0;
  transform: translateX(100%) scale(0.8);
  pointer-events: none;
}

.study-mode-chat-container.expanding {
  animation: expandChat 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.study-mode-chat-container.collapsing {
  animation: collapseChat 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
}

@keyframes expandChat {
  0% {
    opacity: 0;
    transform: translateX(100%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes collapseChat {
  0% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(100%) scale(0.8);
  }
}

/* 暗色模式下的聊天气泡 */
body.dark-mode .study-mode-chat-bubble {
  background: rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

body.dark-mode .study-mode-chat-bubble:hover {
  background: rgba(0, 0, 0, 0.3);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

body.dark-mode .study-mode-chat-bubble.active {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.15);
}

body.dark-mode .chat-bubble-icon {
  color: rgba(255, 255, 255, 0.6);
}

body.dark-mode .study-mode-chat-bubble:hover .chat-bubble-icon {
  color: rgba(255, 255, 255, 0.8);
}

body.dark-mode .study-mode-chat-bubble.active .chat-bubble-icon {
  color: rgba(59, 130, 246, 0.8);
}

body.dark-mode .chat-bubble-text {
  color: rgba(255, 255, 255, 0.6);
}

body.dark-mode .study-mode-chat-bubble:hover .chat-bubble-text {
  color: rgba(255, 255, 255, 0.8);
}

body.dark-mode .study-mode-chat-bubble.active .chat-bubble-text {
  color: #e2e8f0;
}

/* Premium升级提示样式 - 简化版 */
.premium-upgrade-bubble {
  background: #fef3c7 !important;
  border: 1px solid #f59e0b !important;
  border-radius: 12px !important;
  padding: 12px !important;
  color: #92400e !important;
  max-width: 280px !important; /* 设置合适的最大宽度 */
}

.premium-upgrade-bubble .chat-text-content {
  display: block;
  margin-bottom: 8px;
  line-height: 1.4;
}

.upgrade-link-btn {
  background: #f59e0b;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 4px;
}

.upgrade-link-btn:hover {
  background: #d97706;
}

/* 暗黑模式下的Premium升级提示样式 */
body.dark-mode .premium-upgrade-bubble {
  background: #1e3a8a !important;
  border: 1px solid #3b82f6 !important;
  color: #dbeafe !important;
}

body.dark-mode .upgrade-link-btn {
  background: #3b82f6;
}

body.dark-mode .upgrade-link-btn:hover {
  background: #2563eb;
}




