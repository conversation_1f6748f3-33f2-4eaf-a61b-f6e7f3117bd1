# YouTube 学习扩展

这是一个Chrome扩展，用于在YouTube上学习日语和英语。

## 功能特性

### 字幕功能
- 自动获取和显示YouTube视频的双语字幕
- 支持日语、英语等多种语言
- 字幕同步显示和词汇标注

### AI功能（Premium会员专享）
- **AI智能助手** - 基于视频内容的智能问答
- **字幕转录** - 高质量语音转文字服务
- **视频摘要** - 智能生成视频内容摘要

### 学习工具
- 语法分析和词汇解释
- 文本转语音（TTS）功能
- 学习进度跟踪

### 权限管理
- 新用户自动获得7天Premium试用
- Premium功能包括AI助手、字幕转录、视频摘要
- 非Premium用户可使用基础字幕功能

## 安装说明

1. 下载或克隆此项目
2. 打开Chrome浏览器，进入扩展管理页面（chrome://extensions/）
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"，选择项目目录
5. 扩展安装完成后，访问YouTube视频页面即可使用

## 使用方法

### 基础功能
1. 在YouTube视频页面，扩展会自动激活
2. 点击右下角的扩展图标进入学习模式
3. 字幕会自动加载并显示在视频下方

### Premium功能
1. **AI助手**：点击聊天气泡图标，可以询问关于视频内容的问题
2. **字幕转录**：点击转录按钮，AI会自动转录视频音频为字幕
3. **视频摘要**：点击摘要按钮，AI会生成视频内容的智能摘要

### 权限提示
- 如果您不是Premium会员，使用Premium功能时会显示升级提示
- 提示框会展示Premium功能的详细介绍和升级链接
- 新用户可享受7天免费Premium试用

## 技术架构

### 前端
- Chrome Extension Manifest V3
- 模块化JavaScript架构
- CSS3动画和现代UI设计

### 后端集成
- Spring Boot REST API
- Premium会员权限管理
- AI服务集成（DashScope API）

### 权限校验流程
1. 用户点击Premium功能按钮
2. 前端调用权限检查API
3. 后端验证用户订阅状态
4. 根据结果允许使用或显示升级提示

## 开发说明

### 文件结构
```
chrome-extension-demo/
├── manifest.json              # 扩展配置文件
├── background.js              # 后台脚本
├── content.js                 # 内容脚本
├── modules/
│   ├── study-mode/           # 学习模式模块
│   │   ├── study-mode.js     # 主控制器
│   │   ├── study-mode-controls.js  # 播放控制（含权限校验）
│   │   └── study-mode-chat.js      # AI助手功能
│   └── subtitle/             # 字幕功能模块
│       ├── subtitle-manager.js     # 字幕管理器
│       ├── subtitle-transcription.js  # 转录功能
│       └── subtitle-analysis.js       # 分析和摘要功能
└── assets/                   # 静态资源
    ├── styles/              # CSS样式文件
    └── images/              # 图片资源
```

### 权限校验实现
- `checkPremiumPermission()` - 通用权限检查函数
- `showPremiumUpgradeDialog()` - 升级提示对话框
- 后端API统一返回权限错误码（PREMIUM_REQUIRED）
- 前端统一处理权限错误并显示升级提示

## 更新日志

### v1.2.0 (2024-01-XX)
- ✅ 添加Premium会员权限校验
- ✅ 为字幕转录和视频摘要功能添加权限限制
- ✅ 实现美观的升级提示对话框
- ✅ 优化错误处理和用户体验
- ✅ 复用AI助手的权限校验逻辑

### v1.1.0 (2024-01-XX)
- 添加AI助手功能
- 实现字幕转录和视频摘要
- 添加Premium会员系统

### v1.0.0 (2024-01-XX)
- 基础字幕功能
- 多语言支持
- TTS语音合成

## 许可证

MIT License
