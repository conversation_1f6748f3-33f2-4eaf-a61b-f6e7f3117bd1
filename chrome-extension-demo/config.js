// Chrome插件API配置文件
const ExtensionConfig = {
    // API基础地址配置
    API_BASE_URL: 'http://localhost:8080',
    
    // 具体API端点
    API_ENDPOINTS: {
        // 字幕相关
        FETCH_SUBTITLES: '/api/extension/subtitles/fetch',
        CLEANUP_SUBTITLES: '/api/extension/subtitles/cleanup',
        TAG_SUBTITLE: '/api/subtitle/tag', 
        ANALYZE_SUBTITLE: '/api/subtitle/analyze',
        NORMALIZE_WORD: '/api/subtitle/normalize',
        FAVORITE_WORD: '/api/subtitle/favorite',
        UNFAVORITE_WORD: '/api/subtitle/unfavorite',
        LIST_FAVORITES: '/api/subtitle/list',
        
        // 翻译相关
        TRANSLATE_WORD: '/api/translate/word',
        
        // 认证相关
        TOKEN_LOGIN: '/api/auth/token-login',
        CHECK_AUTH: '/api/auth/check'
    },
    
    // 获取完整API URL
    getApiUrl: function(endpoint) {
        return this.API_BASE_URL + (this.API_ENDPOINTS[endpoint] || endpoint);
    },
    
    // 获取字幕文件URL
    getSubtitleUrl: function(path) {
        return this.API_BASE_URL + path;
    }
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ExtensionConfig;
} else {
    window.ExtensionConfig = ExtensionConfig;
}
