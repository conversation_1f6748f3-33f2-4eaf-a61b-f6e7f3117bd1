// 分析面板组件 - Chrome扩展版本
let analysisPanel = null;
let analysisPanelVisible = false;

// 初始化分析面板
function initAnalysisPanel() {
    // 检查是否已经存在分析面板
    if (document.getElementById('analysis-panel')) {
        return;
    }
    
    // 检查并应用当前的主题设置
    chrome.storage.sync.get(['darkMode'], function(result) {
        if (result.darkMode) {
            document.body.classList.add('dark-mode');
        }
    });

    // 创建分析面板容器
    const container = document.createElement('div');
    container.id = 'analysis-panel-container';
    // 强制提升容器的堆叠上下文，确保它位于所有页面内容之上
    container.style.position = 'relative';
    container.style.zIndex = '2147483646'; // 使用一个非常高的 z-index
    document.body.appendChild(container);

    // 插入分析面板HTML
    container.innerHTML = `
        <div class="fixed top-0 right-0 h-full w-1/4 bg-white shadow-lg transform translate-x-full transition-transform duration-300" id="analysis-panel" style="z-index: 2147483647;">
            <div class="glass-container p-4 rounded-xl shadow-lg h-full flex flex-col">
                <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                    <span id="analysis-title">AI语法解析结果</span>
                </h2>
                <div id="analysis-container" class="text-gray-700 overflow-y-auto flex-1" style="max-height: calc(100% - 120px);">
                    <p class="analysis-item p-4 rounded-lg bg-gray-50">等待分析...</p>
                </div>
                <button id="save-selected-content-btn" class="save-btn flex items-center justify-center mt-4">
                    <i class="fas fa-bookmark mr-2"></i>一键收藏
                </button>
            </div>
            <!-- 推拉手柄 -->
            <button class="absolute top-1/2 -left-8 transform -translate-y-1/2 bg-gray-600 text-white px-2 py-4 rounded-l-lg hover:bg-gray-700 transition flex items-center justify-center" id="panel-toggle">
                <i class="fas fa-chevron-left text-lg"></i>
            </button>
        </div>
    `;

    // 获取面板元素
    analysisPanel = document.getElementById('analysis-panel');
    
    // 添加事件监听器
    document.getElementById('panel-toggle').addEventListener('click', toggleAnalysisPanel);
    document.getElementById('save-selected-content-btn').addEventListener('click', saveSelectedContent);
    
    // 加载FontAwesome图标
    loadFontAwesome();
}

// 加载FontAwesome图标
function loadFontAwesome() {
    if (document.querySelector('link[href*="fontawesome"]')) {
        return; // 已加载
    }
    
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css';
    document.head.appendChild(link);
    
    // 确保图标样式加载完成
    console.log('开始加载FontAwesome图标库');
    
    // 添加直接的图标样式，防止CDN加载失败
    const iconStyle = document.createElement('style');
    iconStyle.textContent = `
        .fa-lightbulb:before,
        .fas.fa-lightbulb:before {
            content: "\\f0eb";
            color: #f59e0b !important;
        }
        
        .fa-bookmark:before,
        .fas.fa-bookmark:before {
            content: "\\f02e";
        }
        
        .fa-chevron-left:before {
            content: "\\f053";
        }
        
        .fa-chevron-right:before {
            content: "\\f054";
        }
        
        .fa-check-circle:before {
            content: "\\f058";
        }
        
        .fa-exclamation-circle:before {
            content: "\\f06a";
        }
        
        .fa-exclamation-triangle:before {
            content: "\\f071";
        }
        
        .fa-info-circle:before {
            content: "\\f05a";
        }
    `;
    document.head.appendChild(iconStyle);
}

// 切换分析面板显示/隐藏
function toggleAnalysisPanel() {
    if (!analysisPanel) {
        initAnalysisPanel();
    }
    
    const toggleIcon = document.querySelector('#panel-toggle i');
    
    if (analysisPanelVisible) {
        analysisPanel.style.transform = 'translateX(100%)';
        toggleIcon.classList.replace('fa-chevron-right', 'fa-chevron-left');
    } else {
        analysisPanel.style.transform = 'translateX(0)';
        toggleIcon.classList.replace('fa-chevron-left', 'fa-chevron-right');
    }
    
    analysisPanelVisible = !analysisPanelVisible;
}

// 分析字幕内容
async function analyzeSubtitle(jaText, zhText = '') {
    if (!analysisPanel) {
        initAnalysisPanel();
    }
    
    const container = document.getElementById('analysis-container');
    
    // 检查字幕是否为空
    if (!jaText || jaText === '无字幕' || jaText === '加载中...' || jaText === '字幕加载失败') {
        showToast('当前无字幕可分析', 'error');
        return;
    }
    
    // 显示优美的加载动画
    container.innerHTML = `
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <div class="loading-text">
                AI正在分析中<span class="loading-dots"></span>
            </div>
        </div>
    `;
    
    // 显示分析面板
    if (!analysisPanelVisible) {
        toggleAnalysisPanel();
    }
    
    console.log('发送分析请求: 日文=', jaText, '中文=', zhText);
    
    // 获取当前选择的语言
    const selectedLanguage = await new Promise((resolve) => {
        chrome.storage.sync.get(['selectedVideoLanguage'], function(result) {
            const language = result.selectedVideoLanguage || 'japanese'; // 默认为日语
            resolve(language);
        });
    });
    
    console.log('当前选择的语言:', selectedLanguage);
    
    // 使用Chrome扩展API代理请求，避免跨域问题
    chrome.runtime.sendMessage({
        action: 'proxyFetch',
        url: 'http://localhost:8080/api/subtitle/analyze',
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: `ja=${encodeURIComponent(jaText)}&zh=${encodeURIComponent(zhText)}&language=${encodeURIComponent(selectedLanguage)}`
    }, function(response) {
        console.log('收到分析响应:', response);
        
        if (response && response.success && response.data) {
            const data = response.data;
            
            // 获取markdown内容
            let markdownContent = data.markdown;
            
            if (markdownContent) {
                // 根据语言类型选择不同的处理方式
                if (selectedLanguage === 'english') {
                    // 英语：为固定搭配和俚语添加彩色单选框
                    markdownContent = processEnglishAnalysisContent(markdownContent);
                } else {
                    // 日语：为语法点添加彩色复选框（保持原有逻辑）
                    markdownContent = processJapaneseAnalysisContent(markdownContent);
                }
                
                // 先处理markdown格式
                // 处理加粗和斜体
                markdownContent = markdownContent
                    .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*([^*]+)\*/g, '<em>$1</em>')
                    .replace(/\n/g, '<br>');
                
                // 清空容器并添加分析结果
                container.innerHTML = '';
                const item = document.createElement('div');
                item.className = 'analysis-item p-4 border-b border-gray-200 markdown-content bg-white rounded-lg shadow-sm fade-in';
                item.innerHTML = markdownContent;
                container.prepend(item);
                container.scrollTop = 0;
            } else {
                container.innerHTML = '<p class="analysis-item p-4 rounded-lg bg-gray-50">未找到可分析的内容</p>';
                showToast('未找到可分析的内容', 'warning');
            }
        } else {
            console.error('分析失败:', response);
            container.innerHTML = '<p class="analysis-item p-4 rounded-lg bg-gray-50">分析失败，请重试</p>';
            showToast('分析失败，请重试', 'error');
        }
    });
}

// 处理日语分析内容（保持原有逻辑）
function processJapaneseAnalysisContent(content) {
    // 定义随机颜色数组
    const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899'];
    // 计数器，用于为每个复选框生成唯一 ID
    let checkboxCounter = 0;
    
    // 在每个 ▲ 前添加复选框
    return content.replace(/▲/g, () => {
        const randomColor = colors[Math.floor(Math.random() * colors.length)];
        checkboxCounter++;
        return `<input type="checkbox" class="custom-checkbox" id="checkbox-${checkboxCounter}" style="color: ${randomColor};" data-content-id="${checkboxCounter}"><label for="checkbox-${checkboxCounter}" class="inline-block align-middle">▲</label>`;
    });
}

// 处理英语分析内容
function processEnglishAnalysisContent(content) {
    // 定义随机颜色数组
    const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899'];
    let checkboxCounter = 0;
    
    // 第一步：先识别固定搭配和俚语的具体内容行，添加复选框（在添加颜色之前）
    const lines = content.split('\n').map(line => line.replace(/\r/g, ''));
    let inPhraseSection = false;
    let sectionType = '';
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        
        // 检查是否是固定搭配或俚语的标题行
        if (line.includes('▲固定搭配') || line.includes('▲俚语') || line.includes('▲惯用表达')) {
            inPhraseSection = true;
            sectionType = line.includes('固定搭配') ? '固定搭配' : 
                         line.includes('俚语') ? '俚语' : '惯用表达';
            continue;
        }
        
        // 检查是否进入了新的语法部分（以▲开头，但不是固定搭配/俚语/惯用表达）
        if (line.startsWith('▲') && 
            !line.includes('固定搭配') && !line.includes('俚语') && !line.includes('惯用表达')) {
            inPhraseSection = false;
            continue;
        }
        
        // 如果在固定搭配/俚语部分，且这行包含具体内容（有冒号分隔）
        if (inPhraseSection && line && 
            line.includes(':') && 
            !line.startsWith('▲')) {
            
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            checkboxCounter++;
            
                                // 在具体内容前添加复选框（不包含三角符号）
                    lines[i] = `<input type="checkbox" class="custom-checkbox phrase-content" id="phrase-${checkboxCounter}" style="color: ${randomColor};" data-content-id="${checkboxCounter}"> ${line}`;
        }
    }
    
    // 第二步：将换行符转换为<br>
    content = lines.join('<br>');
    
    // 第三步：为所有剩余的▲符号添加随机颜色
    content = content.replace(/▲/g, () => {
        const randomColor = colors[Math.floor(Math.random() * colors.length)];
        return `<span style="color: ${randomColor}; font-weight: bold;">▲</span>`;
    });
    
    return content;
}

// 保存选中内容
async function saveSelectedContent() {
    // 获取所有选中的复选框（包括日语的和英语固定搭配的）
    const checkboxes = document.querySelectorAll('#analysis-container .custom-checkbox:checked');
    
    if (checkboxes.length === 0) {
        showToast('请先选择要收藏的内容', 'error');
        return;
    }

    // 获取当前选择的语言
    const selectedLanguage = await new Promise((resolve) => {
        chrome.storage.sync.get(['selectedVideoLanguage'], function(result) {
            const language = result.selectedVideoLanguage || 'japanese'; // 默认为日语
            resolve(language);
        });
    });

    // 收集选中的内容
    const contentsToSave = [];
    
    // 处理所有复选框内容
    checkboxes.forEach(checkbox => {
        const content = extractContentFromInput(checkbox);
        if (content) {
            // 如果是英语模式，只发送冒号前面的部分
            if (selectedLanguage === 'english' && content.includes(':')) {
                const phraseOnly = content.split(':')[0].trim();
                contentsToSave.push(phraseOnly);
            } else {
                // 日语模式或没有冒号的内容，保持原样
                contentsToSave.push(content);
            }
        }
    });

    if (contentsToSave.length === 0) {
        showToast('未能提取有效内容进行收藏', 'warning');
        return;
    }

    console.log('将保存的内容:', contentsToSave, '语言:', selectedLanguage);

    // 使用Chrome扩展API代理请求，避免跨域问题
    chrome.runtime.sendMessage({
        action: 'proxyFetch',
        url: 'http://localhost:8080/api/subtitle/save',
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
            contents: contentsToSave,
            language: selectedLanguage 
        })
    }, function(response) {
        console.log('收藏响应:', response);
        try {
            if (response && response.success && response.data) {
                // 后端返回 { status: 'success/error', message: '...' }
                if (response.data.status === 'success') {
                    showToast(response.data.message || '收藏成功！', 'success');
                    // 清除选中状态
                    checkboxes.forEach(checkbox => checkbox.checked = false);
                } else {
                    showToast(response.data.message || '收藏失败', 'error');
                }
            } else {
                showToast('收藏请求失败', 'error');
            }
        } catch (error) {
            console.error('处理收藏响应失败:', error);
            showToast('收藏失败，请重试', 'error');
        }
    });
}

// 从输入元素（复选框或单选框）提取内容
function extractContentFromInput(inputElement) {
    const label = inputElement.nextElementSibling;
    
    // Handle Japanese case with <label>
    if (label && label.tagName === 'LABEL') {
        let nextNode = label.nextSibling;
        let textContent = '';
        
        // Collect all text nodes until the next input element.
        // This is the original logic which we assume is correct for Japanese.
        while (nextNode && 
              (!nextNode.tagName || 
               (nextNode.tagName !== 'INPUT' && 
                (!nextNode.classList || 
                 !nextNode.classList.contains('custom-checkbox'))))) {
            
            if (nextNode.nodeType === 3) { // Text node
                textContent += nextNode.textContent;
            } else if (nextNode.tagName === 'BR') {
                textContent += '\n';
            } else if (nextNode.tagName) { // other tags like <strong>
                textContent += nextNode.textContent;
            }
            
            nextNode = nextNode.nextSibling;
        }
        
        return textContent.trim();
    } 
    
    // Handle English case without <label>
    // The content is the text on the same line, terminated by a <br>.
    else {
        let nextNode = inputElement.nextSibling;
        let textContent = '';

        while (nextNode) {
            if (nextNode.nodeType === 1 && nextNode.tagName === 'BR') {
                break; // End of the line for this phrase
            }
            if (nextNode.nodeType === 1 && nextNode.tagName === 'INPUT') {
                break; // Don't run into the next item
            }

            textContent += nextNode.textContent;
            nextNode = nextNode.nextSibling;
        }

        return textContent.trim();
    }
}

// 显示Toast消息
function showToast(message, type = 'info') {
    // 检查是否已存在Toast容器
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 2147483648;';
        document.body.appendChild(toastContainer);
    }
    
    // 创建新的Toast
    const toast = document.createElement('div');
    
    // 设置样式和内容
    let bgColor, textColor, icon;
    switch (type) {
        case 'success':
            bgColor = '#22c55e';
            textColor = '#ffffff';
            icon = 'fa-check-circle';
            break;
        case 'error':
            bgColor = '#ef4444';
            textColor = '#ffffff';
            icon = 'fa-exclamation-circle';
            break;
        case 'warning':
            bgColor = '#f59e0b';
            textColor = '#ffffff';
            icon = 'fa-exclamation-triangle';
            break;
        default:
            bgColor = '#3b82f6';
            textColor = '#ffffff';
            icon = 'fa-info-circle';
    }
    
    toast.style.cssText = `
        background-color: ${bgColor};
        color: ${textColor};
        padding: 12px 16px;
        border-radius: 8px;
        margin-bottom: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        min-width: 250px;
        max-width: 350px;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
    `;
    
    toast.innerHTML = `
        <i class="fas ${icon} mr-2" style="margin-right: 10px;"></i>
        <span>${message}</span>
    `;
    
    // 添加到容器
    toastContainer.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateY(0)';
    }, 10);
    
    // 3秒后自动消失
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateY(-20px)';
        
        // 完全移除元素
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 3000);
}

// 监听来自插件的消息
chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
    if (message.action === 'toggleDarkMode') {
        // 处理主题切换
        if (message.isDarkMode) {
            document.body.classList.add('dark-mode');
        } else {
            document.body.classList.remove('dark-mode');
        }
    }
});

// 导出函数
window.initAnalysisPanel = initAnalysisPanel;
window.toggleAnalysisPanel = toggleAnalysisPanel;
window.analyzeSubtitle = analyzeSubtitle;
window.saveSelectedContent = saveSelectedContent;
