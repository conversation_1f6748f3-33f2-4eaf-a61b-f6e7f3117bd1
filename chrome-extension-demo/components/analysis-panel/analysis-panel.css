/* 分析面板样式 */
#analysis-panel-container {
    position: relative;
    z-index: 9999;
}

#analysis-panel {
    position: fixed;
    top: 0;
    right: 0;
    height: 100%;
    width: 25%;
    background-color: #ffffff; /* 改为不透明白色 */
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 10010; /* 比退出按钮的z-index值10002更高 */
}

#analysis-panel.visible {
    transform: translateX(0);
}

.glass-container {
    padding: 1rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
}

#analysis-title {
    font-size: 1.8rem; /* 进一步增大标题字体 */
    font-weight: 600;
    color: #1f2937;
    display: inline; /* 确保标题在同一行显示 */
    vertical-align: middle; /* 垂直居中对齐 */
}

h2 i.fa-lightbulb {
    color: #f59e0b;
    margin-right: 0.75rem;
    font-size: 1.8rem; /* 进一步增大图标大小 */
    vertical-align: middle; /* 垂直居中对齐 */
    display: inline-block; /* 确保图标正确显示 */
}

#analysis-container {
    margin-top: 0.5rem;
    color: #4b5563;
    overflow-y: auto;
    flex: 1;
    max-height: calc(100% - 140px); /* 增加高度，为收藏按钮留出更多空间 */
    padding-bottom: 20px; /* 添加底部填充，确保内容不会被遮挡 */
}

.analysis-item {
    padding: 1.25rem;
    border-radius: 0.5rem;
    background-color: #f9fafb;
    margin-bottom: 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); /* 替换左侧边框为阴影效果 */
    transition: all 0.2s ease;
    font-size: 16px; /* 增大字体大小 */
    line-height: 1.6; /* 增加行高 */
}

.analysis-item:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.custom-checkbox {
    appearance: none;
    -webkit-appearance: none;
    width: 1.4rem; /* 增大复选框大小 */
    height: 1.4rem;
    border-radius: 0.25rem;
    margin-right: 0.75rem;
    vertical-align: middle;
    border: 2px solid;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
}

.custom-checkbox:checked {
    background-color: currentColor;
}

.custom-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.8rem;
}

/* 删除不需要的panel-header样式 */

.save-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.6rem 1rem; /* 减小填充使按钮更紧凑 */
    background: linear-gradient(to right, #2563eb, #3b82f6);
    color: white;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 14px; /* 调整按钮文字大小 */
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.2);
    position: relative; /* 确保定位正确 */
    z-index: 2; /* 增加z-index确保按钮可见 */
    width: auto; /* 让按钮宽度自适应内容 */
    white-space: nowrap; /* 防止文字换行 */
    margin-top: 1rem; /* 向上移动按钮 */
    margin-bottom: 0.5rem; /* 添加底部边距 */
    align-self: center; /* 水平居中 */
    max-width: 200px; /* 限制最大宽度 */
}

.save-btn:hover {
    background: linear-gradient(to right, #1d4ed8, #2563eb);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

#panel-toggle {
    position: absolute;
    top: 50%;
    left: -2.5rem;
    transform: translateY(-50%);
    background-color: #4b5563;
    color: white;
    padding: 0.75rem 0.5rem;
    border-radius: 0.5rem 0 0 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border: none;
    z-index: 10011; /* 降低z-index，但保持高于分析面板 */
    width: 2.5rem;
    height: 5rem;
    opacity: 1 !important;
    visibility: visible !important;
}

#panel-toggle:hover {
    background-color: #374151;
}

/* 随机颜色样式 */
.color-1 { border-color: #ef4444; color: #ef4444; }
.color-2 { border-color: #f59e0b; color: #f59e0b; }
.color-3 { border-color: #10b981; color: #10b981; }
.color-4 { border-color: #3b82f6; color: #3b82f6; }
.color-5 { border-color: #8b5cf6; color: #8b5cf6; }
.color-6 { border-color: #ec4899; color: #ec4899; }
.color-7 { border-color: #6366f1; color: #6366f1; }
.color-8 { border-color: #14b8a6; color: #14b8a6; }

/* 深色模式样式 */
body.dark-mode #analysis-panel {
  background-color: #1e293b; /* 改为不透明深色 */
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
}

body.dark-mode .glass-container {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body.dark-mode #analysis-title {
  color: #f9fafb;
}

body.dark-mode #analysis-container {
  color: #e5e7eb;
}

body.dark-mode .analysis-item {
  background-color: #1f2937;
  color: #e5e7eb;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

body.dark-mode .analysis-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

body.dark-mode .analysis-item strong {
  color: #f9fafb;
}

body.dark-mode .analysis-item em {
  color: #9ca3af;
}

body.dark-mode #panel-toggle {
  background-color: #374151;
}

body.dark-mode #panel-toggle:hover {
  background-color: #4b5563;
}

/* 加载动画样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    min-height: 200px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.loading-text {
    font-size: 16px;
    color: #6b7280;
    text-align: center;
    animation: pulse 2s ease-in-out infinite;
}

.loading-dots {
    display: inline-block;
    animation: dots 1.5s steps(4, end) infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* 深色模式下的加载动画 */
body.dark-mode .loading-spinner {
    border-color: #374151;
    border-top-color: #60a5fa;
}

body.dark-mode .loading-text {
    color: #9ca3af;
}

/* 渐入动画 */
.fade-in {
    opacity: 0;
    animation: fadeIn 0.5s ease-in-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
