// 侧边栏控制器
document.addEventListener('DOMContentLoaded', function() {
  // 主题设置
  const themeToggle = document.getElementById('theme-toggle');
  
  // 检查并应用保存的主题设置
  chrome.storage.sync.get(['darkMode'], function(result) {
    if (result.darkMode) {
      document.body.classList.add('dark-mode');
      themeToggle.checked = true;
    }
  });
  
  // 添加主题切换事件
  themeToggle.addEventListener('change', function() {
    toggleDarkMode(this.checked);
  });
  
  // 获取DOM元素
  const googleLoginBtn = document.getElementById('google-login');
  const logoutBtn = document.getElementById('logout');
  const preAuth = document.getElementById('pre-auth');
  const postAuth = document.getElementById('post-auth');
  const loading = document.getElementById('loading');
  const userAvatar = document.getElementById('user-avatar');
  const userName = document.getElementById('user-name');
  
  // 添加网站链接点击事件处理
  document.querySelectorAll('.web-link').forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const targetUrl = this.getAttribute('data-target');
      openWebPage(targetUrl);
    });
  });

  // 语言选择功能
  initLanguageSelection();
    
  // 检查用户是否已登录
  checkAuthState();
  
  // 添加谷歌登录按钮点击事件
  googleLoginBtn.addEventListener('click', authenticateWithGoogle);
  
  // 添加退出登录按钮点击事件
  logoutBtn.addEventListener('click', logout);
  
  // 验证推拉按钮点击时的会话状态
  window.addEventListener('message', function(event) {
    // 确保消息来自我们的扩展或父页面
    if (event.data && event.data.action === 'validateSession') {
      chrome.runtime.sendMessage({action: 'validateSession'}, function(response) {
        const isSessionValid = response && response.isValid;
        
        if (!isSessionValid) {
          // 会话过期，清除用户信息
          logout();
          showErrorMessage('会话已过期，请重新登录');
        }
        
        // 返回会话状态
        event.source.postMessage({
          action: 'sessionStatus',
          isValid: isSessionValid
        }, event.origin);
      });
    }
  });
  
  // 在新标签页中打开网站，确保已经通过后端认证
  function openWebPage(url) {
    // 显示加载状态
    loading.style.display = 'flex';
    
    // 从存储中获取访问令牌和会话ID
    chrome.storage.sync.get(['accessToken', 'subtitleFileId'], function(result) {
      const accessToken = result.accessToken;
      
      if (!accessToken) {
        console.log('没有访问令牌，直接打开页面');
        chrome.tabs.create({ url: url });
        loading.style.display = 'none';
        return;
      }
      
      // 先尝试调用预认证端点检查认证状态
      fetch('http://localhost:8080/api/auth/preauth', {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        credentials: 'include' // 包含cookies
      })
      .then(response => {
        console.log('预认证响应状态:', response.status);
        return response.json();
      })
      .then(preAuthData => {
        console.log('预认证检查结果:', preAuthData);
        
        // 如果已经认证，直接打开页面
        if (preAuthData.authenticated === true) {
          console.log('用户已经认证，直接跳转');
          chrome.tabs.create({ url: url, active: true });
          loading.style.display = 'none';
          return;
        }
        
        // 如果未认证，则调用token-login进行认证
        console.log('用户未认证，进行token认证');
        return fetch('http://localhost:8080/api/auth/token-login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            token: accessToken
          }),
          credentials: 'include' // 包含cookies
        })
        .then(response => {
          if (!response.ok) {
            throw new Error('Authentication failed with status: ' + response.status);
          }
          return response.json();
        })
        .then(data => {
          // 认证成功后，打开目标页面
          console.log('后端认证成功，准备跳转:', data);
          // 保存会话ID
          if (data.subtitleFileId) {
            chrome.storage.sync.set({subtitleFileId: data.subtitleFileId});
          }
          // 这里延迟1000ms确保cookie已被设置，以及会话已建立
          return new Promise(resolve => {
            setTimeout(() => {
              // 打开新标签页
              chrome.tabs.create({ 
                url: url,
                // 确保新打开的页面能继承当前的cookie
                active: true
              });
              loading.style.display = 'none';
              resolve();
            }, 1000);
          });
        });
      })
      .catch(error => {
        console.error('认证过程中出错:', error);
        // 发生错误时也打开页面，但用户可能需要手动登录
        chrome.tabs.create({ url: url });
        loading.style.display = 'none';
      });
    });
  }
  
  // 检查认证状态
  function checkAuthState() {
    // 从storage中获取用户信息
    chrome.storage.sync.get(['userInfo'], function(result) {
      if (result.userInfo) {
        // 用户已登录，显示用户信息
        displayUserInfo(result.userInfo);
      } else {
        // 用户未登录，显示登录界面
        preAuth.style.display = 'block';
        postAuth.style.display = 'none';
      }
    });
  }
  
  // 使用谷歌账号认证
  function authenticateWithGoogle() {
    // 显示加载状态
    preAuth.style.display = 'none';
    loading.style.display = 'flex';
    
    // 获取插件ID用于重定向URL
    const extensionId = chrome.runtime.id;
    
    // 定义OAuth参数
    const authParams = {
      client_id: chrome.runtime.getManifest().oauth2.client_id,
      redirect_uri: `https://${extensionId}.chromiumapp.org/`,
      response_type: 'token',
      scope: chrome.runtime.getManifest().oauth2.scopes.join(' ')
    };
    
    // 构建认证URL
    const authUrl = 'https://accounts.google.com/o/oauth2/auth?' + 
                     Object.keys(authParams).map(key => 
                       `${key}=${encodeURIComponent(authParams[key])}`
                     ).join('&');
    
    // 启动认证流程
    chrome.identity.launchWebAuthFlow({
      url: authUrl,
      interactive: true
    }, function(redirectUrl) {
      if (chrome.runtime.lastError) {
        // 处理错误
        console.error('认证错误:', chrome.runtime.lastError);
        showErrorMessage('登录失败，请稍后再试。');
        loading.style.display = 'none';
        preAuth.style.display = 'block';
        return;
      }
      
      if (redirectUrl) {
        // 从重定向URL中提取访问令牌
        const accessToken = extractAccessToken(redirectUrl);
        if (accessToken) {
          // 使用访问令牌获取用户信息
          fetchUserInfo(accessToken);
        } else {
          showErrorMessage('无法获取访问令牌，请稍后再试。');
          loading.style.display = 'none';
          preAuth.style.display = 'block';
        }
      } else {
        showErrorMessage('认证流程被中断，请重试。');
        loading.style.display = 'none';
        preAuth.style.display = 'block';
      }
    });
  }
  
  // 从重定向URL中提取访问令牌
  function extractAccessToken(redirectUrl) {
    const match = redirectUrl.match(/[#?]access_token=([^&]*)/);
    return match ? match[1] : null;
  }
  
  // 获取用户信息
  function fetchUserInfo(accessToken) {
    fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('获取用户信息失败');
      }
      return response.json();
    })
    .then(userInfo => {
      // 保存用户信息和令牌
      chrome.storage.sync.set({
        userInfo: userInfo,
        accessToken: accessToken,
        loginTime: new Date().getTime()
      }, function() {
        // 显示用户信息
        displayUserInfo(userInfo);
        // 隐藏加载状态
        loading.style.display = 'none';
        
        // 通知父页面用户已登录
        window.parent.postMessage({
          action: 'userLoggedIn',
          userInfo: userInfo
        }, '*');
        
        // 通知background.js用户已登录
        chrome.runtime.sendMessage({
          action: 'userLoggedIn',
          userInfo: userInfo,
          accessToken: accessToken
        });
      });
    })
    .catch(error => {
      console.error('获取用户信息出错:', error);
      showErrorMessage('无法获取用户信息，请稍后再试。');
      loading.style.display = 'none';
      preAuth.style.display = 'block';
    });
  }
  
  // 显示用户信息
  function displayUserInfo(userInfo) {
    // 设置用户头像
    userAvatar.src = userInfo.picture || 'images/default-avatar.png';
    // 设置用户名
    userName.textContent = userInfo.name || '用户';
    
    // 显示用户信息区域，隐藏登录区域和加载状态
    preAuth.style.display = 'none';
    loading.style.display = 'none';
    postAuth.style.display = 'block';

    // 立即从缓存加载并显示会员信息
    chrome.storage.sync.get(['membershipInfo'], function(result) {
      if (result.membershipInfo) {
        updateMembershipDisplay(result.membershipInfo);
      } else {
        // 如果没有缓存，则显示加载状态
        showMembershipLoading();
      }
    });
    
    // 将token发送到后端进行预认证，认证成功后再获取会员信息
    chrome.storage.sync.get(['accessToken'], function(result) {
      if (result.accessToken) {
        authenticateWithBackend(result.accessToken, userInfo);
      } else {
        // 如果没有token，也尝试获取会员信息（可能用户已经在web端登录）
        setTimeout(() => {
          fetchMembershipInfo();
        }, 1000);
      }
    });
  }
  
  // 显示会员信息加载状态
  function showMembershipLoading() {
    const membershipBadge = document.querySelector('.membership-badge');
    const membershipIcon = document.querySelector('.membership-icon');
    const membershipLevel = document.querySelector('.membership-level');
    const expiryText = document.querySelector('.expiry-text');
    
    if (membershipBadge && membershipIcon && membershipLevel && expiryText) {
      membershipBadge.className = 'membership-badge loading';
      membershipIcon.className = 'fas fa-spinner fa-spin membership-icon';
      membershipLevel.textContent = '加载中...';
      expiryText.textContent = '获取会员信息中';
    }
  }
  
  // 获取会员信息（带重试机制）
  function fetchMembershipInfo() {
    chrome.runtime.sendMessage({
      action: 'proxyFetch',
      url: 'http://localhost:8080/api/subscription/active',
      method: 'GET',
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    }, function(response) {
      if (chrome.runtime.lastError || !response || !response.success) {
        console.error('获取会员信息出错:', response ? response.error : (chrome.runtime.lastError?.message || '未知错误'));
        
        // 任何获取会员信息的失败都可能意味着后端会话已失效。
        // 清除'backendAuthenticated'标志，以便在下次需要时强制重新进行token登录。
        chrome.storage.sync.remove('backendAuthenticated', function() {
          console.log('Backend authenticated flag cleared due to fetch failure.');
        });

        // 获取失败时，显示错误提示，但不改变现有会员等级
        const expiryText = document.querySelector('.expiry-text');
        if (expiryText) {
          expiryText.textContent = '服务器升级维护中...';
          expiryText.style.color = '#ef4444'; // 红色提示
        }
        return;
      }
      
      console.log('成功获取会员信息:', response.data);
      // 获取成功后，更新显示并缓存数据
      updateMembershipDisplay(response.data);
      chrome.storage.sync.set({membershipInfo: response.data});
    });
  }
  
  // 更新会员信息显示
  function updateMembershipDisplay(data) {
    const membershipBadge = document.querySelector('.membership-badge');
    const membershipIcon = document.querySelector('.membership-icon');
    const membershipLevel = document.querySelector('.membership-level');
    const expiryText = document.querySelector('.expiry-text');
    
    if (!data.authenticated) {
      return;
    }

    // 重置错误提示样式
    expiryText.style.color = '';
    
    if (data.hasActiveSubscription) {
      const subscription = data.subscription;
      const planName = subscription.planName;
      const endDate = new Date(subscription.endDate);
      const formattedEndDate = endDate.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
      
      // 根据会员类型设置样式和图标
      if (planName.toLowerCase().includes('premium')) {
        membershipBadge.className = 'membership-badge premium';
        membershipIcon.className = 'fas fa-crown membership-icon';
        membershipLevel.textContent = planName;
      } else if (planName.toLowerCase().includes('plus')) {
        membershipBadge.className = 'membership-badge plus';
        membershipIcon.className = 'fas fa-star membership-icon';
        membershipLevel.textContent = planName;
      } else {
        membershipBadge.className = 'membership-badge premium';
        membershipIcon.className = 'fas fa-crown membership-icon';
        membershipLevel.textContent = planName;
      }
      
      expiryText.textContent = `有效期至 ${formattedEndDate}`;
    } else {
      // 普通会员
      membershipBadge.className = 'membership-badge basic';
      membershipIcon.className = 'fas fa-user membership-icon';
      membershipLevel.textContent = '普通会员';
      expiryText.textContent = '升级享受更多功能';
    }
  }
  
  // 向后端发送token进行身份验证
  function authenticateWithBackend(accessToken, userInfo) {
    // 检查是否已经认证过，避免重复认证
    chrome.storage.sync.get(['backendAuthenticated'], function(result) {
      if (result.backendAuthenticated) {
        console.log('用户已在后端认证，直接获取会员信息');
        // 用户已认证，直接获取会员信息
        fetchMembershipInfo();
        return;
      }
      
      fetch('http://localhost:8080/api/auth/token-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: accessToken
        }),
        credentials: 'include' // 包含cookies
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Authentication failed with status: ' + response.status);
        }
        return response.json();
      })
      .then(data => {
        console.log('后端认证成功:', data);
        // 记录已认证状态，并保存会话信息
        chrome.storage.sync.set({
          backendAuthenticated: true,
          sessionInfo: {
            timestamp: new Date().getTime(),
            userId: data.user ? data.user.id : null
          }
        });
        
        // 认证成功后，延迟1秒获取会员信息，确保会话已完全建立
        setTimeout(() => {
          fetchMembershipInfo();
        }, 1000);
      })
      .catch(error => {
        console.error('后端认证失败:', error);
        // 在认证失败时清除状态
        chrome.storage.sync.remove('backendAuthenticated');
        
        // 认证失败时，仍然尝试获取会员信息（可能用户已经在web端登录）
        setTimeout(() => {
          fetchMembershipInfo();
        }, 2000);
      });
    });
  }
  
  // 退出登录
  function logout() {
    // 显示加载状态
    postAuth.style.display = 'none';
    loading.style.display = 'flex';
    
    // 清除存储的用户信息
    chrome.storage.sync.remove(['userInfo', 'accessToken', 'loginTime', 'backendAuthenticated', 'membershipInfo'], function() {
      // 隐藏加载状态，显示登录界面
      loading.style.display = 'none';
      preAuth.style.display = 'block';
      
      // 通知父页面用户已登出
      window.parent.postMessage({
        action: 'userLoggedOut'
      }, '*');
      
      // 通知background.js用户已登出
      chrome.runtime.sendMessage({action: 'userLoggedOut'});
    });
  }
  
  // 显示错误信息
  function showErrorMessage(message) {
    // 可以添加一个错误提示UI元素
    console.error(message);
    
    const notification = document.createElement('div');
    notification.className = 'notification error';
    notification.textContent = message;
    notification.style.position = 'fixed';
    notification.style.bottom = '20px';
    notification.style.left = '50%';
    notification.style.transform = 'translateX(-50%)';
    notification.style.backgroundColor = '#f44336';
    notification.style.color = 'white';
    notification.style.padding = '12px 20px';
    notification.style.borderRadius = '4px';
    notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
    notification.style.zIndex = '10000';
    
    document.body.appendChild(notification);
    
    // 5秒后自动关闭
    setTimeout(() => {
      notification.remove();
    }, 5000);
  }
  
  // 添加学习模式按钮点击事件
  document.addEventListener('click', function(e) {
    if (e.target && e.target.closest('#study-mode-button')) {
      // 首先验证会话是否过期
      chrome.runtime.sendMessage({action: 'validateSession'}, function(response) {
        if (response && response.isValid) {
          // 会话有效，先保存观看历史，再进入学习模式
          chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            if (tabs[0]) {
              // 使用消息传递方式获取视频信息
              chrome.tabs.sendMessage(tabs[0].id, {action: 'getVideoInfo'}, function(videoInfo) {
                console.log('从内容脚本获取视频信息:', videoInfo);
                
                if (videoInfo && videoInfo.videoId && videoInfo.videoTitle) {
                  // 使用background.js代理请求保存观看历史
                  chrome.runtime.sendMessage({
                    action: 'proxyFetch',
                    url: 'http://localhost:8080/api/history/save',
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                      videoId: videoInfo.videoId,
                      videoTitle: videoInfo.videoTitle
                    })
                  }, function(response) {
                    if (response && response.success) {
                      console.log('观看历史保存成功:', response.data);
                    } else {
                      console.error('观看历史保存失败:', response?.error || '未知错误');
                    }
                    
                    // 无论保存成功与否，都继续进入学习模式
                    // 先关闭侧边栏
                    window.parent.postMessage({action: 'toggleSidebar'}, '*');
                    // 然后切换到学习模式
                    chrome.tabs.sendMessage(tabs[0].id, {action: 'toggleStudyMode'});
                  });
                } else {
                  console.error('无法获取视频信息');
                  // 如果无法获取视频信息，仍然进入学习模式
                  window.parent.postMessage({action: 'toggleSidebar'}, '*');
                  chrome.tabs.sendMessage(tabs[0].id, {action: 'toggleStudyMode'});
                }
              });
            }
          });
        } else {
          // 会话过期，清除用户信息并显示登录面板
          logout();
          showErrorMessage('会话已过期，请重新登录');
        }
      });
    }
  });
  
  // 监听来自background.js的消息
  chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
    if (message.action === 'refreshUserInfo') {
      checkAuthState();
    } else if (message.action === 'sessionExpired') {
      // 处理会话过期
      logout();
      showErrorMessage('会话已过期，请重新登录');
    }
  });
}); 

// 切换深色模式
function toggleDarkMode(isDarkMode) {
  // 保存用户的主题偏好
  chrome.storage.sync.set({darkMode: isDarkMode});
  
  // 应用深色模式到当前页面
  if (isDarkMode) {
    document.body.classList.add('dark-mode');
  } else {
    document.body.classList.remove('dark-mode');
  }
  
  // 向当前活动标签页发送消息，更新学习模式的主题
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    if (tabs[0]) {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'toggleDarkMode',
        isDarkMode: isDarkMode
      });
    }
  });
}

// 全局变量：当前选择的视频语言
let selectedVideoLanguage = 'english'; // 默认为英语

// 初始化语言选择功能
function initLanguageSelection() {
  const languageSelector = document.getElementById('language-selector');
  const languagePanel = document.getElementById('language-panel');
  const languageBackBtn = document.getElementById('language-back-btn');
  const selectedLanguageText = document.getElementById('selected-language-text');
  const languageOptions = document.querySelectorAll('.language-option');
  const postAuth = document.getElementById('post-auth');



  // 从存储中加载已选择的语言
  chrome.storage.sync.get(['selectedVideoLanguage'], function(result) {
    if (result.selectedVideoLanguage) {
      console.log('从存储中加载语言设置:', result.selectedVideoLanguage);
      selectedVideoLanguage = result.selectedVideoLanguage;
    } else {
      console.log('存储中没有语言设置，保存默认值:', selectedVideoLanguage);
      // 如果存储中没有语言设置，保存默认值到存储中
      chrome.storage.sync.set({selectedVideoLanguage: selectedVideoLanguage});
    }
    // 无论是否从存储中获取到语言，都要更新显示
    updateLanguageDisplay();
    updateLanguageSelection();
  });

  // 点击语言选择器，显示语言选择面板
  languageSelector.addEventListener('click', function() {
    postAuth.style.display = 'none';
    languagePanel.style.display = 'block';
  });

  // 点击返回按钮，返回主面板
  languageBackBtn.addEventListener('click', function() {
    languagePanel.style.display = 'none';
    postAuth.style.display = 'block';
  });

  // 处理语言选项点击
  languageOptions.forEach(option => {
    option.addEventListener('click', function() {
      const language = this.getAttribute('data-language');
      
      console.log('用户选择语言:', language);
      
      // 更新全局变量
      selectedVideoLanguage = language;
      
      // 保存到存储
      chrome.storage.sync.set({selectedVideoLanguage: language}, function() {
        console.log('语言设置已保存到存储:', language);
        
        // 验证保存是否成功
        chrome.storage.sync.get(['selectedVideoLanguage'], function(result) {
          if (result.selectedVideoLanguage === language) {
            console.log('✅ 语言设置保存验证成功');
          } else {
            console.error('❌ 语言设置保存验证失败，期望:', language, '实际:', result.selectedVideoLanguage);
          }
        });
      });
      
      // 更新显示
      updateLanguageDisplay();
      updateLanguageSelection();
      
      // 返回主面板
      languagePanel.style.display = 'none';
      postAuth.style.display = 'block';
      
      // 通知其他组件语言已更改
      notifyLanguageChange(language);
    });
  });

  // 更新语言显示文本
  function updateLanguageDisplay() {
    const languageNames = {
      'english': '英语',
      'japanese': '日语'
    };
    
    // 更新显示，让语言名称显示在右侧
    selectedLanguageText.textContent = languageNames[selectedVideoLanguage];
  }

  // 更新语言选择状态
  function updateLanguageSelection() {
    languageOptions.forEach(option => {
      const language = option.getAttribute('data-language');
      const checkIcon = option.querySelector('.check-icon');
      
      if (language === selectedVideoLanguage) {
        option.classList.add('selected');
        checkIcon.style.display = 'block';
      } else {
        option.classList.remove('selected');
        checkIcon.style.display = 'none';
      }
    });
  }

  // 通知其他组件语言已更改
  function notifyLanguageChange(language) {
    // 向当前活动标签页发送消息
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, {
          action: 'videoLanguageChanged',
          language: language
        });
      }
    });

    // 向background.js发送消息
    chrome.runtime.sendMessage({
      action: 'videoLanguageChanged',
      language: language
    });

    console.log('视频语言已更改为:', language);
  }
}

// 获取当前选择的视频语言（供其他模块调用）
function getSelectedVideoLanguage() {
  return selectedVideoLanguage;
}