/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
  color: #333;
  background-color: transparent;
  overflow-x: hidden;
}

/* 侧边栏容器 */
.sidebar-container {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  z-index: 10002; /* 确保高于播放器的z-index (10001) */
}

/* 侧边栏内容 */
.sidebar-content {
  width: 100%;
  height: 100%;
  background-color: white;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #ddd #f5f5f5;
  position: relative; /* 为语言面板提供定位参考 */
}

.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}

/* 主内容区 */
.sidebar-body {
  flex: 1;
  padding: 30px 20px;
  padding-bottom: 180px; /* 为底部控件留出空间 */
  overflow-y: auto;
}

/* 登录前区域 */
.welcome-section {
  text-align: center;
  margin-bottom: 30px;
  margin-top: 20px;
}

.welcome-section h2 {
  font-size: 22px;
  margin-bottom: 15px;
  color: #202124;
}

.welcome-logo {
  width: 48px;
  height: 48px;
  margin-bottom: 15px;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.welcome-section p {
  color: #5f6368;
  margin-bottom: 25px;
  font-size: 15px;
}

.features {
  display: flex;
  justify-content: center;
  margin-top: 25px;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 15px;
  text-align: center;
}

.feature-item .material-icons {
  font-size: 30px;
  color: #4285f4;
  margin-bottom: 10px;
}

.feature-item span:last-child {
  font-size: 14px;
  color: #5f6368;
}

/* 登录按钮 */
.login-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80%; /* 从100%减小到80% */
  max-width: 250px; /* 设置最大宽度 */
  padding: 12px; /* 从14px减小到12px */
  margin: 25px auto; /* 从30px 0减小到25px auto，并居中 */
  border: none;
  border-radius: 4px;
  background-color: #4285f4;
  color: white;
  font-size: 14px; /* 从16px减小到14px */
  font-weight: 500;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s, box-shadow 0.2s;
}

.login-button:hover {
  background-color: #3367d6;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.login-button:active {
  background-color: #2a56c6;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.login-button .google-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  background-color: white;
  border-radius: 50%;
  padding: 2px;
  flex-shrink: 0;
}

/* 隐私提示 */
.privacy-notice {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-top: 20px;
}

.privacy-notice .material-icons {
  color: #5f6368;
  margin-right: 10px;
  font-size: 20px;
}

.privacy-notice p {
  font-size: 12px;
  color: #5f6368;
  line-height: 1.4;
}

/* 登录后用户信息 */
.user-profile {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.user-avatar-container {
  position: relative;
  margin-right: 15px;
}

.user-avatar {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #4285f4;
}

.online-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 14px;
  height: 14px;
  background-color: #34a853;
  border-radius: 50%;
  border: 2px solid white;
}

.user-info h2 {
  font-size: 20px;
  margin-bottom: 6px;
}

.user-email {
  font-size: 14px;
  color: #5f6368;
}

/* 视频语言选择区域 */
.language-selection-section {
  margin-bottom: 0; /* 移除底部间距，让选项更紧密相连 */
  border-bottom: 1px solid #f1f1f1; /* 添加与其他选项一致的分割线 */
}

.language-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0; /* 统一高度，与其他选项保持一致 */
  cursor: pointer;
  transition: background-color 0.2s;
  border-radius: 6px;
  min-height: 24px; /* 确保最小高度一致 */
}

.language-selector:hover {
  background-color: #f8f8f8;
}

.language-info {
  display: flex;
  align-items: center;
  flex: 1; /* 占据剩余空间 */
  justify-content: space-between; /* 图标在左，语言名称在右 */
}

.language-label {
  display: flex;
  align-items: center;
}

.selected-language {
  font-size: 14px;
  color: #6b7280;
  font-weight: 400;
}

.arrow-icon {
  color: #9ca3af;
  font-size: 14px;
  transition: transform 0.2s;
  margin-left: 12px; /* 增加语言名称和箭头之间的间距 */
}

.language-selector:hover .arrow-icon {
  transform: translateX(2px);
}

/* 语言选择面板 */
.language-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: white;
  z-index: 10020; /* 确保在所有其他元素之上 */
  padding: 20px;
  overflow-y: auto;
}

.panel-header {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f1f1f1;
}

.back-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  margin-right: 15px;
  border-radius: 50%;
  transition: background-color 0.2s;
  color: #6b7280;
}

.back-button:hover {
  background-color: #f3f4f6;
}

.back-button i {
  font-size: 16px;
}

.panel-header h3 {
  font-size: 18px;
  font-weight: 500;
  color: #1f2937;
  margin: 0;
}

.language-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.language-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 12px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.language-option-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.flag-icon {
  font-size: 20px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flag-image {
  width: 20px;
  height: 15px;
  object-fit: cover;
  border-radius: 2px;
}

.language-option:hover {
  background-color: #f8fafc;
  border-color: #e2e8f0;
}

.language-option.selected {
  background-color: #eff6ff;
  border-color: #3b82f6;
}

.language-option span {
  font-size: 15px;
  font-weight: 400;
}

.check-icon {
  color: #10b981;
  font-size: 16px;
}

/* 账号选项 */
.account-options {
  margin-bottom: 30px;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 16px 0; /* 统一高度 */
  cursor: pointer;
  border-bottom: 1px solid #f1f1f1;
  transition: background-color 0.2s;
  text-decoration: none;
  color: inherit;
  min-height: 24px; /* 确保最小高度一致 */
}

.option-item:hover {
  background-color: #f8f8f8;
}

/* 菜单图标样式 */
.menu-icon {
  margin-right: 15px;
  font-size: 22px;
  width: 25px;
  text-align: center;
}

/* 图标颜色 */
.red-icon {
  color: #ef4444;
}

.indigo-icon {
  color: #6366f1;
}

.blue-icon {
  color: #3b82f6;
}

.cyan-icon {
  color: #06b6d4;
}

.green-icon {
  color: #10b981;
}

.orange-icon {
  color: #f97316;
}

.amber-icon {
  color: #f59e0b;
}

.purple-icon {
  color: #8b5cf6;
}

/* 退出登录按钮 */
.logout-section {
  margin-top: 0;
  margin-bottom: 20px;
  padding: 0 20px;
}

.logout-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50%; /* 减少到原来的一半 */
  max-width: 150px; /* 限制最大宽度 */
  margin: 0 auto; /* 水平居中 */
  padding: 8px 10px; /* 减少内边距高度 */
  border: none;
  border-radius: 4px;
  background-color: #f44336;
  color: white;
  font-size: 14px; /* 减小字体大小 */
  cursor: pointer;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background-color: #d32f2f;
}

.logout-button .material-icons {
  margin-right: 10px;
  font-size: 20px;
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 50px 20px;
}

.loader {
  margin-bottom: 25px;
}

.spinner {
  width: 60px;
  height: 60px;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

.spinner .path {
  stroke: #4285f4;
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

.loading-section p {
  margin-bottom: 10px;
  font-size: 16px;
}

.loading-section .small-text {
  font-size: 13px;
  color: #5f6368;
}

/* 主题切换开关样式 */
.theme-toggle-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 0;
  padding: 0 20px 10px 20px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.theme-toggle-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 8px;
}

.theme-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  font-size: 18px;
}

.light-icon {
  color: #f59e0b;
}

.dark-icon {
  color: #6366f1;
}

.theme-toggle-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 30px;
  margin: 0 10px;
}

.theme-toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e5e7eb;
  transition: .4s;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
}

.slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
  background-color: #6366f1;
}

input:focus + .slider {
  box-shadow: 0 0 1px #6366f1;
}

input:checked + .slider:before {
  transform: translateX(30px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.theme-label {
  font-size: 14px;
  color: #4b5563;
  font-weight: 500;
}

/* 深色模式样式 */
body.dark-mode {
  color: #e5e7eb;
  background-color: #1f2937;
}

.dark-mode .sidebar-content {
  background-color: #111827;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
}

.dark-mode .sidebar-content::-webkit-scrollbar-track {
  background: #374151;
}

.dark-mode .sidebar-content::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

.dark-mode .welcome-section h2 {
  color: #f9fafb;
}

.dark-mode .welcome-section p,
.dark-mode .feature-item span:last-child,
.dark-mode .user-email,
.dark-mode .privacy-notice p,
.dark-mode .privacy-notice .material-icons,
.dark-mode .loading-section .small-text {
  color: #9ca3af;
}

.dark-mode .option-item {
  border-bottom: 1px solid #374151;
}

.dark-mode .option-item:hover {
  background-color: #374151;
}

.dark-mode .theme-toggle-section {
  /* Removed the background-color */
}

.dark-mode .theme-label {
  color: #d1d5db;
}

/* 暗色模式下的语言选择样式 */
.dark-mode .language-selection-section {
  border-bottom: 1px solid #374151; /* 深色模式下的分割线，与其他选项保持一致 */
}

.dark-mode .language-selector:hover {
  background-color: #374151;
}

.dark-mode .language-panel {
  background-color: #111827;
}

.dark-mode .panel-header {
  border-bottom-color: #374151;
}

.dark-mode .panel-header h3 {
  color: #f9fafb;
}

.dark-mode .back-button {
  color: #9ca3af;
}

.dark-mode .back-button:hover {
  background-color: #374151;
}

.dark-mode .language-option:hover {
  background-color: #374151;
  border-color: #4b5563;
}

.dark-mode .language-option.selected {
  background-color: #1e3a8a;
  border-color: #3b82f6;
}

.dark-mode .language-option span {
  color: #f9fafb;
}

.dark-mode .selected-language {
  color: #9ca3af;
}

/* 登录后显示的区域 */
.auth-section {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 60px); /* 减去sidebar-body的padding */
  position: relative; /* 为语言面板的绝对定位提供参考 */
}

/* 底部控制区域 */
.bottom-controls {
  position: fixed;
  bottom: 0;
  left: 20px;
  right: 20px;
  width: calc(100% - 40px); /* 考虑左右padding */
  max-width: 100%;
  margin: 0 auto;
  padding: 20px 0;
  background-color: white;
  border-top: 1px solid #eee;
  z-index: 10003; /* 确保在其他元素之上 */
}

.dark-mode .bottom-controls {
  background-color: #111827;
  border-top: 1px solid #374151;
}

/* 响应式调整 */
@media (max-width: 600px) {
  .sidebar-container {
    width: 100%;
  }
}

/* 会员信息样式 */
.membership-info {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.membership-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  width: fit-content;
}

.membership-badge.premium {
  background-color: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

.membership-badge.plus {
  background-color: rgba(34, 197, 94, 0.1);
  color: #16a34a;
}

.membership-badge.basic {
  background-color: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.membership-badge.loading {
  background-color: rgba(156, 163, 175, 0.1);
  color: #9ca3af;
}

.membership-icon {
  font-size: 10px;
}

.membership-level {
  font-weight: 500;
}

.membership-expiry {
  margin-left: 2px;
}

.expiry-text {
  font-size: 10px;
  color: #6b7280;
  opacity: 0.8;
}

/* 深色模式下的会员信息样式 */
.dark-mode .membership-badge.premium {
  background-color: rgba(245, 158, 11, 0.15);
  color: #fbbf24;
}

.dark-mode .membership-badge.plus {
  background-color: rgba(34, 197, 94, 0.15);
  color: #22c55e;
}

.dark-mode .membership-badge.basic {
  background-color: rgba(156, 163, 175, 0.15);
  color: #9ca3af;
}

.dark-mode .membership-badge.loading {
  background-color: rgba(156, 163, 175, 0.15);
  color: #9ca3af;
}

.dark-mode .expiry-text {
  color: #9ca3af;
}
