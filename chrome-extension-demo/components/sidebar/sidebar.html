<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>YouTube谷歌登录</title>
  <link rel="stylesheet" href="sidebar.css">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>
<body>
  <div class="sidebar-container">
    <div class="sidebar-content">
      <!-- 主内容区 -->
      <div class="sidebar-body">
        <!-- 登录前显示 -->
        <div id="pre-auth" class="auth-section">
          <div class="welcome-section">
            <img src="../../assets/images/icon128.png" alt="Logo" class="welcome-logo">
            <p>请使用您的谷歌账号登录以获取个性化体验</p>
            <div class="features">
              <div class="feature-item">
                <span class="material-icons">verified_user</span>
                <span>安全认证</span>
              </div>
              <div class="feature-item">
                <span class="material-icons">history</span>
                <span>观看历史</span>
              </div>
              <div class="feature-item">
                <span class="material-icons">subscriptions</span>
                <span>订阅频道</span>
              </div>
            </div>
          </div>
          
          <button id="google-login" class="login-button">
            <svg class="google-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="24px" height="24px">
              <path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"/>
              <path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"/>
              <path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"/>
              <path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"/>
            </svg>
            <span>使用谷歌账号登录</span>
          </button>
          
          <div class="privacy-notice">
            <span class="material-icons">shield</span>
            <p>我们重视您的隐私，不会未经许可使用您的数据</p>
          </div>
        </div>
        
        <!-- 登录后显示 -->
        <div id="post-auth" class="auth-section" style="display: none;">
          <div class="user-profile">
            <div class="user-avatar-container">
              <img id="user-avatar" src="" alt="用户头像" class="user-avatar">
              <div class="online-status"></div>
            </div>
            <div class="user-info">
              <h2 id="user-name">用户名</h2>
              <!-- 会员信息显示区域 -->
              <div id="membership-info" class="membership-info">
                <div class="membership-badge loading">
                  <i class="fas fa-spinner fa-spin membership-icon"></i>
                  <span class="membership-level">加载中...</span>
                </div>
                <div class="membership-expiry">
                  <span class="expiry-text">获取会员信息中</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 视频语言选择区域 -->
          <div class="language-selection-section">
            <div class="language-selector" id="language-selector">
              <div class="language-info">
                <div class="language-label">
                  <i class="fas fa-language menu-icon purple-icon"></i>
                  <span>视频语言</span>
                </div>
                <span id="selected-language-text" class="selected-language">英语</span>
              </div>
              <i class="fas fa-chevron-right arrow-icon"></i>
            </div>
          </div>

          <div class="account-options">
            <a href="#" id="study-mode-button" class="option-item">
              <i class="fas fa-play menu-icon red-icon"></i>
              <span>学习模式</span>
            </a>
            <a href="#" data-target="http://localhost:8080/index.html" class="option-item web-link">
              <i class="fas fa-home menu-icon indigo-icon"></i>
              <span>网站主页</span>
            </a>
            <a href="#" data-target="http://localhost:8080/pages/profile/profile.html" class="option-item web-link">
              <i class="fas fa-user-circle menu-icon cyan-icon"></i>
              <span>个人主页</span>
            </a>
            <a href="#" data-target="http://localhost:8080/pages/dialog/dialog.html" class="option-item web-link">
              <i class="fas fa-comments menu-icon green-icon"></i>
              <span>场景对话</span>
            </a>
            <a href="#" data-target="http://localhost:8080/pages/review/review.html" class="option-item web-link">
              <i class="fas fa-book-open menu-icon orange-icon"></i>
              <span>单词复习</span>
            </a>
            <a href="#" data-target="http://localhost:8080/index.html#pricing" class="option-item web-link">
              <i class="fas fa-crown menu-icon amber-icon"></i>
              <span>会员订阅</span>
            </a>
          </div>
          
          <!-- 底部控制区域 -->
          <div class="bottom-controls">
            <!-- 退出登录按钮 -->
            <div class="logout-section">
              <button id="logout" class="logout-button">
                <span class="material-icons">exit_to_app</span>
                <span>退出登录</span>
              </button>
            </div>
            
            <!-- 主题切换开关 -->
            <div class="theme-toggle-section">
              <div class="theme-toggle-wrapper">
                <span class="theme-icon light-icon">
                  <i class="fas fa-sun"></i>
                </span>
                <label class="theme-toggle-switch">
                  <input type="checkbox" id="theme-toggle">
                  <span class="slider round"></span>
                </label>
                <span class="theme-icon dark-icon">
                  <i class="fas fa-moon"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 加载中状态 -->
        <div id="loading" class="loading-section" style="display: none;">
          <div class="loader">
            <svg class="spinner" viewBox="0 0 50 50">
              <circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="4"></circle>
            </svg>
          </div>
          <p>正在处理您的登录请求...</p>
          <p class="small-text">请稍等片刻，我们正在与谷歌服务器通信</p>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 语言选择面板 -->
  <div class="language-panel" id="language-panel" style="display: none;">
    <div class="panel-header">
      <button class="back-button" id="language-back-btn">
        <i class="fas fa-arrow-left"></i>
      </button>
      <h3>选择视频语言</h3>
    </div>
    <div class="language-options">
      <div class="language-option" data-language="english">
        <div class="language-option-content">
          <span class="flag-icon">
            <img src="../../assets/images/usa.png" alt="美国国旗" class="flag-image">
          </span>
          <span>英语</span>
        </div>
        <i class="fas fa-check check-icon" style="display: none;"></i>
      </div>
      <div class="language-option" data-language="japanese">
        <div class="language-option-content">
          <span class="flag-icon">
            <img src="../../assets/images/japan.png" alt="日本国旗" class="flag-image">
          </span>
          <span>日语</span>
        </div>
        <i class="fas fa-check check-icon" style="display: none;"></i>
      </div>
    </div>
  </div>
  
  <script src="sidebar.js"></script>
</body>
</html>
