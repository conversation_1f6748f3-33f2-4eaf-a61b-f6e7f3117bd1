// 当DOM内容加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
  // 获取DOM元素
  const googleLoginBtn = document.getElementById('google-login');
  const logoutBtn = document.getElementById('logout');
  const preAuth = document.getElementById('pre-auth');
  const postAuth = document.getElementById('post-auth');
  const loading = document.getElementById('loading');
  const userAvatar = document.getElementById('user-avatar');
  const userName = document.getElementById('user-name');
  const userEmail = document.getElementById('user-email');
  const studyModeBtn = document.getElementById('study-mode');
  
  // 检查用户是否已登录
  checkAuthState();
  
  // 添加谷歌登录按钮点击事件
  googleLoginBtn.addEventListener('click', authenticateWithGoogle);
  
  // 添加退出登录按钮点击事件
  logoutBtn.addEventListener('click', logout);
  
  // 添加学习模式按钮点击事件
  studyModeBtn.addEventListener('click', togglestudyMode);
  
  // 检查学习模式状态
  checkstudyModeState();
  
  // 检查认证状态
  function checkAuthState() {
    // 从storage中获取用户信息
    chrome.storage.sync.get(['userInfo'], function(result) {
      if (result.userInfo) {
        // 用户已登录，显示用户信息
        displayUserInfo(result.userInfo);
      } else {
        // 用户未登录，显示登录界面
        preAuth.style.display = 'block';
        postAuth.style.display = 'none';
      }
    });
  }
  
  // 使用谷歌账号认证
  function authenticateWithGoogle() {
    // 显示加载状态
    preAuth.style.display = 'none';
    loading.style.display = 'block';
    
    // 获取插件ID用于重定向URL
    const extensionId = chrome.runtime.id;
    
    // 定义OAuth参数
    const authParams = {
      client_id: chrome.runtime.getManifest().oauth2.client_id,
      redirect_uri: `https://${extensionId}.chromiumapp.org/`,
      response_type: 'token',
      scope: chrome.runtime.getManifest().oauth2.scopes.join(' ')
    };
    
    // 构建认证URL
    const authUrl = 'https://accounts.google.com/o/oauth2/auth?' + 
                     Object.keys(authParams).map(key => 
                       `${key}=${encodeURIComponent(authParams[key])}`
                     ).join('&');
    
    // 启动认证流程
    chrome.identity.launchWebAuthFlow({
      url: authUrl,
      interactive: true
    }, function(redirectUrl) {
      if (chrome.runtime.lastError) {
        // 处理错误
        console.error(chrome.runtime.lastError);
        loading.style.display = 'none';
        preAuth.style.display = 'block';
        return;
      }
      
      if (redirectUrl) {
        // 从重定向URL中提取访问令牌
        const accessToken = extractAccessToken(redirectUrl);
        if (accessToken) {
          // 使用访问令牌获取用户信息
          fetchUserInfo(accessToken);
        } else {
          loading.style.display = 'none';
          preAuth.style.display = 'block';
        }
      }
    });
  }
  
  // 从重定向URL中提取访问令牌
  function extractAccessToken(redirectUrl) {
    const match = redirectUrl.match(/[#?]access_token=([^&]*)/);
    return match ? match[1] : null;
  }
  
  // 获取用户信息
  function fetchUserInfo(accessToken) {
    fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('获取用户信息失败');
      }
      return response.json();
    })
    .then(userInfo => {
      // 保存用户信息和访问令牌
      chrome.storage.sync.set({
        userInfo: userInfo,
        accessToken: accessToken,
        loginTime: new Date().getTime()
      }, function() {
        // 显示用户信息
        displayUserInfo(userInfo);
        // 隐藏加载状态
        loading.style.display = 'none';
        
        // 预先进行后端认证
        authenticateWithBackend(accessToken, userInfo);
      });
    })
    .catch(error => {
      console.error('获取用户信息出错:', error);
      loading.style.display = 'none';
      preAuth.style.display = 'block';
    });
  }
  
  // 向后端发送token进行身份验证
  function authenticateWithBackend(accessToken, userInfo) {
    fetch('http://localhost:8080/api/auth/token-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: accessToken
      }),
      credentials: 'include' // 包含cookies
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('Authentication failed with status: ' + response.status);
      }
      return response.json();
    })
    .then(data => {
      console.log('后端认证成功:', data);
      // 记录已认证状态
      chrome.storage.sync.set({backendAuthenticated: true});
    })
    .catch(error => {
      console.error('后端认证失败:', error);
    });
  }
  
  // 显示用户信息
  function displayUserInfo(userInfo) {
    // 设置用户头像
    userAvatar.src = userInfo.picture || 'images/default-avatar.png';
    // 设置用户名
    userName.textContent = userInfo.name || '用户';
    // 设置用户邮箱
    userEmail.textContent = userInfo.email || '';
    
    // 显示用户信息区域，隐藏登录区域
    preAuth.style.display = 'none';
    postAuth.style.display = 'block';
  }
  
  // 退出登录
  function logout() {
    // 清除存储的用户信息
    chrome.storage.sync.remove('userInfo', function() {
      // 显示登录界面，隐藏用户信息
      preAuth.style.display = 'block';
      postAuth.style.display = 'none';
    });
  }
  
  // 切换学习模式
  function togglestudyMode() {
    // 获取当前标签页
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs[0] && tabs[0].url.includes('youtube.com')) {
        // 检查当前学习模式状态
        chrome.storage.sync.get(['studyModeActive'], function(result) {
          const isActive = !result.studyModeActive; // 切换状态
          
          // 更新存储状态
          chrome.storage.sync.set({studyModeActive: isActive}, function() {
            // 更新按钮外观
            updatestudyModeButton(isActive);
            
            // 发送消息到内容脚本切换学习模式
            chrome.tabs.sendMessage(tabs[0].id, {action: 'togglestudyMode'});
          });
        });
      } else {
        alert('学习模式只能在YouTube网站使用');
      }
    });
  }
  
  // 检查学习模式状态
  function checkstudyModeState() {
    chrome.storage.sync.get(['studyModeActive'], function(result) {
      updatestudyModeButton(result.studyModeActive);
    });
  }
  
  // 更新学习模式按钮外观
  function updatestudyModeButton(isActive) {
    if (isActive) {
      studyModeBtn.classList.add('active');
      studyModeBtn.innerHTML = '<i class="fas fa-compress"></i> 退出学习模式';
    } else {
      studyModeBtn.classList.remove('active');
      studyModeBtn.innerHTML = '<i class="fas fa-film"></i> 学习模式';
    }
  }
}); 