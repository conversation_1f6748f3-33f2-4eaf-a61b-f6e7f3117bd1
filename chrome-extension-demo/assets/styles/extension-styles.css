/* 播放器容器样式 - 使用与player页面相同的比例容器 */
#extension-player-container {
    max-width: 800px;
    margin: 0 auto 20px auto;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 关键:使用16:9比例容器，与player页面完全一致 */
#extension-player-wrapper {
    position: relative;
    width: 100%;
    padding-top: 56.25%; /* 16:9 aspect ratio - 与player页面相同 */
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid #e5e7eb;
    background: #000; /* 添加黑色背景避免闪烁 */
}

/* 播放器元素样式 - 与player页面iframe样式一致 */
#extension-player-wrapper iframe,
#extension-player-wrapper video,
#extension-player-wrapper [id*="player"] {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    border: none !important;
    border-radius: 6px;
}

/* 确保YouTube播放器不被其他样式覆盖 */
#extension-player-wrapper * {
    box-sizing: border-box;
}

/* Extension Styles */

/* 基础样式 */
.extension-container {
    font-family: 'Noto Sans SC', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 字幕区域样式 */
#extension-subtitle-container {
    position: fixed;
    bottom: 50px; /* 从20px改为50px，向上移动30像素 */
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    max-width: 800px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 8px;
    z-index: 9999; /* 降低z-index，确保缩略图预览(11000)可以显示在上方 */
    padding: 15px;
    box-sizing: border-box;
    color: #fff;
}

/* 学习模式下字幕容器完全透明 - 覆盖所有其他样式 */
.study-mode-active #extension-subtitle-container,
#study-mode-overlay #extension-subtitle-container {
    background-color: transparent !important;
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    backdrop-filter: none !important;
}

/* 通知样式 */
#extension-subtitle-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px 15px;
    border-radius: 4px;
    color: white;
    font-weight: 500;
    z-index: 10000; /* 降低z-index，但保持高于字幕区域 */
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    display: none;
}

/* AI转录选项区域样式 */
.transcribe-options {
    background-color: rgba(30, 41, 59, 0.95);
    border-radius: 6px;
    margin-top: 5px;
    padding: 12px;
}

.transcribe-options-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}

.language-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.language-selector label {
    font-size: 14px;
    color: #e2e8f0;
}

.language-selector select {
    background-color: #334155;
    color: #e2e8f0;
    border: 1px solid #475569;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 14px;
    outline: none;
}

.start-transcribe-btn {
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.start-transcribe-btn:hover {
    background-color: #2563eb;
}

.transcribe-btn {
    background-color: #8b5cf6;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.transcribe-btn:hover {
    background-color: #7c3aed;
}

/* 转录进度条样式 */
.transcribe-progress {
    margin-top: 5px;
    padding: 12px;
    background-color: rgba(30, 41, 59, 0.95);
    border-radius: 6px;
}

.progress-bar-container {
    height: 8px;
    background-color: #334155;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-bar {
    height: 100%;
    background-color: #3b82f6;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 14px;
    color: #e2e8f0;
    margin-bottom: 4px;
}

.progress-eta {
    font-size: 12px;
    color: #94a3b8;
}

/* 摘要模态框样式 */
#extension-summary-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10005;
    display: none;
}

.summary-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
}

.summary-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: modalAppear 0.3s ease-out;
}

@keyframes modalAppear {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.summary-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e5e7eb;
}

.summary-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
}

.summary-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
}

.summary-modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.summary-modal-body {
    padding: 24px;
}

.summary-loading {
    text-align: center;
    padding: 20px 0;
}

.loading-spinner {
    display: inline-block;
    width: 32px;
    height: 32px;
    border: 3px solid #e5e7eb;
    border-radius: 50%;
    border-top-color: #3b82f6;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.loading-text {
    font-size: 16px;
    color: #374151;
    margin-bottom: 8px;
    font-weight: 500;
}

.loading-subtext {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.5;
}

.summary-result {
    max-height: 400px;
    overflow-y: auto;
}

.summary-video-title {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e5e7eb;
}

.summary-content {
    font-size: 14px;
    line-height: 1.6;
    color: #374151;
    white-space: pre-wrap;
}