body {
  font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
  width: 320px;
  padding: 15px;
  margin: 0;
  background-color: #f5f5f5;
}

.container {
  text-align: center;
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h1 {
  color: #333;
  font-size: 20px;
  margin-bottom: 15px;
}

h2 {
  font-size: 16px;
  margin: 0 0 5px 0;
}

p {
  color: #666;
  margin-bottom: 20px;
  font-size: 14px;
}

/* 谷歌登录按钮样式 */
.login-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  width: 100%;
  max-width: 240px;
  margin: 0 auto;
}

.login-button:hover {
  background-color: #3367d6;
}

.google-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  background-color: white;
  border-radius: 50%;
  margin-right: 10px;
  position: relative;
}

.google-icon::before {
  content: "G";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #4285f4;
  font-weight: bold;
  font-size: 12px;
}

/* 用户信息区域样式 */
.user-info {
  display: flex;
  align-items: center;
  text-align: left;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 6px;
}

.avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
  border: 2px solid #4285f4;
  object-fit: cover;
}

.user-details {
  flex: 1;
}

#user-email {
  margin: 0;
  font-size: 12px;
  color: #777;
}

/* 退出按钮样式 */
.logout-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.logout-button:hover {
  background-color: #d32f2f;
}

/* 加载动画样式 */
.spinner {
  width: 40px;
  height: 40px;
  margin: 0 auto 15px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4285f4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 