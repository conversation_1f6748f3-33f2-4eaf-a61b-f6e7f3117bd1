/* 字幕加载提示样式优化 */

/* 基础样式调整 */
.subtitle-loading-hint {
  margin-bottom: 10px !important;
  padding: 8px 12px !important;
  border-radius: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  width: 100% !important;
  background: transparent !important; /* 移除背景颜色 */
  border: none !important; /* 移除边框 */
  box-shadow: none !important; /* 移除阴影 */
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  min-height: auto !important;
}

/* 隐藏状态 - 使用更高优先级确保能够隐藏 */
.subtitle-loading-hint.hidden {
  display: none !important;
}

/* 移除脉动动画 */
@keyframes pulseHint {
  0% { opacity: 1; }
  100% { opacity: 1; }
}

/* 移除光效 */
.subtitle-loading-hint::after {
  display: none !important;
}

@keyframes hintShimmer {
  0% { opacity: 0; }
  100% { opacity: 0; }
}

/* 加载动画容器 */
.subtitle-loading-hint .mr-3 {
  margin-right: 10px !important;
  position: relative !important;
  width: 14px !important;
  height: 14px !important;
  flex-shrink: 0 !important;
}

/* 加载动画圆圈 */
.subtitle-loading-hint .w-8 {
  width: 14px !important;
  height: 14px !important;
}

/* 加载动画边框 */
.subtitle-loading-hint .border-4 {
  border-width: 1.5px !important;
}

/* 文字样式 */
.subtitle-loading-hint p {
  margin: 0 !important;
  font-size: 15px !important;
  line-height: 1.4 !important;
  font-weight: 500 !important;
  letter-spacing: 0.3px !important;
}

.subtitle-loading-hint .text-xs {
  font-size: 13px !important;
  opacity: 0.85 !important;
  margin-top: 3px !important;
  font-weight: 400 !important;
}

/* 学习模式下的加载提示样式 */
.study-mode-active .subtitle-loading-hint {
  background: transparent !important; /* 移除背景颜色 */
  backdrop-filter: none !important; /* 移除模糊效果 */
  border: none !important; /* 移除边框 */
  transform: translateZ(0) !important;
}

/* 成功状态样式 */
.subtitle-loading-hint.bg-green-50 {
  background: transparent !important; /* 移除背景颜色 */
  border: none !important; /* 移除边框 */
}

.subtitle-loading-hint.bg-green-50 .border-green-200,
.subtitle-loading-hint.bg-green-50 .border-t-green-500 {
  border-color: transparent !important; /* 移除边框颜色 */
  border-top-color: transparent !important; /* 移除顶部边框颜色 */
}

/* 加载中状态样式 */
.subtitle-loading-hint.bg-blue-50 {
  background: transparent !important; /* 移除背景颜色 */
  border: none !important; /* 移除边框 */
}

.subtitle-loading-hint.bg-blue-50 .border-blue-200,
.subtitle-loading-hint.bg-blue-50 .border-t-blue-500 {
  border-color: transparent !important; /* 移除边框颜色 */
  border-top-color: transparent !important; /* 移除顶部边框颜色 */
}

/* 警告状态样式 */
.subtitle-loading-hint.bg-amber-50 {
  background: transparent !important; /* 移除背景颜色 */
  border: none !important; /* 移除边框 */
}

.subtitle-loading-hint.bg-amber-50 .border-amber-200,
.subtitle-loading-hint.bg-amber-50 .border-t-amber-500 {
  border-color: transparent !important; /* 移除边框颜色 */
  border-top-color: transparent !important; /* 移除顶部边框颜色 */
}

/* 错误状态样式 */
.subtitle-loading-hint.bg-red-50 {
  background: transparent !important; /* 移除背景颜色 */
  border: none !important; /* 移除边框 */
}

.subtitle-loading-hint.bg-red-50 .border-red-200,
.subtitle-loading-hint.bg-red-50 .border-t-red-500 {
  border-color: transparent !important; /* 移除边框颜色 */
  border-top-color: transparent !important; /* 移除顶部边框颜色 */
}

/* 深色模式 */
body.dark-mode .subtitle-loading-hint {
  background: transparent !important; /* 移除背景颜色 */
  border: none !important; /* 移除边框 */
  box-shadow: none !important; /* 移除阴影 */
}

body.dark-mode .subtitle-loading-hint p {
  color: #e2e8f0 !important;
}

body.dark-mode .subtitle-loading-hint.bg-green-50 {
  background: transparent !important; /* 移除背景颜色 */
  border: none !important; /* 移除边框 */
}

body.dark-mode .subtitle-loading-hint.bg-blue-50 {
  background: transparent !important; /* 移除背景颜色 */
  border: none !important; /* 移除边框 */
}

body.dark-mode .subtitle-loading-hint.bg-amber-50 {
  background: transparent !important; /* 移除背景颜色 */
  border: none !important; /* 移除边框 */
}

body.dark-mode .subtitle-loading-hint.bg-red-50 {
  background: transparent !important; /* 移除背景颜色 */
  border: none !important; /* 移除边框 */
}


