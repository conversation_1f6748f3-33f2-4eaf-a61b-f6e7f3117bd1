# 学习模式章节信息显示功能

## 功能概述

为学习模式的storyboard缩略图预览添加了章节信息显示功能。当用户鼠标悬浮在进度条上时，除了显示缩略图和时间戳外，还会显示当前时间点对应的章节标题。

## 功能特点

- **自动章节检测**：自动从YouTube页面获取视频章节信息
- **智能时间映射**：根据鼠标悬浮位置的时间，智能匹配对应的章节
- **优雅显示**：章节信息显示在缩略图和时间戳之间，界面简洁美观
- **缓存机制**：章节信息会被缓存30秒，避免重复获取
- **响应式设计**：支持不同屏幕尺寸和暗色主题

## 实现细节

### 1. 章节管理器 (ChapterManager)

位置：`chrome-extension-demo/modules/study-mode/study-mode-storyboard.js`

主要功能：
- `getChapters()`: 获取并缓存章节信息
- `getChapterAtTime(timeInSeconds)`: 根据时间获取对应章节
- `timeStringToSeconds(timeStr)`: 时间格式转换
- `clearCache()`: 清除缓存

### 2. UI结构修改

#### HTML结构
在 `study-mode-controls.js` 中的预览容器添加了章节信息元素：

```html
<div class="progress-preview" id="progress-preview">
  <div class="preview-thumbnail"></div>
  <div class="preview-chapter"></div>  <!-- 新增章节信息 -->
  <div class="preview-time"></div>
</div>
```

#### CSS样式
在 `study-mode-subtitle.css` 中添加了章节信息的样式：

```css
.preview-chapter {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
  font-weight: 400;
  text-align: center;
  width: 100%; /* 使用全宽度确保居中对齐 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: none; /* 默认隐藏，有章节信息时显示 */
}

.preview-time {
  font-weight: 500;
  text-align: center; /* 确保时间戳也居中对齐 */
  width: 100%; /* 使用全宽度确保居中对齐 */
}
```

### 3. 核心逻辑

#### 章节信息获取
使用用户提供的代码获取YouTube页面的章节信息：

```javascript
const uniqueChapters = new Map();

document.querySelectorAll('ytd-macro-markers-list-renderer')
  .forEach(renderer => {
    renderer.querySelectorAll('ytd-macro-markers-list-item-renderer')
      .forEach(item => {
        const title = item.querySelector('h4[title]')?.getAttribute('title') || '';
        const time = item.querySelector('#time')?.textContent.trim() || '';
        const key = `${title}-${time}`;
        if (!uniqueChapters.has(key)) {
          uniqueChapters.set(key, { title, time });
        }
      });
  });
```

#### 时间映射算法
根据当前时间找到对应的章节：

```javascript
async getChapterAtTime(timeInSeconds) {
  const chapters = await this.getChapters();
  
  if (chapters.length === 0) {
    return null;
  }

  let currentChapter = chapters[0];
  
  for (let i = 0; i < chapters.length; i++) {
    if (timeInSeconds >= chapters[i].timeInSeconds) {
      currentChapter = chapters[i];
    } else {
      break;
    }
  }

  return currentChapter;
}
```

## 使用方法

1. 进入YouTube视频页面
2. 启动学习模式
3. 将鼠标悬浮在进度条上
4. 观察缩略图预览中显示的章节信息

## 兼容性

- 支持有章节信息的YouTube视频
- 对于没有章节信息的视频，章节区域会自动隐藏
- 支持暗色主题
- 支持响应式设计

## 测试

可以使用 `test-chapter-functionality.html` 文件测试章节功能的基本逻辑。

## 样式优化

### 对齐修复
- 章节信息和时间戳现在完美居中对齐
- 使用 `width: 100%` 和 `text-align: center` 确保一致的对齐效果
- 支持长章节名的自动截断和省略号显示

### 测试页面
可以使用以下测试页面验证功能：
- `test-chapter-functionality.html` - 基本功能测试
- `test-alignment.html` - 对齐效果测试

## 注意事项

- 章节信息依赖于YouTube页面的DOM结构，如果YouTube更新页面结构可能需要相应调整
- 章节信息会缓存30秒，如果视频切换，缓存会自动清除
- 章节标题过长时会自动截断并显示省略号
- 章节信息和时间戳现在完美居中对齐，提供一致的视觉体验
