# 思维导图功能重构总结

## 重构目标
将思维导图功能的字幕数据来源从读取yt-dlp下载的字幕文件修改为从前端获取，与视频摘要功能保持一致。

## 修改内容

### 1. 后端修改 (MindMapController.java)

#### 新增导入
- 添加了处理JSON数据所需的导入：`JsonNode`, `ObjectMapper`, `ArrayList`, `List`
- 添加了文件路径处理：`Path`, `Paths`

#### 修改请求处理逻辑
- **参数验证**：从只检查`subtitleFileId`改为检查`subtitleFileId`或`subtitleContent`至少有一个不为空
- **字幕内容获取**：优先使用前端传递的`subtitleContent`，如果没有才回退到文件读取方式
- **多格式支持**：支持JSON格式、VTT格式和纯文本格式的字幕数据处理

#### 新增方法
- `processJsonSubtitleData()`: 处理前端传来的JSON格式字幕数据（从SummaryController复制）

#### 修改请求DTO
- 在`MindMapRequest`类中添加了`subtitleContent`字段及其getter/setter方法

### 2. 前端修改 (subtitle-analysis.js)

#### 修改generateMindMap方法
- 从同步方法改为异步方法：`async generateMindMap(retryCount = 0)`
- **字幕数据检查**：检查`this.core.zhSubtitles`和`this.core.jaSubtitles`是否有已加载的数据
- **重试机制**：如果没有字幕数据，尝试触发字幕加载并重试一次
- **字幕内容处理**：将字幕数组转换为纯文本格式
- **请求参数**：在发送给后端的请求中添加`subtitleContent`参数

#### 移除依赖检查
- 不再检查`this.core.subtitleFileId`是否存在
- 如果没有文件ID，使用占位符`'frontend-cache'`

### 3. Background.js修改

#### 修改generateMindMap函数
- 添加`subtitleContent`参数：`async function generateMindMap(subtitleFileId, videoUrl, subtitleContent = null)`
- 参数验证：检查`subtitleFileId`或`subtitleContent`至少有一个不为空
- 请求体构建：如果有`subtitleContent`，添加到请求体中

#### 修改消息路由
- 更新消息处理器以传递`subtitleContent`参数

## 重构优势

### 1. 一致性
- 思维导图功能现在与视频摘要功能使用相同的数据获取方式
- 统一的错误处理和用户体验

### 2. 实时性
- 直接使用前端已加载的字幕数据，无需等待文件下载
- 减少了文件I/O操作，提高响应速度

### 3. 向后兼容
- 保持了对旧的文件读取方式的支持
- 如果前端没有提供字幕内容，会自动回退到文件读取

### 4. 灵活性
- 支持多种字幕数据格式（JSON、VTT、纯文本）
- 可以处理不同来源的字幕数据

## 验证方法

### 1. 功能测试
1. 在YouTube视频页面加载字幕
2. 点击思维导图功能
3. 验证是否能正常生成思维导图
4. 检查控制台日志确认使用的是前端字幕数据

### 2. 兼容性测试
1. 测试没有前端字幕数据时的回退机制
2. 验证错误处理和用户提示

### 3. 性能测试
1. 比较重构前后的响应时间
2. 验证内存使用情况

## 注意事项

1. **数据格式**：确保前端传递的字幕数据格式正确
2. **错误处理**：保持与视频摘要功能一致的错误处理逻辑
3. **日志记录**：添加了详细的日志记录以便调试
4. **权限检查**：保持原有的Plus/Premium会员权限检查

## 文件清单

### 修改的文件
1. `src/main/java/org/example/youtubeanalysisdemo/controller/MindMapController.java`
2. `chrome-extension-demo/modules/subtitle/subtitle-analysis.js`
3. `chrome-extension-demo/background.js`

### 新增的文件
1. `MINDMAP_REFACTOR_SUMMARY.md` (本文档)

重构完成后，思维导图功能将能够直接使用前端加载的字幕数据，提供更快速、更一致的用户体验。
