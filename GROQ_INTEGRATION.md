# Groq API 转录集成说明

## 概述

本项目已成功将视频转录功能从本地Whisper模型迁移到Groq API，大幅提升了转录速度和系统响应性能。

## 主要改进

### 1. 性能提升
- **转录速度**: 从本地推理的几分钟缩短到Groq API的几秒钟
- **无需排队**: 移除了原有的排队机制，转录请求立即处理
- **资源消耗**: 不再需要本地GPU资源，降低服务器负载

### 2. 架构简化
- **移除队列机制**: 删除了`BlockingQueue`和`ExecutorService`的复杂排队逻辑
- **直接API调用**: 使用CompletableFuture直接处理异步转录请求
- **代码精简**: 转录服务代码量减少约40%

### 3. 功能完整性
- **VTT格式**: 保持完整的VTT字幕文件格式
- **多语言支持**: 支持日语、英语、中文、韩语等多种语言
- **时间戳精度**: 保持毫秒级时间戳精度
- **错误处理**: 完善的异常处理和重试机制

## 技术实现

### 核心类结构

```
├── service/
│   ├── GroqTranscriptionService.java    # 新增：Groq API集成服务
│   ├── TranscriptionService.java        # 修改：移除排队机制，使用Groq服务
│   └── TranscriptionTaskService.java    # 修改：适配新的转录流程
```

### 关键变更

#### 1. 新增GroqTranscriptionService
- 封装Groq API调用逻辑
- 处理音频文件上传和响应解析
- 生成标准VTT格式字幕文件

#### 2. 修改TranscriptionService
- 移除`@PostConstruct`和`@PreDestroy`方法
- 删除队列相关代码（`BlockingQueue`、`ExecutorService`）
- 使用`transcribeVideo`方法替代`queueTranscription`
- 简化异步处理逻辑

#### 3. 更新TranscriptionTaskService
- 直接调用`transcribeVideo`方法
- 移除队列状态管理
- 优化错误处理流程

## 配置说明

### 1. 环境配置

在`application.properties`中添加Groq API配置：

```properties
# Groq API配置
groq.api.key=your-groq-api-key-here
```

### 2. 支持的语言代码

| 语言 | 代码 | 别名 |
|------|------|------|
| 日语 | ja | japanese |
| 英语 | en | english |
| 中文 | zh | chinese |
| 韩语 | ko | korean |

### 3. API端点

- **Groq API**: `https://api.groq.com/openai/v1/audio/transcriptions`
- **模型**: `distil-whisper-large-v3-en`
- **响应格式**: `verbose_json`

## 使用方法

### 1. 直接调用转录服务

```java
@Autowired
private TranscriptionService transcriptionService;

// 异步转录视频
CompletableFuture<String> future = transcriptionService.transcribeVideo(
    videoUrl, videoId, language, subtitleFileId
);

// 获取VTT文件路径
String vttFilePath = future.get();
```

### 2. 通过任务服务管理

```java
@Autowired
private TranscriptionTaskService taskService;

// 创建转录任务
String taskId = taskService.createTranscriptionTask(
    videoUrl, language, createdBy, subtitleFileId
);

// 异步处理任务
taskService.processTranscriptionTaskAsync(taskId);

// 查询任务状态
Map<String, Object> progress = taskService.getTranscriptionProgress(taskId);
```

## 性能对比

| 指标 | 本地Whisper | Groq API | 提升幅度 |
|------|-------------|----------|----------|
| 转录速度 | 2-5分钟 | 10-30秒 | 5-10倍 |
| 资源消耗 | 高（GPU） | 低（API调用） | 显著降低 |
| 并发能力 | 单线程排队 | 多请求并行 | 显著提升 |
| 系统复杂度 | 高（队列管理） | 低（直接调用） | 显著简化 |

## 错误处理

### 1. API密钥未配置
```
错误: Groq API Key 未配置
解决: 在application.properties中配置groq.api.key
```

### 2. 音频文件问题
```
错误: 音频文件不存在或为空
解决: 检查音频下载流程和文件路径
```

### 3. API调用失败
```
错误: Groq API请求失败: 401 - Unauthorized
解决: 检查API密钥是否正确和有效
```

### 4. 响应格式错误
```
错误: Groq API响应格式错误：缺少segments数组
解决: 检查API响应格式和模型配置
```

## 测试说明

### 1. 单元测试
- `GroqTranscriptionServiceTest`: 测试核心功能
- 时间格式转换测试
- 语言代码映射测试
- JSON响应解析测试

### 2. 集成测试
- 音频文件上传测试
- VTT文件生成测试
- 错误处理测试

## 迁移指南

### 1. 从本地Whisper迁移
1. 配置Groq API密钥
2. 更新依赖的服务调用
3. 测试转录功能
4. 清理本地Whisper相关代码

### 2. 回滚方案
如需回滚到本地Whisper：
1. 恢复原有的排队机制代码
2. 重新配置Python环境
3. 更新服务调用逻辑

## 注意事项

1. **API限流**: Groq API可能有调用频率限制，需要合理控制请求频率
2. **文件大小**: 音频文件大小限制（通常25MB以内）
3. **网络依赖**: 需要稳定的网络连接到Groq API
4. **成本考虑**: API调用可能产生费用，需要监控使用量

## 后续优化

1. **缓存机制**: 对相同音频文件的转录结果进行缓存
2. **批量处理**: 支持多个音频文件的批量转录
3. **质量评估**: 添加转录质量评估和改进机制
4. **监控告警**: 添加API调用监控和异常告警 